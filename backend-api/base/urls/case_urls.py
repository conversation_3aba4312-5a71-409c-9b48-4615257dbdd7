from django.urls import path

from base.views.case_views import (
    get_list_cases,
    case_detail,
    add_case,
    delete_case,
    update_case_detail,
    update_case_status_only,
    update_case_step,
    get_list_cases_byuser,
    assigned_case_to,
    get_list_cases_byinsurance,
    get_list_cases_byprovider,
    get_list_cases_byloged,
    update_case_status,
    duplicate_case_detail,
    get_list_cases_map,
    get_case_histories,
    download_case_history,
)


urlpatterns = [
    path("", get_list_cases, name="get_list_cases"),
    path("map-list/", get_list_cases_map, name="get_list_cases_map"),
    path("coordinator/<str:pk>/", get_list_cases_byuser, name="get_list_cases_byuser"),
    path("current-logged/", get_list_cases_byloged, name="get_list_cases_byloged"),
    path(
        "insurance/<str:pk>/",
        get_list_cases_byinsurance,
        name="get_list_cases_byinsurance",
    ),
    path(
        "provider/<str:pk>/",
        get_list_cases_byprovider,
        name="get_list_cases_byprovider",
    ),
    path("detail/<str:pk>/", case_detail, name="case_detail"),
    path("history/<str:pk>/", get_case_histories, name="get_case_histories"),
    path("history/download/<str:pk>/", download_case_history, name="download_case_history"),
    path("delete/<str:pk>/", delete_case, name="delete_case"),
    path("add/", add_case, name="add_case"),
    path("update/<str:pk>/", update_case_detail, name="update_case_detail"),
    path("update-status-only/<str:pk>/", update_case_status_only, name="update_case_status_only"),
    path("update-step/<str:pk>/", update_case_step, name="update_case_step"),
    path("duplicate/<str:pk>/", duplicate_case_detail, name="duplicate_case_detail"),
    path("assigned-to/<str:pk>/", assigned_case_to, name="assigned_case_to"),
    path("update-status/", update_case_status, name="update_case_status"),
]
