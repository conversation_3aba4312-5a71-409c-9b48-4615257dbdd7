import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { downloadCaseHistory } from "../redux/actions/caseActions";
import { toast } from "react-toastify";
import Loader from "./Loader";
import Alert from "./Alert";
import Paginate from "./Paginate";

function CaseHistory({ historyData, loading, error, caseId }) {
  const dispatch = useDispatch();
  const [isDownloading, setIsDownloading] = useState(false);

  const history = historyData?.history || [];
  const page = historyData?.page || 1;
  const pages = historyData?.pages || 1;
  const count = historyData?.count || 0;

  // Handle download
  const handleDownload = async () => {
    if (!caseId) {
      toast.error("Case ID not available");
      return;
    }

    setIsDownloading(true);
    try {
      await dispatch(downloadCaseHistory(caseId));
      toast.success("History downloaded successfully!");
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Failed to download history. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  // Function to format date
  const formatDate = (dateString) => {
    if (dateString && dateString !== "") {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return dateString;
    }
  };

  // Function to get action color and icon
  const getActionStyles = (action) => {
    switch (action) {
      case "Created":
        return {
          color: "bg-[#E7F9ED] text-[#0C6735]",
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#0C9D58]" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          )
        };
      case "Modified":
        return {
          color: "bg-[#E6F4F7] text-[#0388A6]",
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#0388A6]" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          )
        };
      case "Deleted":
        return {
          color: "bg-[#FEECEB] text-[#B42318]",
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#D92D20]" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          )
        };
      default:
        return {
          color: "bg-[#F5F5F5] text-[#344054]",
          icon: (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#667085]" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          )
        };
    }
  };

  // Function to check if a field is likely to be a boolean field
  const isBooleanField = (fieldName) => {
    if (!fieldName) return false;

    const lowerFieldName = fieldName.toLowerCase();

    // Common boolean field patterns
    return (
      lowerFieldName.startsWith('is_') ||
      lowerFieldName.startsWith('has_') ||
      lowerFieldName.startsWith('can_') ||
      lowerFieldName.startsWith('should_') ||
      lowerFieldName.startsWith('allow_') ||
      lowerFieldName.includes('_is_') ||
      lowerFieldName.includes('_has_') ||
      lowerFieldName.includes('_can_') ||
      lowerFieldName.includes('enabled') ||
      lowerFieldName.includes('disabled') ||
      lowerFieldName.includes('active') ||
      lowerFieldName.includes('visible') ||
      lowerFieldName.includes('deleted') ||
      lowerFieldName.includes('completed') ||
      lowerFieldName.includes('approved') ||
      lowerFieldName.includes('confirmed') ||
      lowerFieldName.includes('verified') ||
      lowerFieldName.includes('paid') ||
      lowerFieldName === 'status'
    );
  };

  // Function to format price values
  const formatPriceIfNeeded = (value, fieldName) => {
    // Skip if not a number
    if (value === null || value === undefined || isNaN(value)) {
      return value;
    }

    // Check if the field name suggests this is a price
    const isPriceField = fieldName && (
      fieldName.toLowerCase().includes('price') ||
      fieldName.toLowerCase().includes('amount') ||
      fieldName.toLowerCase().includes('cost') ||
      fieldName.toLowerCase().includes('eur') ||
      fieldName.toLowerCase().includes('total')
    );

    // Check if the value has more than 2 decimal places
    const stringValue = String(value);
    const hasDecimalPlaces = stringValue.includes('.') &&
                            stringValue.split('.')[1].length > 2;

    if (isPriceField || hasDecimalPlaces) {
      return parseFloat(value).toFixed(2);
    }

    return value;
  };

  // Function to render changes
  const renderChanges = (changes) => {
    if (!changes || Object.keys(changes).length === 0) {
      return <div className="text-[#667085] bg-[#F9FAFB] p-4 rounded-lg text-center">No changes detected</div>;
    }

    return (
      <div className="mt-4 space-y-4">
        <div className="overflow-x-auto rounded-xl shadow-sm">
          <table className="min-w-full">
            <thead>
              <tr>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-[#667085] uppercase tracking-wider bg-[#F9FAFB] rounded-tl-lg">
                  Field
                </th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-[#667085] uppercase tracking-wider bg-[#F9FAFB]">
                  Previous Value
                </th>
                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-[#667085] uppercase tracking-wider bg-[#F9FAFB] rounded-tr-lg">
                  New Value
                </th>
              </tr>
            </thead>
            <tbody>
              {Object.keys(changes).map((key, index) => {
                const change = changes[key];
                let oldValue, newValue;

                // Check if this is a boolean field
                if (isBooleanField(change.field_name)) {
                  // For boolean fields, we'll let renderValue handle the formatting
                  oldValue = change.old;
                  newValue = change.new;
                } else {
                  // For other fields, format price values if needed
                  oldValue = formatPriceIfNeeded(change.old, change.field_name);
                  newValue = formatPriceIfNeeded(change.new, change.field_name);
                }

                return (
                  <tr key={key} className={index % 2 === 0 ? 'bg-white' : 'bg-[#F9FAFB]'}>
                    <td className="px-4 py-3 text-sm font-medium text-[#344054] break-words">
                      {change.field_name}
                    </td>
                    <td className="px-4 py-3 text-sm text-[#667085] break-words">
                      <span className="text-[#B42318]">{renderValue(oldValue)}</span>
                    </td>
                    <td className="px-4 py-3 text-sm text-[#667085] break-words">
                      <span className="text-[#0C6735]">{renderValue(newValue)}</span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Function to render values (handling objects and booleans)
  const renderValue = (value) => {
    if (value === null || value === undefined || value === "" || value === "null" || value === "undefined") {
      return <span className="text-[#667085] bg-[#F9FAFB] px-2 py-0.5 rounded-full text-xs">None</span>;
    }

    // Convert boolean values to Yes/No with enhanced detection
    // Check for boolean true values (true, "true", "True", "TRUE", 1, "1", "yes", "Yes", "YES")
    if (
      value === true ||
      value === "true" ||
      value === "True" ||
      value === "TRUE" ||
      value === 1 ||
      value === "1" ||
      (typeof value === "string" && value.toLowerCase() === "yes")
    ) {
      return <span className="text-[#0C6735] font-medium px-2 py-0.5 bg-[#E7F9ED] rounded-full text-xs">Yes</span>;
    }

    // Check for boolean false values (false, "false", "False", "FALSE", 0, "0", "no", "No", "NO")
    if (
      value === false ||
      value === "false" ||
      value === "False" ||
      value === "FALSE" ||
      value === 0 ||
      value === "0" ||
      (typeof value === "string" && value.toLowerCase() === "no")
    ) {
      return <span className="text-[#B42318] font-medium px-2 py-0.5 bg-[#FEECEB] rounded-full text-xs">No</span>;
    }

    if (typeof value === "object" && value !== null) {
      if (value.name) {
        return value.name;
      } else if (value.full_name) {
        return value.full_name;
      } else if (value.id) {
        return `ID: ${value.id}`;
      } else {
        return JSON.stringify(value);
      }
    }

    // Price formatting is now handled by formatPriceIfNeeded

    return String(value);
  };

  return (
    <div className="my-4 mx-2 bg-white shadow-sm py-5 sm:py-6 px-5 sm:px-6 rounded-xl">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
        <div className="flex items-center mb-3 sm:mb-0">
          <div className="bg-[#E6F4F7] p-2 rounded-md mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-5 h-5 text-[#0388A6]">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
          </div>
          <h2 className="text-base sm:text-lg font-medium text-[#344054]">
            Case History
          </h2>
        </div>
        <div className="flex items-center gap-3">
          {/* Download Button */}
          <button
            onClick={handleDownload}
            disabled={isDownloading || loading}
            className="flex items-center bg-[#0388A6] hover:bg-[#026e84] disabled:bg-[#94A3B8] text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 shadow-sm"
          >
            {isDownloading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Downloading...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-4 h-4 mr-2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                </svg>
                Download History
              </>
            )}
          </button>
          {/* Records count */}
          {pages > 1 && (
            <div className="text-xs sm:text-sm text-[#667085] bg-[#F9FAFB] px-3 py-1.5 rounded-full">
              Showing {history.length} of {count} records
            </div>
          )}
        </div>
      </div>

      {loading ? (
        <Loader />
      ) : error ? (
        <Alert type="error" message={error} />
      ) : history && history.length > 0 ? (
        <div>
          <div className="relative">
            {/* Timeline line - hidden on small screens */}
            <div className="absolute left-4 md:left-8 top-0 bottom-0 w-0.5 bg-[#E6F4F7] hidden sm:block"></div>

            {/* Timeline items */}
            <div className="space-y-8">
              {history.map((record) => {
                const actionStyle = getActionStyles(record.action);

                return (
                  <div key={record.id} className="relative pl-6 sm:pl-16">
                    {/* Timeline dot - smaller on mobile */}
                    <div className="absolute left-0 sm:left-6 top-0 sm:-translate-x-1/2 w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-white shadow-sm flex items-center justify-center z-10">
                      <div className={`w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-[#0388A6]`}></div>
                    </div>

                    {/* Content card */}
                    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-4 sm:p-6">
                      {/* Header */}
                      <div className="flex flex-col sm:flex-row justify-between mb-4 sm:mb-5">
                        <div className="flex flex-col sm:flex-row sm:items-center mb-3 sm:mb-0">
                          <div className="flex flex-wrap items-center gap-2 mb-2 sm:mb-0">
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${actionStyle.color} mr-1 w-fit`}>
                              <span className="mr-1.5">{actionStyle.icon}</span>
                              {record.action}
                            </span>

                            {/* Object type badge */}
                            {record.object_type && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#F2F4F7] text-[#344054] mr-1">
                                {record.object_type === 'case' ? 'Case' :
                                 record.object_type === 'patient' ? 'Patient' :
                                 record.object_type === 'assistance' ? 'Assistance' :
                                 record.object_type === 'status' ? 'Status' :
                                 record.object_type === 'file' ? 'File' :
                                 record.object_type}
                              </span>
                            )}

                            {/* Object name */}
                            {record.object_name && (
                              <span className="text-[#344054] text-xs sm:text-sm font-medium">
                                {record.object_name}
                              </span>
                            )}
                          </div>

                          <span className="text-[#667085] text-xs sm:text-sm">
                            {formatDate(record.date)}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <div className="flex items-center text-xs sm:text-sm text-[#344054]">
                            <div className="bg-[#F9FAFB] p-1 rounded-full mr-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#667085]" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <span className="font-medium">
                              {record.user?.full_name || record.user?.email || "System"}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      {record.action === "Modified" && record.changes && Object.keys(record.changes).length > 0 && (
                        renderChanges(record.changes)
                      )}

                      {record.action === "Created" && (
                        <div className="bg-[#E7F9ED] rounded-lg p-3 sm:p-4 text-xs sm:text-sm text-[#0C6735]">
                          <div className="flex items-center">
                            <div className="bg-white rounded-full p-1.5 shadow-sm mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-[#0C9D58]" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="font-medium">
                              {record.object_type === 'case' ? 'Initial case creation' :
                               record.object_type === 'patient' ? 'Patient information updated' :
                               record.object_type === 'assistance' ? 'Assistance service added' :
                               record.object_type === 'status' ? 'Status updated' :
                               record.object_type === 'file' ? `File added: ${record.data?.file_name || ''}` :
                               'Created'}
                            </p>
                          </div>
                        </div>
                      )}

                      {record.action === "Deleted" && (
                        <div className="bg-[#FEECEB] rounded-lg p-3 sm:p-4 text-xs sm:text-sm text-[#B42318]">
                          <div className="flex items-center">
                            <div className="bg-white rounded-full p-1.5 shadow-sm mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5 text-[#D92D20]" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="font-medium">
                              {record.object_type === 'case' ? 'Case was deleted' :
                               record.object_type === 'patient' ? 'Patient information removed' :
                               record.object_type === 'assistance' ? 'Assistance service removed' :
                               record.object_type === 'status' ? 'Status removed' :
                               record.object_type === 'file' ? `File removed: ${record.data?.file_name || ''}` :
                               'Deleted'}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pagination */}
          <div className="mt-10">
            <Paginate
              pages={pages}
              page={page}
              route={`?tab=History&`}
            />
          </div>
        </div>
      ) : (
        <div className="text-center py-10 sm:py-12 bg-white rounded-xl">
          <div className="bg-[#E6F4F7] w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-8 h-8 sm:w-10 sm:h-10 text-[#0388A6]">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
            </svg>
          </div>
          <h3 className="text-[#344054] font-medium text-lg sm:text-xl mb-2">No History Records</h3>
          <p className="text-[#667085] text-sm sm:text-base max-w-md mx-auto">Any changes made to this case will appear here in chronological order</p>
        </div>
      )}
    </div>
  );
}

export default CaseHistory;
