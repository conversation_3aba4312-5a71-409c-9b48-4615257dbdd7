{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_LIST_MAP_REQUEST, CASE_LIST_MAP_SUCCESS, CASE_LIST_MAP_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_STATUS_UPDATE_REQUEST, CASE_STATUS_UPDATE_SUCCESS, CASE_STATUS_UPDATE_FAIL,\n//\nCASE_STEP_UPDATE_REQUEST, CASE_STEP_UPDATE_SUCCESS, CASE_STEP_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL,\n//\nCOMMENT_CASE_ADD_REQUEST, COMMENT_CASE_ADD_SUCCESS, COMMENT_CASE_ADD_FAIL,\n//\nCOMMENT_CASE_DELETE_REQUEST, COMMENT_CASE_DELETE_SUCCESS, COMMENT_CASE_DELETE_FAIL,\n//\nCASE_ASSIGNED_UPDATE_REQUEST, CASE_ASSIGNED_UPDATE_SUCCESS, CASE_ASSIGNED_UPDATE_FAIL,\n//\nCASE_INSURANCE_LIST_REQUEST, CASE_INSURANCE_LIST_SUCCESS, CASE_INSURANCE_LIST_FAIL,\n//\nCASE_PROVIDER_LIST_REQUEST, CASE_PROVIDER_LIST_SUCCESS, CASE_PROVIDER_LIST_FAIL,\n//\nCASE_PROFILE_LIST_REQUEST, CASE_PROFILE_LIST_SUCCESS, CASE_PROFILE_LIST_FAIL,\n//\nCASE_DUPLICATE_REQUEST, CASE_DUPLICATE_SUCCESS, CASE_DUPLICATE_FAIL,\n//\nCASE_HISTORY_REQUEST, CASE_HISTORY_SUCCESS, CASE_HISTORY_FAIL\n//\n} from \"../constants/caseConstants\";\nexport const deleteCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_DELETE_REQUEST:\n      return {\n        loadingCommentCaseDelete: true\n      };\n    case COMMENT_CASE_DELETE_SUCCESS:\n      toast.success(\"This Comment has been deleted successfully\");\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: true\n      };\n    case COMMENT_CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: false,\n        errorCommentCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const duplicateCaseReducer = (state = {\n  caseDuplicate: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return {\n        loadingCaseDuplicate: true\n      };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n        caseDuplicate: action.payload.new_case\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload\n      };\n    case \"RESET_DUPLICATE_CASE\":\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListLoggedReducer = (state = {\n  casesLogged: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return {\n        loadingCasesLogged: true,\n        casesLogged: []\n      };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListProviderReducer = (state = {\n  casesProvider: []\n}, action) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return {\n        loadingCasesProvider: true,\n        casesProvider: []\n      };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListInsuranceReducer = (state = {\n  casesInsurance: []\n}, action) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return {\n        loadingCasesInsurance: true,\n        casesInsurance: []\n      };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return {\n        loadingCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return {\n        loadingCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const commentCaseListReducer = (state = {\n  comments: []\n}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return {\n        loadingCommentCase: true,\n        comments: []\n      };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return {\n        loadingCommentCase: false,\n        errorCommentCase: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListCoordinatorReducer = (state = {\n  casesCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCasesCoordinator: true,\n        casesCoordinator: []\n      };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseStatusReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_STATUS_UPDATE_REQUEST:\n      return {\n        loadingCaseStatusUpdate: true\n      };\n    case CASE_STATUS_UPDATE_SUCCESS:\n      toast.success(\"Case status has been updated successfully.\");\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: true\n      };\n    case CASE_STATUS_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: false,\n        errorCaseStatusUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListMapReducer = (state = {\n  casesMap: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_MAP_REQUEST:\n      return {\n        loadingCasesMap: true,\n        casesMap: []\n      };\n    case CASE_LIST_MAP_SUCCESS:\n      return {\n        loadingCasesMap: false,\n        casesMap: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_MAP_FAIL:\n      return {\n        loadingCasesMap: false,\n        errorCasesMap: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseHistoryReducer = (state = {\n  history: []\n}, action) => {\n  switch (action.type) {\n    case CASE_HISTORY_REQUEST:\n      return {\n        loadingHistory: true,\n        history: []\n      };\n    case CASE_HISTORY_SUCCESS:\n      return {\n        loadingHistory: false,\n        history: action.payload.history,\n        page: action.payload.page,\n        pages: action.payload.pages,\n        count: action.payload.count\n      };\n    case CASE_HISTORY_FAIL:\n      return {\n        loadingHistory: false,\n        errorHistory: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_LIST_MAP_REQUEST", "CASE_LIST_MAP_SUCCESS", "CASE_LIST_MAP_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_STATUS_UPDATE_REQUEST", "CASE_STATUS_UPDATE_SUCCESS", "CASE_STATUS_UPDATE_FAIL", "CASE_STEP_UPDATE_REQUEST", "CASE_STEP_UPDATE_SUCCESS", "CASE_STEP_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "COMMENT_CASE_DELETE_REQUEST", "COMMENT_CASE_DELETE_SUCCESS", "COMMENT_CASE_DELETE_FAIL", "CASE_ASSIGNED_UPDATE_REQUEST", "CASE_ASSIGNED_UPDATE_SUCCESS", "CASE_ASSIGNED_UPDATE_FAIL", "CASE_INSURANCE_LIST_REQUEST", "CASE_INSURANCE_LIST_SUCCESS", "CASE_INSURANCE_LIST_FAIL", "CASE_PROVIDER_LIST_REQUEST", "CASE_PROVIDER_LIST_SUCCESS", "CASE_PROVIDER_LIST_FAIL", "CASE_PROFILE_LIST_REQUEST", "CASE_PROFILE_LIST_SUCCESS", "CASE_PROFILE_LIST_FAIL", "CASE_DUPLICATE_REQUEST", "CASE_DUPLICATE_SUCCESS", "CASE_DUPLICATE_FAIL", "CASE_HISTORY_REQUEST", "CASE_HISTORY_SUCCESS", "CASE_HISTORY_FAIL", "deleteCommentCaseReducer", "state", "action", "type", "loadingCommentCaseDelete", "success", "successCommentCaseDelete", "error", "payload", "errorCommentCaseDelete", "duplicateCaseReducer", "caseDuplicate", "loadingCaseDuplicate", "successCaseDuplicate", "new_case", "errorCaseDuplicate", "caseListLoggedReducer", "casesLogged", "loadingCasesLogged", "cases", "pages", "page", "errorCasesLogged", "caseListProviderReducer", "casesProvider", "loadingCasesProvider", "errorCasesProvider", "caseListInsuranceReducer", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "updateCaseAssignedReducer", "loadingCaseAssignedUpdate", "successCaseAssignedUpdate", "errorCaseAssignedUpdate", "createNewCommentCaseReducer", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "commentCaseListReducer", "comments", "loadingCommentCase", "errorCommentCase", "caseListCoordinatorReducer", "casesCoordinator", "loadingCasesCoordinator", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "updateCaseStatusReducer", "loadingCaseStatusUpdate", "successCaseStatusUpdate", "errorCaseStatusUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListMapReducer", "casesMap", "loadingCasesMap", "errorCasesMap", "caseListReducer", "loadingCases", "errorCases", "caseHistoryReducer", "history", "loadingHistory", "count", "errorHistory"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_LIST_MAP_REQUEST,\n  CASE_LIST_MAP_SUCCESS,\n  CASE_LIST_MAP_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_STATUS_UPDATE_REQUEST,\n  CASE_STATUS_UPDATE_SUCCESS,\n  CASE_STATUS_UPDATE_FAIL,\n  //\n  CASE_STEP_UPDATE_REQUEST,\n  CASE_STEP_UPDATE_SUCCESS,\n  CASE_STEP_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n  //\n  COMMENT_CASE_DELETE_REQUEST,\n  COMMENT_CASE_DELETE_SUCCESS,\n  COMMENT_CASE_DELETE_FAIL,\n  //\n  CASE_ASSIGNED_UPDATE_REQUEST,\n  CASE_ASSIGNED_UPDATE_SUCCESS,\n  CASE_ASSIGNED_UPDATE_FAIL,\n  //\n  CASE_INSURANCE_LIST_REQUEST,\n  CASE_INSURANCE_LIST_SUCCESS,\n  CASE_INSURANCE_LIST_FAIL,\n  //\n  CASE_PROVIDER_LIST_REQUEST,\n  CASE_PROVIDER_LIST_SUCCESS,\n  CASE_PROVIDER_LIST_FAIL,\n  //\n  CASE_PROFILE_LIST_REQUEST,\n  CASE_PROFILE_LIST_SUCCESS,\n  CASE_PROFILE_LIST_FAIL,\n  //\n  CASE_DUPLICATE_REQUEST,\n  CASE_DUPLICATE_SUCCESS,\n  CASE_DUPLICATE_FAIL,\n  //\n  CASE_HISTORY_REQUEST,\n  CASE_HISTORY_SUCCESS,\n  CASE_HISTORY_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const deleteCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_DELETE_REQUEST:\n      return { loadingCommentCaseDelete: true };\n    case COMMENT_CASE_DELETE_SUCCESS:\n      toast.success(\"This Comment has been deleted successfully\");\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: true,\n      };\n    case COMMENT_CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: false,\n        errorCommentCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const duplicateCaseReducer = (state = { caseDuplicate: {} }, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return { loadingCaseDuplicate: true };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n        caseDuplicate: action.payload.new_case,\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload,\n      };\n    case \"RESET_DUPLICATE_CASE\":\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n      };\n\n    default:\n      return state;\n  }\n};\n\nexport const caseListLoggedReducer = (state = { casesLogged: [] }, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return { loadingCasesLogged: true, casesLogged: [] };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListProviderReducer = (\n  state = { casesProvider: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return { loadingCasesProvider: true, casesProvider: [] };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListInsuranceReducer = (\n  state = { casesInsurance: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return { loadingCasesInsurance: true, casesInsurance: [] };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return { loadingCaseAssignedUpdate: true };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true,\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return { loadingCommentCaseAdd: true };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true,\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const commentCaseListReducer = (state = { comments: [] }, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return { loadingCommentCase: true, comments: [] };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return { loadingCommentCase: false, errorCommentCase: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseStatusReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_STATUS_UPDATE_REQUEST:\n      return { loadingCaseStatusUpdate: true };\n    case CASE_STATUS_UPDATE_SUCCESS:\n      toast.success(\"Case status has been updated successfully.\");\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: true,\n      };\n    case CASE_STATUS_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: false,\n        errorCaseStatusUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListMapReducer = (state = { casesMap: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_MAP_REQUEST:\n      return { loadingCasesMap: true, casesMap: [] };\n    case CASE_LIST_MAP_SUCCESS:\n      return {\n        loadingCasesMap: false,\n        casesMap: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_MAP_FAIL:\n      return { loadingCasesMap: false, errorCasesMap: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseHistoryReducer = (state = { history: [] }, action) => {\n  switch (action.type) {\n    case CASE_HISTORY_REQUEST:\n      return { loadingHistory: true, history: [] };\n    case CASE_HISTORY_SUCCESS:\n      return {\n        loadingHistory: false,\n        history: action.payload.history,\n        page: action.payload.page,\n        pages: action.payload.pages,\n        count: action.payload.count,\n      };\n    case CASE_HISTORY_FAIL:\n      return { loadingHistory: false, errorHistory: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKxB,2BAA2B;MAC9B,OAAO;QAAEyB,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAKxB,2BAA2B;MAC9BnC,KAAK,CAAC4D,OAAO,CAAC,4CAA4C,CAAC;MAC3D,OAAO;QACLD,wBAAwB,EAAE,KAAK;QAC/BE,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAKzB,wBAAwB;MAC3BpC,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,wBAAwB,EAAE,KAAK;QAC/BE,wBAAwB,EAAE,KAAK;QAC/BG,sBAAsB,EAAEP,MAAM,CAACM;MACjC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,oBAAoB,GAAGA,CAACT,KAAK,GAAG;EAAEU,aAAa,EAAE,CAAC;AAAE,CAAC,EAAET,MAAM,KAAK;EAC7E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,sBAAsB;MACzB,OAAO;QAAEkB,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKjB,sBAAsB;MACzBlD,KAAK,CAAC4D,OAAO,CAAC,6CAA6C,CAAC;MAC5D,OAAO;QACLO,oBAAoB,EAAE,KAAK;QAC3BC,oBAAoB,EAAE,IAAI;QAC1BF,aAAa,EAAET,MAAM,CAACM,OAAO,CAACM;MAChC,CAAC;IACH,KAAKlB,mBAAmB;MACtBnD,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLI,oBAAoB,EAAE,KAAK;QAC3BC,oBAAoB,EAAE,KAAK;QAC3BE,kBAAkB,EAAEb,MAAM,CAACM;MAC7B,CAAC;IACH,KAAK,sBAAsB;MACzB,OAAO;QACLI,oBAAoB,EAAE,KAAK;QAC3BC,oBAAoB,EAAE;MACxB,CAAC;IAEH;MACE,OAAOZ,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMe,qBAAqB,GAAGA,CAACf,KAAK,GAAG;EAAEgB,WAAW,EAAE;AAAG,CAAC,EAAEf,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,yBAAyB;MAC5B,OAAO;QAAE2B,kBAAkB,EAAE,IAAI;QAAED,WAAW,EAAE;MAAG,CAAC;IACtD,KAAKzB,yBAAyB;MAC5B,OAAO;QACL0B,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAEf,MAAM,CAACM,OAAO,CAACW,KAAK;QACjCC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAK5B,sBAAsB;MACzB,OAAO;QACLyB,kBAAkB,EAAE,KAAK;QACzBI,gBAAgB,EAAEpB,MAAM,CAACM;MAC3B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsB,uBAAuB,GAAGA,CACrCtB,KAAK,GAAG;EAAEuB,aAAa,EAAE;AAAG,CAAC,EAC7BtB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,0BAA0B;MAC7B,OAAO;QAAEqC,oBAAoB,EAAE,IAAI;QAAED,aAAa,EAAE;MAAG,CAAC;IAC1D,KAAKnC,0BAA0B;MAC7B,OAAO;QACLoC,oBAAoB,EAAE,KAAK;QAC3BD,aAAa,EAAEtB,MAAM,CAACM,OAAO,CAACW,KAAK;QACnCC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAK/B,uBAAuB;MAC1B,OAAO;QACLmC,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAExB,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0B,wBAAwB,GAAGA,CACtC1B,KAAK,GAAG;EAAE2B,cAAc,EAAE;AAAG,CAAC,EAC9B1B,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,2BAA2B;MAC9B,OAAO;QAAE4C,qBAAqB,EAAE,IAAI;QAAED,cAAc,EAAE;MAAG,CAAC;IAC5D,KAAK1C,2BAA2B;MAC9B,OAAO;QACL2C,qBAAqB,EAAE,KAAK;QAC5BD,cAAc,EAAE1B,MAAM,CAACM,OAAO,CAACW,KAAK;QACpCC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAKlC,wBAAwB;MAC3B,OAAO;QACL0C,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAE5B,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8B,yBAAyB,GAAGA,CAAC9B,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC/D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrB,4BAA4B;MAC/B,OAAO;QAAEkD,yBAAyB,EAAE;MAAK,CAAC;IAC5C,KAAKjD,4BAA4B;MAC/BtC,KAAK,CAAC4D,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACL2B,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE;MAC7B,CAAC;IACH,KAAKjD,yBAAyB;MAC5BvC,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLwB,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE,KAAK;QAChCC,uBAAuB,EAAEhC,MAAM,CAACM;MAClC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkC,2BAA2B,GAAGA,CAAClC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK3B,wBAAwB;MAC3B,OAAO;QAAE4D,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAK3D,wBAAwB;MAC3BhC,KAAK,CAAC4D,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACL+B,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAK3D,qBAAqB;MACxBjC,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACL4B,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAEpC,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsC,sBAAsB,GAAGA,CAACtC,KAAK,GAAG;EAAEuC,QAAQ,EAAE;AAAG,CAAC,EAAEtC,MAAM,KAAK;EAC1E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK9B,yBAAyB;MAC5B,OAAO;QAAEoE,kBAAkB,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IACnD,KAAKlE,yBAAyB;MAC5B,OAAO;QACLmE,kBAAkB,EAAE,KAAK;QACzBD,QAAQ,EAAEtC,MAAM,CAACM,OAAO,CAACgC,QAAQ;QACjCpB,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAK9C,sBAAsB;MACzB,OAAO;QAAEkE,kBAAkB,EAAE,KAAK;QAAEC,gBAAgB,EAAExC,MAAM,CAACM;MAAQ,CAAC;IACxE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0C,0BAA0B,GAAGA,CACxC1C,KAAK,GAAG;EAAE2C,gBAAgB,EAAE;AAAG,CAAC,EAChC1C,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKjC,6BAA6B;MAChC,OAAO;QAAE2E,uBAAuB,EAAE,IAAI;QAAED,gBAAgB,EAAE;MAAG,CAAC;IAChE,KAAKzE,6BAA6B;MAChC,OAAO;QACL0E,uBAAuB,EAAE,KAAK;QAC9BD,gBAAgB,EAAE1C,MAAM,CAACM,OAAO,CAACW,KAAK;QACtCC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAKjD,0BAA0B;MAC7B,OAAO;QACLyE,uBAAuB,EAAE,KAAK;QAC9BC,qBAAqB,EAAE5C,MAAM,CAACM;MAChC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8C,iBAAiB,GAAGA,CAAC9C,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK7C,mBAAmB;MACtB,OAAO;QAAE0F,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKzF,mBAAmB;MACtBd,KAAK,CAAC4D,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACL2C,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKzF,gBAAgB;MACnBf,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLwC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEhD,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkD,uBAAuB,GAAGA,CAAClD,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC7D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK1C,0BAA0B;MAC7B,OAAO;QAAE2F,uBAAuB,EAAE;MAAK,CAAC;IAC1C,KAAK1F,0BAA0B;MAC7BjB,KAAK,CAAC4D,OAAO,CAAC,4CAA4C,CAAC;MAC3D,OAAO;QACL+C,uBAAuB,EAAE,KAAK;QAC9BC,uBAAuB,EAAE;MAC3B,CAAC;IACH,KAAK1F,uBAAuB;MAC1BlB,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACL4C,uBAAuB,EAAE,KAAK;QAC9BC,uBAAuB,EAAE,KAAK;QAC9BC,qBAAqB,EAAEpD,MAAM,CAACM;MAChC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsD,iBAAiB,GAAGA,CAACtD,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKpC,mBAAmB;MACtB,OAAO;QAAEyF,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKxF,mBAAmB;MACtBvB,KAAK,CAAC4D,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLmD,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKxF,gBAAgB;MACnBxB,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLgD,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAExD,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0D,oBAAoB,GAAGA,CAAC1D,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnD,gBAAgB;MACnB,OAAO;QAAE4G,cAAc,EAAE;MAAK,CAAC;IACjC,KAAK3G,gBAAgB;MACnBR,KAAK,CAAC4D,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACLuD,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAK3G,aAAa;MAChBT,KAAK,CAAC8D,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLoD,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE5D,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8D,iBAAiB,GAAGA,CAAC9D,KAAK,GAAG;EAAE+D,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAE9D,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhD,mBAAmB;MACtB,OAAO;QAAE8G,eAAe,EAAE;MAAK,CAAC;IAClC,KAAK7G,mBAAmB;MACtB,OAAO;QACL6G,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAE9D,MAAM,CAACM;MACnB,CAAC;IACH,KAAKnD,gBAAgB;MACnB,OAAO;QACL4G,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAEjE,MAAM,CAACM;MACxB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMmE,kBAAkB,GAAGA,CAACnE,KAAK,GAAG;EAAEoE,QAAQ,EAAE;AAAG,CAAC,EAAEnE,MAAM,KAAK;EACtE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKtD,qBAAqB;MACxB,OAAO;QAAEyH,eAAe,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IAChD,KAAKvH,qBAAqB;MACxB,OAAO;QACLwH,eAAe,EAAE,KAAK;QACtBD,QAAQ,EAAEnE,MAAM,CAACM,OAAO,CAACW,KAAK;QAC9BC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAKtE,kBAAkB;MACrB,OAAO;QAAEuH,eAAe,EAAE,KAAK;QAAEC,aAAa,EAAErE,MAAM,CAACM;MAAQ,CAAC;IAClE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMuE,eAAe,GAAGA,CAACvE,KAAK,GAAG;EAAEkB,KAAK,EAAE;AAAG,CAAC,EAAEjB,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKzD,iBAAiB;MACpB,OAAO;QAAE+H,YAAY,EAAE,IAAI;QAAEtD,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKxE,iBAAiB;MACpB,OAAO;QACL8H,YAAY,EAAE,KAAK;QACnBtD,KAAK,EAAEjB,MAAM,CAACM,OAAO,CAACW,KAAK;QAC3BC,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3BC,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa;MACvB,CAAC;IACH,KAAKzE,cAAc;MACjB,OAAO;QAAE6H,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAExE,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0E,kBAAkB,GAAGA,CAAC1E,KAAK,GAAG;EAAE2E,OAAO,EAAE;AAAG,CAAC,EAAE1E,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,oBAAoB;MACvB,OAAO;QAAEgF,cAAc,EAAE,IAAI;QAAED,OAAO,EAAE;MAAG,CAAC;IAC9C,KAAK9E,oBAAoB;MACvB,OAAO;QACL+E,cAAc,EAAE,KAAK;QACrBD,OAAO,EAAE1E,MAAM,CAACM,OAAO,CAACoE,OAAO;QAC/BvD,IAAI,EAAEnB,MAAM,CAACM,OAAO,CAACa,IAAI;QACzBD,KAAK,EAAElB,MAAM,CAACM,OAAO,CAACY,KAAK;QAC3B0D,KAAK,EAAE5E,MAAM,CAACM,OAAO,CAACsE;MACxB,CAAC;IACH,KAAK/E,iBAAiB;MACpB,OAAO;QAAE8E,cAAc,EAAE,KAAK;QAAEE,YAAY,EAAE7E,MAAM,CAACM;MAAQ,CAAC;IAChE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}