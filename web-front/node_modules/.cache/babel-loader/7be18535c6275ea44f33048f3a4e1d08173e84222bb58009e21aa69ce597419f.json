{"ast": null, "code": "import{toast}from\"react-toastify\";import{CASE_LIST_REQUEST,CASE_LIST_SUCCESS,CASE_LIST_FAIL,//\nCASE_LIST_MAP_REQUEST,CASE_LIST_MAP_SUCCESS,CASE_LIST_MAP_FAIL,//\nCASE_ADD_REQUEST,CASE_ADD_SUCCESS,CASE_ADD_FAIL,//\nCASE_DETAIL_REQUEST,CASE_DETAIL_SUCCESS,CASE_DETAIL_FAIL,//\nCASE_UPDATE_REQUEST,CASE_UPDATE_SUCCESS,CASE_UPDATE_FAIL,//\nCASE_STATUS_UPDATE_REQUEST,CASE_STATUS_UPDATE_SUCCESS,CASE_STATUS_UPDATE_FAIL,//\nCASE_STEP_UPDATE_REQUEST,CASE_STEP_UPDATE_SUCCESS,CASE_STEP_UPDATE_FAIL,//\nCASE_DELETE_REQUEST,CASE_DELETE_SUCCESS,CASE_DELETE_FAIL,//\nCASE_COORDINATOR_LIST_REQUEST,CASE_COORDINATOR_LIST_SUCCESS,CASE_COORDINATOR_LIST_FAIL,//\nCOMMENT_CASE_LIST_REQUEST,COMMENT_CASE_LIST_SUCCESS,COMMENT_CASE_LIST_FAIL,//\nCOMMENT_CASE_ADD_REQUEST,COMMENT_CASE_ADD_SUCCESS,COMMENT_CASE_ADD_FAIL,//\nCOMMENT_CASE_DELETE_REQUEST,COMMENT_CASE_DELETE_SUCCESS,COMMENT_CASE_DELETE_FAIL,//\nCASE_ASSIGNED_UPDATE_REQUEST,CASE_ASSIGNED_UPDATE_SUCCESS,CASE_ASSIGNED_UPDATE_FAIL,//\nCASE_INSURANCE_LIST_REQUEST,CASE_INSURANCE_LIST_SUCCESS,CASE_INSURANCE_LIST_FAIL,//\nCASE_PROVIDER_LIST_REQUEST,CASE_PROVIDER_LIST_SUCCESS,CASE_PROVIDER_LIST_FAIL,//\nCASE_PROFILE_LIST_REQUEST,CASE_PROFILE_LIST_SUCCESS,CASE_PROFILE_LIST_FAIL,//\nCASE_DUPLICATE_REQUEST,CASE_DUPLICATE_SUCCESS,CASE_DUPLICATE_FAIL,//\nCASE_HISTORY_REQUEST,CASE_HISTORY_SUCCESS,CASE_HISTORY_FAIL//\n}from\"../constants/caseConstants\";export const deleteCommentCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case COMMENT_CASE_DELETE_REQUEST:return{loadingCommentCaseDelete:true};case COMMENT_CASE_DELETE_SUCCESS:toast.success(\"This Comment has been deleted successfully\");return{loadingCommentCaseDelete:false,successCommentCaseDelete:true};case COMMENT_CASE_DELETE_FAIL:toast.error(action.payload);return{loadingCommentCaseDelete:false,successCommentCaseDelete:false,errorCommentCaseDelete:action.payload};default:return state;}};export const duplicateCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{caseDuplicate:{}};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_DUPLICATE_REQUEST:return{loadingCaseDuplicate:true};case CASE_DUPLICATE_SUCCESS:toast.success(\"This Case has been duplicated successfully.\");return{loadingCaseDuplicate:false,successCaseDuplicate:true,caseDuplicate:action.payload.new_case};case CASE_DUPLICATE_FAIL:toast.error(action.payload);return{loadingCaseDuplicate:false,successCaseDuplicate:false,errorCaseDuplicate:action.payload};case\"RESET_DUPLICATE_CASE\":return{loadingCaseDuplicate:false,successCaseDuplicate:false};default:return state;}};export const caseListLoggedReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{casesLogged:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_PROFILE_LIST_REQUEST:return{loadingCasesLogged:true,casesLogged:[]};case CASE_PROFILE_LIST_SUCCESS:return{loadingCasesLogged:false,casesLogged:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_PROFILE_LIST_FAIL:return{loadingCasesLogged:false,errorCasesLogged:action.payload};default:return state;}};export const caseListProviderReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{casesProvider:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_PROVIDER_LIST_REQUEST:return{loadingCasesProvider:true,casesProvider:[]};case CASE_PROVIDER_LIST_SUCCESS:return{loadingCasesProvider:false,casesProvider:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_PROVIDER_LIST_FAIL:return{loadingCasesProvider:false,errorCasesProvider:action.payload};default:return state;}};export const caseListInsuranceReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{casesInsurance:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_INSURANCE_LIST_REQUEST:return{loadingCasesInsurance:true,casesInsurance:[]};case CASE_INSURANCE_LIST_SUCCESS:return{loadingCasesInsurance:false,casesInsurance:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_INSURANCE_LIST_FAIL:return{loadingCasesInsurance:false,errorCasesInsurance:action.payload};default:return state;}};export const updateCaseAssignedReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_ASSIGNED_UPDATE_REQUEST:return{loadingCaseAssignedUpdate:true};case CASE_ASSIGNED_UPDATE_SUCCESS:toast.success(\"This Case has been updated successfully.\");return{loadingCaseAssignedUpdate:false,successCaseAssignedUpdate:true};case CASE_ASSIGNED_UPDATE_FAIL:toast.error(action.payload);return{loadingCaseAssignedUpdate:false,successCaseAssignedUpdate:false,errorCaseAssignedUpdate:action.payload};default:return state;}};export const createNewCommentCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case COMMENT_CASE_ADD_REQUEST:return{loadingCommentCaseAdd:true};case COMMENT_CASE_ADD_SUCCESS:toast.success(\"This Comment has been added successfully\");return{loadingCommentCaseAdd:false,successCommentCaseAdd:true};case COMMENT_CASE_ADD_FAIL:toast.error(action.payload);return{loadingCommentCaseAdd:false,successCommentCaseAdd:false,errorCommentCaseAdd:action.payload};default:return state;}};export const commentCaseListReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{comments:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case COMMENT_CASE_LIST_REQUEST:return{loadingCommentCase:true,comments:[]};case COMMENT_CASE_LIST_SUCCESS:return{loadingCommentCase:false,comments:action.payload.comments,pages:action.payload.pages,page:action.payload.page};case COMMENT_CASE_LIST_FAIL:return{loadingCommentCase:false,errorCommentCase:action.payload};default:return state;}};export const caseListCoordinatorReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{casesCoordinator:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_COORDINATOR_LIST_REQUEST:return{loadingCasesCoordinator:true,casesCoordinator:[]};case CASE_COORDINATOR_LIST_SUCCESS:return{loadingCasesCoordinator:false,casesCoordinator:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_COORDINATOR_LIST_FAIL:return{loadingCasesCoordinator:false,errorCasesCoordinator:action.payload};default:return state;}};export const updateCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_UPDATE_REQUEST:return{loadingCaseUpdate:true};case CASE_UPDATE_SUCCESS:toast.success(\"This Case has been updated successfully.\");return{loadingCaseUpdate:false,successCaseUpdate:true};case CASE_UPDATE_FAIL:toast.error(action.payload);return{loadingCaseUpdate:false,successCaseUpdate:false,errorCaseUpdate:action.payload};default:return state;}};export const updateCaseStatusReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_STATUS_UPDATE_REQUEST:return{loadingCaseStatusUpdate:true};case CASE_STATUS_UPDATE_SUCCESS:toast.success(\"Case status has been updated successfully.\");return{loadingCaseStatusUpdate:false,successCaseStatusUpdate:true};case CASE_STATUS_UPDATE_FAIL:toast.error(action.payload);return{loadingCaseStatusUpdate:false,successCaseStatusUpdate:false,errorCaseStatusUpdate:action.payload};default:return state;}};export const updateCaseStepReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_STEP_UPDATE_REQUEST:return{loadingCaseStepUpdate:true};case CASE_STEP_UPDATE_SUCCESS:return{loadingCaseStepUpdate:false,successCaseStepUpdate:true};case CASE_STEP_UPDATE_FAIL:toast.error(action.payload);return{loadingCaseStepUpdate:false,successCaseStepUpdate:false,errorCaseStepUpdate:action.payload};default:return state;}};export const deleteCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_DELETE_REQUEST:return{loadingCaseDelete:true};case CASE_DELETE_SUCCESS:toast.success(\"This Case has been successfully deleted.\");return{loadingCaseDelete:false,successCaseDelete:true};case CASE_DELETE_FAIL:toast.error(action.payload);return{loadingCaseDelete:false,successCaseDelete:false,errorCaseDelete:action.payload};default:return state;}};export const createNewCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_ADD_REQUEST:return{loadingCaseAdd:true};case CASE_ADD_SUCCESS:toast.success(\"This Case has been added successfully\");return{loadingCaseAdd:false,successCaseAdd:true};case CASE_ADD_FAIL:toast.error(action.payload);return{loadingCaseAdd:false,successCaseAdd:false,errorCaseAdd:action.payload};default:return state;}};export const detailCaseReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{caseInfo:{}};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_DETAIL_REQUEST:return{loadingCaseInfo:true};case CASE_DETAIL_SUCCESS:return{loadingCaseInfo:false,successCaseInfo:true,caseInfo:action.payload};case CASE_DETAIL_FAIL:return{loadingCaseInfo:false,successCaseInfo:false,errorCaseInfo:action.payload};default:return state;}};export const caseListMapReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{casesMap:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_LIST_MAP_REQUEST:return{loadingCasesMap:true,casesMap:[]};case CASE_LIST_MAP_SUCCESS:return{loadingCasesMap:false,casesMap:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_LIST_MAP_FAIL:return{loadingCasesMap:false,errorCasesMap:action.payload};default:return state;}};export const caseListReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{cases:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_LIST_REQUEST:return{loadingCases:true,cases:[]};case CASE_LIST_SUCCESS:return{loadingCases:false,cases:action.payload.cases,pages:action.payload.pages,page:action.payload.page};case CASE_LIST_FAIL:return{loadingCases:false,errorCases:action.payload};default:return state;}};export const caseHistoryReducer=function(){let state=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{history:[]};let action=arguments.length>1?arguments[1]:undefined;switch(action.type){case CASE_HISTORY_REQUEST:return{loadingHistory:true,history:[]};case CASE_HISTORY_SUCCESS:return{loadingHistory:false,history:action.payload.history,page:action.payload.page,pages:action.payload.pages,count:action.payload.count};case CASE_HISTORY_FAIL:return{loadingHistory:false,errorHistory:action.payload};default:return state;}};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_LIST_MAP_REQUEST", "CASE_LIST_MAP_SUCCESS", "CASE_LIST_MAP_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_STATUS_UPDATE_REQUEST", "CASE_STATUS_UPDATE_SUCCESS", "CASE_STATUS_UPDATE_FAIL", "CASE_STEP_UPDATE_REQUEST", "CASE_STEP_UPDATE_SUCCESS", "CASE_STEP_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "COMMENT_CASE_DELETE_REQUEST", "COMMENT_CASE_DELETE_SUCCESS", "COMMENT_CASE_DELETE_FAIL", "CASE_ASSIGNED_UPDATE_REQUEST", "CASE_ASSIGNED_UPDATE_SUCCESS", "CASE_ASSIGNED_UPDATE_FAIL", "CASE_INSURANCE_LIST_REQUEST", "CASE_INSURANCE_LIST_SUCCESS", "CASE_INSURANCE_LIST_FAIL", "CASE_PROVIDER_LIST_REQUEST", "CASE_PROVIDER_LIST_SUCCESS", "CASE_PROVIDER_LIST_FAIL", "CASE_PROFILE_LIST_REQUEST", "CASE_PROFILE_LIST_SUCCESS", "CASE_PROFILE_LIST_FAIL", "CASE_DUPLICATE_REQUEST", "CASE_DUPLICATE_SUCCESS", "CASE_DUPLICATE_FAIL", "CASE_HISTORY_REQUEST", "CASE_HISTORY_SUCCESS", "CASE_HISTORY_FAIL", "deleteCommentCaseReducer", "state", "arguments", "length", "undefined", "action", "type", "loadingCommentCaseDelete", "success", "successCommentCaseDelete", "error", "payload", "errorCommentCaseDelete", "duplicateCaseReducer", "caseDuplicate", "loadingCaseDuplicate", "successCaseDuplicate", "new_case", "errorCaseDuplicate", "caseListLoggedReducer", "casesLogged", "loadingCasesLogged", "cases", "pages", "page", "errorCasesLogged", "caseListProviderReducer", "casesProvider", "loadingCasesProvider", "errorCasesProvider", "caseListInsuranceReducer", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "updateCaseAssignedReducer", "loadingCaseAssignedUpdate", "successCaseAssignedUpdate", "errorCaseAssignedUpdate", "createNewCommentCaseReducer", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "commentCaseListReducer", "comments", "loadingCommentCase", "errorCommentCase", "caseListCoordinatorReducer", "casesCoordinator", "loadingCasesCoordinator", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "updateCaseStatusReducer", "loadingCaseStatusUpdate", "successCaseStatusUpdate", "errorCaseStatusUpdate", "updateCaseStepReducer", "loadingCaseStepUpdate", "successCaseStepUpdate", "errorCaseStepUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListMapReducer", "casesMap", "loadingCasesMap", "errorCasesMap", "caseListReducer", "loadingCases", "errorCases", "caseHistoryReducer", "history", "loadingHistory", "count", "errorHistory"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_LIST_MAP_REQUEST,\n  CASE_LIST_MAP_SUCCESS,\n  CASE_LIST_MAP_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_STATUS_UPDATE_REQUEST,\n  CASE_STATUS_UPDATE_SUCCESS,\n  CASE_STATUS_UPDATE_FAIL,\n  //\n  CASE_STEP_UPDATE_REQUEST,\n  CASE_STEP_UPDATE_SUCCESS,\n  CASE_STEP_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n  //\n  COMMENT_CASE_DELETE_REQUEST,\n  COMMENT_CASE_DELETE_SUCCESS,\n  COMMENT_CASE_DELETE_FAIL,\n  //\n  CASE_ASSIGNED_UPDATE_REQUEST,\n  CASE_ASSIGNED_UPDATE_SUCCESS,\n  CASE_ASSIGNED_UPDATE_FAIL,\n  //\n  CASE_INSURANCE_LIST_REQUEST,\n  CASE_INSURANCE_LIST_SUCCESS,\n  CASE_INSURANCE_LIST_FAIL,\n  //\n  CASE_PROVIDER_LIST_REQUEST,\n  CASE_PROVIDER_LIST_SUCCESS,\n  CASE_PROVIDER_LIST_FAIL,\n  //\n  CASE_PROFILE_LIST_REQUEST,\n  CASE_PROFILE_LIST_SUCCESS,\n  CASE_PROFILE_LIST_FAIL,\n  //\n  CASE_DUPLICATE_REQUEST,\n  CASE_DUPLICATE_SUCCESS,\n  CASE_DUPLICATE_FAIL,\n  //\n  CASE_HISTORY_REQUEST,\n  CASE_HISTORY_SUCCESS,\n  CASE_HISTORY_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const deleteCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_DELETE_REQUEST:\n      return { loadingCommentCaseDelete: true };\n    case COMMENT_CASE_DELETE_SUCCESS:\n      toast.success(\"This Comment has been deleted successfully\");\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: true,\n      };\n    case COMMENT_CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseDelete: false,\n        successCommentCaseDelete: false,\n        errorCommentCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const duplicateCaseReducer = (state = { caseDuplicate: {} }, action) => {\n  switch (action.type) {\n    case CASE_DUPLICATE_REQUEST:\n      return { loadingCaseDuplicate: true };\n    case CASE_DUPLICATE_SUCCESS:\n      toast.success(\"This Case has been duplicated successfully.\");\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: true,\n        caseDuplicate: action.payload.new_case,\n      };\n    case CASE_DUPLICATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n        errorCaseDuplicate: action.payload,\n      };\n    case \"RESET_DUPLICATE_CASE\":\n      return {\n        loadingCaseDuplicate: false,\n        successCaseDuplicate: false,\n      };\n\n    default:\n      return state;\n  }\n};\n\nexport const caseListLoggedReducer = (state = { casesLogged: [] }, action) => {\n  switch (action.type) {\n    case CASE_PROFILE_LIST_REQUEST:\n      return { loadingCasesLogged: true, casesLogged: [] };\n    case CASE_PROFILE_LIST_SUCCESS:\n      return {\n        loadingCasesLogged: false,\n        casesLogged: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROFILE_LIST_FAIL:\n      return {\n        loadingCasesLogged: false,\n        errorCasesLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListProviderReducer = (\n  state = { casesProvider: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_PROVIDER_LIST_REQUEST:\n      return { loadingCasesProvider: true, casesProvider: [] };\n    case CASE_PROVIDER_LIST_SUCCESS:\n      return {\n        loadingCasesProvider: false,\n        casesProvider: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_PROVIDER_LIST_FAIL:\n      return {\n        loadingCasesProvider: false,\n        errorCasesProvider: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListInsuranceReducer = (\n  state = { casesInsurance: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_INSURANCE_LIST_REQUEST:\n      return { loadingCasesInsurance: true, casesInsurance: [] };\n    case CASE_INSURANCE_LIST_SUCCESS:\n      return {\n        loadingCasesInsurance: false,\n        casesInsurance: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_INSURANCE_LIST_FAIL:\n      return {\n        loadingCasesInsurance: false,\n        errorCasesInsurance: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseAssignedReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ASSIGNED_UPDATE_REQUEST:\n      return { loadingCaseAssignedUpdate: true };\n    case CASE_ASSIGNED_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: true,\n      };\n    case CASE_ASSIGNED_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAssignedUpdate: false,\n        successCaseAssignedUpdate: false,\n        errorCaseAssignedUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCommentCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_ADD_REQUEST:\n      return { loadingCommentCaseAdd: true };\n    case COMMENT_CASE_ADD_SUCCESS:\n      toast.success(\"This Comment has been added successfully\");\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: true,\n      };\n    case COMMENT_CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCommentCaseAdd: false,\n        successCommentCaseAdd: false,\n        errorCommentCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const commentCaseListReducer = (state = { comments: [] }, action) => {\n  switch (action.type) {\n    case COMMENT_CASE_LIST_REQUEST:\n      return { loadingCommentCase: true, comments: [] };\n    case COMMENT_CASE_LIST_SUCCESS:\n      return {\n        loadingCommentCase: false,\n        comments: action.payload.comments,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COMMENT_CASE_LIST_FAIL:\n      return { loadingCommentCase: false, errorCommentCase: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseStatusReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_STATUS_UPDATE_REQUEST:\n      return { loadingCaseStatusUpdate: true };\n    case CASE_STATUS_UPDATE_SUCCESS:\n      toast.success(\"Case status has been updated successfully.\");\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: true,\n      };\n    case CASE_STATUS_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseStatusUpdate: false,\n        successCaseStatusUpdate: false,\n        errorCaseStatusUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseStepReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_STEP_UPDATE_REQUEST:\n      return { loadingCaseStepUpdate: true };\n    case CASE_STEP_UPDATE_SUCCESS:\n      \n      return {\n        loadingCaseStepUpdate: false,\n        successCaseStepUpdate: true,\n      };\n    case CASE_STEP_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseStepUpdate: false,\n        successCaseStepUpdate: false,\n        errorCaseStepUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"This Case has been added successfully\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListMapReducer = (state = { casesMap: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_MAP_REQUEST:\n      return { loadingCasesMap: true, casesMap: [] };\n    case CASE_LIST_MAP_SUCCESS:\n      return {\n        loadingCasesMap: false,\n        casesMap: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_MAP_FAIL:\n      return { loadingCasesMap: false, errorCasesMap: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const caseHistoryReducer = (state = { history: [] }, action) => {\n  switch (action.type) {\n    case CASE_HISTORY_REQUEST:\n      return { loadingHistory: true, history: [] };\n    case CASE_HISTORY_SUCCESS:\n      return {\n        loadingHistory: false,\n        history: action.payload.history,\n        page: action.payload.page,\n        pages: action.payload.pages,\n        count: action.payload.count,\n      };\n    case CASE_HISTORY_FAIL:\n      return { loadingHistory: false, errorHistory: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,OAASA,KAAK,KAAQ,gBAAgB,CACtC,OACEC,iBAAiB,CACjBC,iBAAiB,CACjBC,cAAc,CACd;AACAC,qBAAqB,CACrBC,qBAAqB,CACrBC,kBAAkB,CAClB;AACAC,gBAAgB,CAChBC,gBAAgB,CAChBC,aAAa,CACb;AACAC,mBAAmB,CACnBC,mBAAmB,CACnBC,gBAAgB,CAChB;AACAC,mBAAmB,CACnBC,mBAAmB,CACnBC,gBAAgB,CAChB;AACAC,0BAA0B,CAC1BC,0BAA0B,CAC1BC,uBAAuB,CACvB;AACAC,wBAAwB,CACxBC,wBAAwB,CACxBC,qBAAqB,CACrB;AACAC,mBAAmB,CACnBC,mBAAmB,CACnBC,gBAAgB,CAChB;AACAC,6BAA6B,CAC7BC,6BAA6B,CAC7BC,0BAA0B,CAC1B;AACAC,yBAAyB,CACzBC,yBAAyB,CACzBC,sBAAsB,CACtB;AACAC,wBAAwB,CACxBC,wBAAwB,CACxBC,qBAAqB,CACrB;AACAC,2BAA2B,CAC3BC,2BAA2B,CAC3BC,wBAAwB,CACxB;AACAC,4BAA4B,CAC5BC,4BAA4B,CAC5BC,yBAAyB,CACzB;AACAC,2BAA2B,CAC3BC,2BAA2B,CAC3BC,wBAAwB,CACxB;AACAC,0BAA0B,CAC1BC,0BAA0B,CAC1BC,uBAAuB,CACvB;AACAC,yBAAyB,CACzBC,yBAAyB,CACzBC,sBAAsB,CACtB;AACAC,sBAAsB,CACtBC,sBAAsB,CACtBC,mBAAmB,CACnB;AACAC,oBAAoB,CACpBC,oBAAoB,CACpBC,iBACA;AAAA,KACK,4BAA4B,CAEnC,MAAO,MAAM,CAAAC,wBAAwB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACzD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAA3B,2BAA2B,CAC9B,MAAO,CAAE4B,wBAAwB,CAAE,IAAK,CAAC,CAC3C,IAAK,CAAA3B,2BAA2B,CAC9BnC,KAAK,CAAC+D,OAAO,CAAC,4CAA4C,CAAC,CAC3D,MAAO,CACLD,wBAAwB,CAAE,KAAK,CAC/BE,wBAAwB,CAAE,IAC5B,CAAC,CACH,IAAK,CAAA5B,wBAAwB,CAC3BpC,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLJ,wBAAwB,CAAE,KAAK,CAC/BE,wBAAwB,CAAE,KAAK,CAC/BG,sBAAsB,CAAEP,MAAM,CAACM,OACjC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAY,oBAAoB,CAAG,QAAAA,CAAA,CAA2C,IAA1C,CAAAZ,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEY,aAAa,CAAE,CAAC,CAAE,CAAC,IAAE,CAAAT,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACxE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAZ,sBAAsB,CACzB,MAAO,CAAEqB,oBAAoB,CAAE,IAAK,CAAC,CACvC,IAAK,CAAApB,sBAAsB,CACzBlD,KAAK,CAAC+D,OAAO,CAAC,6CAA6C,CAAC,CAC5D,MAAO,CACLO,oBAAoB,CAAE,KAAK,CAC3BC,oBAAoB,CAAE,IAAI,CAC1BF,aAAa,CAAET,MAAM,CAACM,OAAO,CAACM,QAChC,CAAC,CACH,IAAK,CAAArB,mBAAmB,CACtBnD,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLI,oBAAoB,CAAE,KAAK,CAC3BC,oBAAoB,CAAE,KAAK,CAC3BE,kBAAkB,CAAEb,MAAM,CAACM,OAC7B,CAAC,CACH,IAAK,sBAAsB,CACzB,MAAO,CACLI,oBAAoB,CAAE,KAAK,CAC3BC,oBAAoB,CAAE,KACxB,CAAC,CAEH,QACE,MAAO,CAAAf,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAkB,qBAAqB,CAAG,QAAAA,CAAA,CAAyC,IAAxC,CAAAlB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEkB,WAAW,CAAE,EAAG,CAAC,IAAE,CAAAf,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACvE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAf,yBAAyB,CAC5B,MAAO,CAAE8B,kBAAkB,CAAE,IAAI,CAAED,WAAW,CAAE,EAAG,CAAC,CACtD,IAAK,CAAA5B,yBAAyB,CAC5B,MAAO,CACL6B,kBAAkB,CAAE,KAAK,CACzBD,WAAW,CAAEf,MAAM,CAACM,OAAO,CAACW,KAAK,CACjCC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAA/B,sBAAsB,CACzB,MAAO,CACL4B,kBAAkB,CAAE,KAAK,CACzBI,gBAAgB,CAAEpB,MAAM,CAACM,OAC3B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAyB,uBAAuB,CAAG,QAAAA,CAAA,CAGlC,IAFH,CAAAzB,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEyB,aAAa,CAAE,EAAG,CAAC,IAC7B,CAAAtB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEN,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAlB,0BAA0B,CAC7B,MAAO,CAAEwC,oBAAoB,CAAE,IAAI,CAAED,aAAa,CAAE,EAAG,CAAC,CAC1D,IAAK,CAAAtC,0BAA0B,CAC7B,MAAO,CACLuC,oBAAoB,CAAE,KAAK,CAC3BD,aAAa,CAAEtB,MAAM,CAACM,OAAO,CAACW,KAAK,CACnCC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAAlC,uBAAuB,CAC1B,MAAO,CACLsC,oBAAoB,CAAE,KAAK,CAC3BC,kBAAkB,CAAExB,MAAM,CAACM,OAC7B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA6B,wBAAwB,CAAG,QAAAA,CAAA,CAGnC,IAFH,CAAA7B,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAE6B,cAAc,CAAE,EAAG,CAAC,IAC9B,CAAA1B,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEN,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAArB,2BAA2B,CAC9B,MAAO,CAAE+C,qBAAqB,CAAE,IAAI,CAAED,cAAc,CAAE,EAAG,CAAC,CAC5D,IAAK,CAAA7C,2BAA2B,CAC9B,MAAO,CACL8C,qBAAqB,CAAE,KAAK,CAC5BD,cAAc,CAAE1B,MAAM,CAACM,OAAO,CAACW,KAAK,CACpCC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAArC,wBAAwB,CAC3B,MAAO,CACL6C,qBAAqB,CAAE,KAAK,CAC5BC,mBAAmB,CAAE5B,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAiC,yBAAyB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAjC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAC1D,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAxB,4BAA4B,CAC/B,MAAO,CAAEqD,yBAAyB,CAAE,IAAK,CAAC,CAC5C,IAAK,CAAApD,4BAA4B,CAC/BtC,KAAK,CAAC+D,OAAO,CAAC,0CAA0C,CAAC,CACzD,MAAO,CACL2B,yBAAyB,CAAE,KAAK,CAChCC,yBAAyB,CAAE,IAC7B,CAAC,CACH,IAAK,CAAApD,yBAAyB,CAC5BvC,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLwB,yBAAyB,CAAE,KAAK,CAChCC,yBAAyB,CAAE,KAAK,CAChCC,uBAAuB,CAAEhC,MAAM,CAACM,OAClC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAqC,2BAA2B,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAArC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAC5D,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAA9B,wBAAwB,CAC3B,MAAO,CAAE+D,qBAAqB,CAAE,IAAK,CAAC,CACxC,IAAK,CAAA9D,wBAAwB,CAC3BhC,KAAK,CAAC+D,OAAO,CAAC,0CAA0C,CAAC,CACzD,MAAO,CACL+B,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,IACzB,CAAC,CACH,IAAK,CAAA9D,qBAAqB,CACxBjC,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACL4B,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,KAAK,CAC5BC,mBAAmB,CAAEpC,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAyC,sBAAsB,CAAG,QAAAA,CAAA,CAAsC,IAArC,CAAAzC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEyC,QAAQ,CAAE,EAAG,CAAC,IAAE,CAAAtC,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACrE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAjC,yBAAyB,CAC5B,MAAO,CAAEuE,kBAAkB,CAAE,IAAI,CAAED,QAAQ,CAAE,EAAG,CAAC,CACnD,IAAK,CAAArE,yBAAyB,CAC5B,MAAO,CACLsE,kBAAkB,CAAE,KAAK,CACzBD,QAAQ,CAAEtC,MAAM,CAACM,OAAO,CAACgC,QAAQ,CACjCpB,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAAjD,sBAAsB,CACzB,MAAO,CAAEqE,kBAAkB,CAAE,KAAK,CAAEC,gBAAgB,CAAExC,MAAM,CAACM,OAAQ,CAAC,CACxE,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA6C,0BAA0B,CAAG,QAAAA,CAAA,CAGrC,IAFH,CAAA7C,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAE6C,gBAAgB,CAAE,EAAG,CAAC,IAChC,CAAA1C,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEN,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAApC,6BAA6B,CAChC,MAAO,CAAE8E,uBAAuB,CAAE,IAAI,CAAED,gBAAgB,CAAE,EAAG,CAAC,CAChE,IAAK,CAAA5E,6BAA6B,CAChC,MAAO,CACL6E,uBAAuB,CAAE,KAAK,CAC9BD,gBAAgB,CAAE1C,MAAM,CAACM,OAAO,CAACW,KAAK,CACtCC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAApD,0BAA0B,CAC7B,MAAO,CACL4E,uBAAuB,CAAE,KAAK,CAC9BC,qBAAqB,CAAE5C,MAAM,CAACM,OAChC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAiD,iBAAiB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAjD,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAClD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAhD,mBAAmB,CACtB,MAAO,CAAE6F,iBAAiB,CAAE,IAAK,CAAC,CACpC,IAAK,CAAA5F,mBAAmB,CACtBd,KAAK,CAAC+D,OAAO,CAAC,0CAA0C,CAAC,CACzD,MAAO,CACL2C,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,IACrB,CAAC,CACH,IAAK,CAAA5F,gBAAgB,CACnBf,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLwC,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,KAAK,CACxBC,eAAe,CAAEhD,MAAM,CAACM,OAC1B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAqD,uBAAuB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAArD,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACxD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAA7C,0BAA0B,CAC7B,MAAO,CAAE8F,uBAAuB,CAAE,IAAK,CAAC,CAC1C,IAAK,CAAA7F,0BAA0B,CAC7BjB,KAAK,CAAC+D,OAAO,CAAC,4CAA4C,CAAC,CAC3D,MAAO,CACL+C,uBAAuB,CAAE,KAAK,CAC9BC,uBAAuB,CAAE,IAC3B,CAAC,CACH,IAAK,CAAA7F,uBAAuB,CAC1BlB,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACL4C,uBAAuB,CAAE,KAAK,CAC9BC,uBAAuB,CAAE,KAAK,CAC9BC,qBAAqB,CAAEpD,MAAM,CAACM,OAChC,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAyD,qBAAqB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAzD,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACtD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAA1C,wBAAwB,CAC3B,MAAO,CAAE+F,qBAAqB,CAAE,IAAK,CAAC,CACxC,IAAK,CAAA9F,wBAAwB,CAE3B,MAAO,CACL8F,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,IACzB,CAAC,CACH,IAAK,CAAA9F,qBAAqB,CACxBrB,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLgD,qBAAqB,CAAE,KAAK,CAC5BC,qBAAqB,CAAE,KAAK,CAC5BC,mBAAmB,CAAExD,MAAM,CAACM,OAC9B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA6D,iBAAiB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAA7D,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAClD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAvC,mBAAmB,CACtB,MAAO,CAAEgG,iBAAiB,CAAE,IAAK,CAAC,CACpC,IAAK,CAAA/F,mBAAmB,CACtBvB,KAAK,CAAC+D,OAAO,CAAC,0CAA0C,CAAC,CACzD,MAAO,CACLuD,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,IACrB,CAAC,CACH,IAAK,CAAA/F,gBAAgB,CACnBxB,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLoD,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,KAAK,CACxBC,eAAe,CAAE5D,MAAM,CAACM,OAC1B,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAiE,oBAAoB,CAAG,QAAAA,CAAA,CAAwB,IAAvB,CAAAjE,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAAE,CAAAG,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACrD,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAtD,gBAAgB,CACnB,MAAO,CAAEmH,cAAc,CAAE,IAAK,CAAC,CACjC,IAAK,CAAAlH,gBAAgB,CACnBR,KAAK,CAAC+D,OAAO,CAAC,uCAAuC,CAAC,CACtD,MAAO,CACL2D,cAAc,CAAE,KAAK,CACrBC,cAAc,CAAE,IAClB,CAAC,CACH,IAAK,CAAAlH,aAAa,CAChBT,KAAK,CAACiE,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC,CAC3B,MAAO,CACLwD,cAAc,CAAE,KAAK,CACrBC,cAAc,CAAE,KAAK,CACrBC,YAAY,CAAEhE,MAAM,CAACM,OACvB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAqE,iBAAiB,CAAG,QAAAA,CAAA,CAAsC,IAArC,CAAArE,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEqE,QAAQ,CAAE,CAAC,CAAE,CAAC,IAAE,CAAAlE,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAChE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAnD,mBAAmB,CACtB,MAAO,CAAEqH,eAAe,CAAE,IAAK,CAAC,CAClC,IAAK,CAAApH,mBAAmB,CACtB,MAAO,CACLoH,eAAe,CAAE,KAAK,CACtBC,eAAe,CAAE,IAAI,CACrBF,QAAQ,CAAElE,MAAM,CAACM,OACnB,CAAC,CACH,IAAK,CAAAtD,gBAAgB,CACnB,MAAO,CACLmH,eAAe,CAAE,KAAK,CACtBC,eAAe,CAAE,KAAK,CACtBC,aAAa,CAAErE,MAAM,CAACM,OACxB,CAAC,CACH,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA0E,kBAAkB,CAAG,QAAAA,CAAA,CAAsC,IAArC,CAAA1E,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAE0E,QAAQ,CAAE,EAAG,CAAC,IAAE,CAAAvE,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CACjE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAzD,qBAAqB,CACxB,MAAO,CAAEgI,eAAe,CAAE,IAAI,CAAED,QAAQ,CAAE,EAAG,CAAC,CAChD,IAAK,CAAA9H,qBAAqB,CACxB,MAAO,CACL+H,eAAe,CAAE,KAAK,CACtBD,QAAQ,CAAEvE,MAAM,CAACM,OAAO,CAACW,KAAK,CAC9BC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAAzE,kBAAkB,CACrB,MAAO,CAAE8H,eAAe,CAAE,KAAK,CAAEC,aAAa,CAAEzE,MAAM,CAACM,OAAQ,CAAC,CAClE,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAA8E,eAAe,CAAG,QAAAA,CAAA,CAAmC,IAAlC,CAAA9E,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEoB,KAAK,CAAE,EAAG,CAAC,IAAE,CAAAjB,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAC3D,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAA5D,iBAAiB,CACpB,MAAO,CAAEsI,YAAY,CAAE,IAAI,CAAE1D,KAAK,CAAE,EAAG,CAAC,CAC1C,IAAK,CAAA3E,iBAAiB,CACpB,MAAO,CACLqI,YAAY,CAAE,KAAK,CACnB1D,KAAK,CAAEjB,MAAM,CAACM,OAAO,CAACW,KAAK,CAC3BC,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3BC,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IACvB,CAAC,CACH,IAAK,CAAA5E,cAAc,CACjB,MAAO,CAAEoI,YAAY,CAAE,KAAK,CAAEC,UAAU,CAAE5E,MAAM,CAACM,OAAQ,CAAC,CAC5D,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC,CAED,MAAO,MAAM,CAAAiF,kBAAkB,CAAG,QAAAA,CAAA,CAAqC,IAApC,CAAAjF,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAEiF,OAAO,CAAE,EAAG,CAAC,IAAE,CAAA9E,MAAM,CAAAH,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAChE,OAAQC,MAAM,CAACC,IAAI,EACjB,IAAK,CAAAT,oBAAoB,CACvB,MAAO,CAAEuF,cAAc,CAAE,IAAI,CAAED,OAAO,CAAE,EAAG,CAAC,CAC9C,IAAK,CAAArF,oBAAoB,CACvB,MAAO,CACLsF,cAAc,CAAE,KAAK,CACrBD,OAAO,CAAE9E,MAAM,CAACM,OAAO,CAACwE,OAAO,CAC/B3D,IAAI,CAAEnB,MAAM,CAACM,OAAO,CAACa,IAAI,CACzBD,KAAK,CAAElB,MAAM,CAACM,OAAO,CAACY,KAAK,CAC3B8D,KAAK,CAAEhF,MAAM,CAACM,OAAO,CAAC0E,KACxB,CAAC,CACH,IAAK,CAAAtF,iBAAiB,CACpB,MAAO,CAAEqF,cAAc,CAAE,KAAK,CAAEE,YAAY,CAAEjF,MAAM,CAACM,OAAQ,CAAC,CAChE,QACE,MAAO,CAAAV,KAAK,CAChB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}