{"ast": null, "code": "import axios from \"../../axios\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_LIST_MAP_REQUEST, CASE_LIST_MAP_SUCCESS, CASE_LIST_MAP_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_STATUS_UPDATE_REQUEST, CASE_STATUS_UPDATE_SUCCESS, CASE_STATUS_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL,\n//\nCOMMENT_CASE_ADD_REQUEST, COMMENT_CASE_ADD_SUCCESS, COMMENT_CASE_ADD_FAIL,\n//\nCOMMENT_CASE_DELETE_REQUEST, COMMENT_CASE_DELETE_SUCCESS, COMMENT_CASE_DELETE_FAIL,\n//\nCASE_ASSIGNED_UPDATE_REQUEST, CASE_ASSIGNED_UPDATE_SUCCESS, CASE_ASSIGNED_UPDATE_FAIL,\n//\nCASE_INSURANCE_LIST_REQUEST, CASE_INSURANCE_LIST_SUCCESS, CASE_INSURANCE_LIST_FAIL,\n//\nCASE_PROVIDER_LIST_REQUEST, CASE_PROVIDER_LIST_SUCCESS, CASE_PROVIDER_LIST_FAIL,\n//\nCASE_PROFILE_LIST_REQUEST, CASE_PROFILE_LIST_SUCCESS, CASE_PROFILE_LIST_FAIL,\n//\nCASE_DUPLICATE_REQUEST, CASE_DUPLICATE_SUCCESS, CASE_DUPLICATE_FAIL,\n//\nCASE_HISTORY_REQUEST, CASE_HISTORY_SUCCESS, CASE_HISTORY_FAIL\n//\n} from \"../constants/caseConstants\";\n\n// case add\nexport const deleteCommentCase = commentId => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COMMENT_CASE_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/comments/delete/${commentId}/`, config);\n    dispatch({\n      type: COMMENT_CASE_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COMMENT_CASE_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// duplicate case\nexport const duplicateCase = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DUPLICATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/cases/duplicate/${id}/`, {}, config);\n    dispatch({\n      type: CASE_DUPLICATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DUPLICATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases by logged\nexport const casesListLogged = (page, filter = \"\") => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_PROFILE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/current-logged/?page=${page}&status=${filter}`, config);\n    dispatch({\n      type: CASE_PROFILE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_PROFILE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases by provider\nexport const casesListProvider = (page, filter = \"\", provider) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_PROVIDER_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/provider/${provider}/?page=${page}&status=${filter}`, config);\n    dispatch({\n      type: CASE_PROVIDER_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_PROVIDER_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases by insurance\nexport const casesListInsurance = (page, filter = \"\", insurance) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_INSURANCE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/insurance/${insurance}/?page=${page}&status=${filter}`, config);\n    dispatch({\n      type: CASE_INSURANCE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_INSURANCE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateAssignedCase = (id, dataCase) => async (dispatch, getState) => {\n  console.log(\"start\");\n  try {\n    dispatch({\n      type: CASE_ASSIGNED_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/cases/assigned-to/${id}/`, dataCase, config);\n    dispatch({\n      type: CASE_ASSIGNED_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    console.log(error);\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_ASSIGNED_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// comment add\nexport const addNewCommentCase = (commentCase, caseId) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COMMENT_CASE_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/comments/add/${caseId}/`, commentCase, config);\n    dispatch({\n      type: COMMENT_CASE_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COMMENT_CASE_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list comment case\nexport const getListCommentCase = (page, caseId) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COMMENT_CASE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/comments/case/${caseId}/?page=${page}`, config);\n    dispatch({\n      type: COMMENT_CASE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COMMENT_CASE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases\nexport const casesListCoordinator = (page, filter = \"\", coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_COORDINATOR_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/coordinator/${coordinator}/?page=${page}&status=${filter}`, config);\n    dispatch({\n      type: CASE_COORDINATOR_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_COORDINATOR_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// Get case history\nexport const getCaseHistory = (caseId, page = 1) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_HISTORY_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/history/${caseId}/?page=${page}`, config);\n    dispatch({\n      type: CASE_HISTORY_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_HISTORY_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateCase = (id, caseItem) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_UPDATE_REQUEST\n    });\n    // const formData = new FormData();\n    // Object.keys(caseItem).forEach((key) => {\n    //   const value = caseItem[key];\n    //   if (value instanceof FileList || value instanceof Array) {\n    //     value.forEach((file, index) => {\n    //       formData.append(`${key}[${index}]`, file); // إضافة الملفات كـ array\n    //     });\n    //   } else if (value instanceof File) {\n    //     formData.append(key, value); // إضافة ملف واحد\n    //   } else {\n    //     formData.append(key, value); // إضافة بيانات عادية\n    //   }\n    // });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/cases/update/${id}/`, caseItem, config);\n    dispatch({\n      type: CASE_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// update case status only\nexport const updateCaseStatusOnly = (id, statusList) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_STATUS_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const formData = new FormData();\n    statusList.forEach(status => {\n      formData.append('case_status[]', status);\n    });\n    const {\n      data\n    } = await axios.put(`/cases/update-status-only/${id}/`, formData, config);\n    dispatch({\n      type: CASE_STATUS_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_STATUS_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// delete case\nexport const deleteCase = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/cases/delete/${id}/`, config);\n    dispatch({\n      type: CASE_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// case add\nexport const addNewCase = caseItem => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/cases/add/`, caseItem, config);\n    dispatch({\n      type: CASE_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// detail case\nexport const detailCase = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DETAIL_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/detail/${id}/`, config);\n    dispatch({\n      type: CASE_DETAIL_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DETAIL_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases\nexport const casesListMap = (page, status = \"\", caseId = \"\", patient = \"\", statusCase = \"\", insurance = \"\", provider = \"\", coordinator = \"\", type = \"\", ciaId = \"\", filterpaid = \"\") => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_LIST_MAP_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&ismaps=ismaps`, config);\n    dispatch({\n      type: CASE_LIST_MAP_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_LIST_MAP_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list cases\nexport const casesList = (page, status = \"\", caseId = \"\", patient = \"\", statusCase = \"\", insurance = \"\", provider = \"\", coordinator = \"\", type = \"\", ciaId = \"\", filterpaid = \"\") => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}`, config);\n    dispatch({\n      type: CASE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// Optimized get list cases for Dashboard (lightweight)\nexport const casesListDashboard = (page, status = \"\", caseId = \"\", patient = \"\", statusCase = \"\", insurance = \"\", provider = \"\", coordinator = \"\", type = \"\", ciaId = \"\", filterpaid = \"\") => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&isdashboard=true`, config);\n    dispatch({\n      type: CASE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// Optimized get list cases for CaseScreen (lightweight)\nexport const casesListCaseScreen = (page, status = \"\", caseId = \"\", patient = \"\", statusCase = \"\", insurance = \"\", provider = \"\", coordinator = \"\", type = \"\", ciaId = \"\", filterpaid = \"\") => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&iscasescreen=true`, config);\n    dispatch({\n      type: CASE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_LIST_MAP_REQUEST", "CASE_LIST_MAP_SUCCESS", "CASE_LIST_MAP_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_STATUS_UPDATE_REQUEST", "CASE_STATUS_UPDATE_SUCCESS", "CASE_STATUS_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "COMMENT_CASE_ADD_REQUEST", "COMMENT_CASE_ADD_SUCCESS", "COMMENT_CASE_ADD_FAIL", "COMMENT_CASE_DELETE_REQUEST", "COMMENT_CASE_DELETE_SUCCESS", "COMMENT_CASE_DELETE_FAIL", "CASE_ASSIGNED_UPDATE_REQUEST", "CASE_ASSIGNED_UPDATE_SUCCESS", "CASE_ASSIGNED_UPDATE_FAIL", "CASE_INSURANCE_LIST_REQUEST", "CASE_INSURANCE_LIST_SUCCESS", "CASE_INSURANCE_LIST_FAIL", "CASE_PROVIDER_LIST_REQUEST", "CASE_PROVIDER_LIST_SUCCESS", "CASE_PROVIDER_LIST_FAIL", "CASE_PROFILE_LIST_REQUEST", "CASE_PROFILE_LIST_SUCCESS", "CASE_PROFILE_LIST_FAIL", "CASE_DUPLICATE_REQUEST", "CASE_DUPLICATE_SUCCESS", "CASE_DUPLICATE_FAIL", "CASE_HISTORY_REQUEST", "CASE_HISTORY_SUCCESS", "CASE_HISTORY_FAIL", "deleteCommentCase", "commentId", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "delete", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "duplicateCase", "id", "put", "casesListLogged", "page", "filter", "get", "casesList<PERSON><PERSON>ider", "provider", "casesListInsurance", "insurance", "updateAssignedCase", "dataCase", "console", "log", "addNewCommentCase", "commentCase", "caseId", "post", "getListCommentCase", "casesListCoordinator", "coordinator", "getCaseHistory", "updateCase", "caseItem", "updateCaseStatusOnly", "statusList", "formData", "FormData", "for<PERSON>ach", "status", "append", "deleteCase", "addNewCase", "detailCase", "casesListMap", "patient", "statusCase", "ciaId", "filterpaid", "casesList", "casesListDashboard", "casesListCaseScreen"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/caseActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_LIST_MAP_REQUEST,\n  CASE_LIST_MAP_SUCCESS,\n  CASE_LIST_MAP_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_STATUS_UPDATE_REQUEST,\n  CASE_STATUS_UPDATE_SUCCESS,\n  CASE_STATUS_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n  COMMENT_CASE_ADD_REQUEST,\n  COMMENT_CASE_ADD_SUCCESS,\n  COMMENT_CASE_ADD_FAIL,\n  //\n  COMMENT_CASE_DELETE_REQUEST,\n  COMMENT_CASE_DELETE_SUCCESS,\n  COMMENT_CASE_DELETE_FAIL,\n  //\n  CASE_ASSIGNED_UPDATE_REQUEST,\n  CASE_ASSIGNED_UPDATE_SUCCESS,\n  CASE_ASSIGNED_UPDATE_FAIL,\n  //\n  CASE_INSURANCE_LIST_REQUEST,\n  CASE_INSURANCE_LIST_SUCCESS,\n  CASE_INSURANCE_LIST_FAIL,\n  //\n  CASE_PROVIDER_LIST_REQUEST,\n  CASE_PROVIDER_LIST_SUCCESS,\n  CASE_PROVIDER_LIST_FAIL,\n  //\n  CASE_PROFILE_LIST_REQUEST,\n  CASE_PROFILE_LIST_SUCCESS,\n  CASE_PROFILE_LIST_FAIL,\n  //\n  CASE_DUPLICATE_REQUEST,\n  CASE_DUPLICATE_SUCCESS,\n  CASE_DUPLICATE_FAIL,\n  //\n  CASE_HISTORY_REQUEST,\n  CASE_HISTORY_SUCCESS,\n  CASE_HISTORY_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\n// case add\nexport const deleteCommentCase = (commentId) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COMMENT_CASE_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(\n      `/comments/delete/${commentId}/`,\n      config\n    );\n\n    dispatch({\n      type: COMMENT_CASE_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COMMENT_CASE_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// duplicate case\nexport const duplicateCase = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DUPLICATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/cases/duplicate/${id}/`, {}, config);\n\n    dispatch({\n      type: CASE_DUPLICATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DUPLICATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list cases by logged\nexport const casesListLogged =\n  (page, filter = \"\") =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_PROFILE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/current-logged/?page=${page}&status=${filter}`,\n        config\n      );\n\n      dispatch({\n        type: CASE_PROFILE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_PROFILE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// get list cases by provider\nexport const casesListProvider =\n  (page, filter = \"\", provider) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_PROVIDER_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/provider/${provider}/?page=${page}&status=${filter}`,\n        config\n      );\n\n      dispatch({\n        type: CASE_PROVIDER_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_PROVIDER_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// get list cases by insurance\nexport const casesListInsurance =\n  (page, filter = \"\", insurance) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_INSURANCE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/insurance/${insurance}/?page=${page}&status=${filter}`,\n        config\n      );\n\n      dispatch({\n        type: CASE_INSURANCE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_INSURANCE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const updateAssignedCase =\n  (id, dataCase) => async (dispatch, getState) => {\n    console.log(\"start\");\n\n    try {\n      dispatch({\n        type: CASE_ASSIGNED_UPDATE_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.put(\n        `/cases/assigned-to/${id}/`,\n        dataCase,\n        config\n      );\n\n      dispatch({\n        type: CASE_ASSIGNED_UPDATE_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      console.log(error);\n\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_ASSIGNED_UPDATE_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// comment add\nexport const addNewCommentCase =\n  (commentCase, caseId) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COMMENT_CASE_ADD_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.post(\n        `/comments/add/${caseId}/`,\n        commentCase,\n        config\n      );\n\n      dispatch({\n        type: COMMENT_CASE_ADD_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COMMENT_CASE_ADD_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// get list comment case\nexport const getListCommentCase =\n  (page, caseId) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COMMENT_CASE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/comments/case/${caseId}/?page=${page}`,\n        config\n      );\n\n      dispatch({\n        type: COMMENT_CASE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COMMENT_CASE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// get list cases\nexport const casesListCoordinator =\n  (page, filter = \"\", coordinator) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_COORDINATOR_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/coordinator/${coordinator}/?page=${page}&status=${filter}`,\n        config\n      );\n\n      dispatch({\n        type: CASE_COORDINATOR_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_COORDINATOR_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// Get case history\nexport const getCaseHistory = (caseId, page = 1) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_HISTORY_REQUEST,\n    });\n\n    var {\n      userLogin: { userInfo },\n    } = getState();\n\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    const { data } = await axios.get(\n      `/cases/history/${caseId}/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: CASE_HISTORY_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_HISTORY_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateCase = (id, caseItem) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_UPDATE_REQUEST,\n    });\n    // const formData = new FormData();\n    // Object.keys(caseItem).forEach((key) => {\n    //   const value = caseItem[key];\n    //   if (value instanceof FileList || value instanceof Array) {\n    //     value.forEach((file, index) => {\n    //       formData.append(`${key}[${index}]`, file); // إضافة الملفات كـ array\n    //     });\n    //   } else if (value instanceof File) {\n    //     formData.append(key, value); // إضافة ملف واحد\n    //   } else {\n    //     formData.append(key, value); // إضافة بيانات عادية\n    //   }\n    // });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/cases/update/${id}/`, caseItem, config);\n\n    dispatch({\n      type: CASE_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// update case status only\nexport const updateCaseStatusOnly = (id, statusList) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_STATUS_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n\n    const formData = new FormData();\n    statusList.forEach(status => {\n      formData.append('case_status[]', status);\n    });\n\n    const { data } = await axios.put(`/cases/update-status-only/${id}/`, formData, config);\n\n    dispatch({\n      type: CASE_STATUS_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_STATUS_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// delete case\nexport const deleteCase = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DELETE_REQUEST,\n    });\n\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/cases/delete/${id}/`, config);\n\n    dispatch({\n      type: CASE_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// case add\nexport const addNewCase = (caseItem) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/cases/add/`, caseItem, config);\n\n    dispatch({\n      type: CASE_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// detail case\nexport const detailCase = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: CASE_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/cases/detail/${id}/`, config);\n\n    dispatch({\n      type: CASE_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: CASE_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list cases\nexport const casesListMap =\n  (\n    page,\n    status = \"\",\n    caseId = \"\",\n    patient = \"\",\n    statusCase = \"\",\n    insurance = \"\",\n    provider = \"\",\n    coordinator = \"\",\n    type = \"\",\n    ciaId = \"\",\n    filterpaid = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_LIST_MAP_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&ismaps=ismaps`,\n        config\n      );\n\n      dispatch({\n        type: CASE_LIST_MAP_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_LIST_MAP_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// get list cases\nexport const casesList =\n  (\n    page,\n    status = \"\",\n    caseId = \"\",\n    patient = \"\",\n    statusCase = \"\",\n    insurance = \"\",\n    provider = \"\",\n    coordinator = \"\",\n    type = \"\",\n    ciaId = \"\",\n    filterpaid = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}`,\n        config\n      );\n\n      dispatch({\n        type: CASE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// Optimized get list cases for Dashboard (lightweight)\nexport const casesListDashboard =\n  (\n    page,\n    status = \"\",\n    caseId = \"\",\n    patient = \"\",\n    statusCase = \"\",\n    insurance = \"\",\n    provider = \"\",\n    coordinator = \"\",\n    type = \"\",\n    ciaId = \"\",\n    filterpaid = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&isdashboard=true`,\n        config\n      );\n\n      dispatch({\n        type: CASE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\n// Optimized get list cases for CaseScreen (lightweight)\nexport const casesListCaseScreen =\n  (\n    page,\n    status = \"\",\n    caseId = \"\",\n    patient = \"\",\n    statusCase = \"\",\n    insurance = \"\",\n    provider = \"\",\n    coordinator = \"\",\n    type = \"\",\n    ciaId = \"\",\n    filterpaid = \"\"\n  ) =>\n  async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: CASE_LIST_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/cases/?page=${page}&status=${status}&patient=${patient}&statuscase=${statusCase}&id=${caseId}&insurance=${insurance}&provider=${provider}&coordinator=${coordinator}&type=${type}&ciaid=${ciaId}&filterpaid=${filterpaid}&iscasescreen=true`,\n        config\n      );\n\n      dispatch({\n        type: CASE_LIST_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: CASE_LIST_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC,kBAAkB;AAClB;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC;AACA;AAAA,OACK,4BAA4B;;AAEnC;AACA,OAAO,MAAMC,iBAAiB,GAAIC,SAAS,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC5E,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEzB;IACR,CAAC,CAAC;IACF,IAAI;MACF0B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACgE,MAAM,CAChC,oBAAmBX,SAAU,GAAE,EAChCM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAExB,2BAA2B;MACjCiC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEvB,wBAAwB;MAC9BgC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,aAAa,GAAIC,EAAE,IAAK,OAAOtB,QAAQ,EAAEC,QAAQ,KAAK;EACjE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEV;IACR,CAAC,CAAC;IACF,IAAI;MACFW,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6E,GAAG,CAAE,oBAAmBD,EAAG,GAAE,EAAE,CAAC,CAAC,EAAEjB,MAAM,CAAC;IAEvEL,QAAQ,CAAC;MACPE,IAAI,EAAET,sBAAsB;MAC5BkB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAER,mBAAmB;MACzBiB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMS,eAAe,GAC1BA,CAACC,IAAI,EAAEC,MAAM,GAAG,EAAE,KAClB,OAAO1B,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEb;IACR,CAAC,CAAC;IACF,IAAI;MACFc,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,+BAA8BF,IAAK,WAAUC,MAAO,EAAC,EACtDrB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEZ,yBAAyB;MAC/BqB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEX,sBAAsB;MAC5BoB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAMa,iBAAiB,GAC5BA,CAACH,IAAI,EAAEC,MAAM,GAAG,EAAE,EAAEG,QAAQ,KAC5B,OAAO7B,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEhB;IACR,CAAC,CAAC;IACF,IAAI;MACFiB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,mBAAkBE,QAAS,UAASJ,IAAK,WAAUC,MAAO,EAAC,EAC5DrB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEf,0BAA0B;MAChCwB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEd,uBAAuB;MAC7BuB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAMe,kBAAkB,GAC7BA,CAACL,IAAI,EAAEC,MAAM,GAAG,EAAE,EAAEK,SAAS,KAC7B,OAAO/B,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEnB;IACR,CAAC,CAAC;IACF,IAAI;MACFoB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,oBAAmBI,SAAU,UAASN,IAAK,WAAUC,MAAO,EAAC,EAC9DrB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAElB,2BAA2B;MACjC2B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEjB,wBAAwB;MAC9B0B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMiB,kBAAkB,GAC7BA,CAACV,EAAE,EAAEW,QAAQ,KAAK,OAAOjC,QAAQ,EAAEC,QAAQ,KAAK;EAC9CiC,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;EAEpB,IAAI;IACFnC,QAAQ,CAAC;MACPE,IAAI,EAAEtB;IACR,CAAC,CAAC;IACF,IAAI;MACFuB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6E,GAAG,CAC7B,sBAAqBD,EAAG,GAAE,EAC3BW,QAAQ,EACR5B,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAErB,4BAA4B;MAClC8B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdsB,OAAO,CAACC,GAAG,CAACvB,KAAK,CAAC;IAElB,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEpB,yBAAyB;MAC/B6B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAMqB,iBAAiB,GAC5BA,CAACC,WAAW,EAAEC,MAAM,KAAK,OAAOtC,QAAQ,EAAEC,QAAQ,KAAK;EACrD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE5B;IACR,CAAC,CAAC;IACF,IAAI;MACF6B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6F,IAAI,CAC9B,iBAAgBD,MAAO,GAAE,EAC1BD,WAAW,EACXhC,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAE3B,wBAAwB;MAC9BoC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE1B,qBAAqB;MAC3BmC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAMyB,kBAAkB,GAC7BA,CAACf,IAAI,EAAEa,MAAM,KAAK,OAAOtC,QAAQ,EAAEC,QAAQ,KAAK;EAC9C,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE/B;IACR,CAAC,CAAC;IACF,IAAI;MACFgC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,kBAAiBW,MAAO,UAASb,IAAK,EAAC,EACxCpB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAE9B,yBAAyB;MAC/BuC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE7B,sBAAsB;MAC5BsC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAM0B,oBAAoB,GAC/BA,CAAChB,IAAI,EAAEC,MAAM,GAAG,EAAE,EAAEgB,WAAW,KAC/B,OAAO1C,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAElC;IACR,CAAC,CAAC;IACF,IAAI;MACFmC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,sBAAqBe,WAAY,UAASjB,IAAK,WAAUC,MAAO,EAAC,EAClErB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEjC,6BAA6B;MACnC0C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEhC,0BAA0B;MAChCyC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAM4B,cAAc,GAAGA,CAACL,MAAM,EAAEb,IAAI,GAAG,CAAC,KAAK,OAAOzB,QAAQ,EAAEC,QAAQ,KAAK;EAChF,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEP;IACR,CAAC,CAAC;IAEF,IAAI;MACFQ,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IAEd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IAED,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,kBAAiBW,MAAO,UAASb,IAAK,EAAC,EACxCpB,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEN,oBAAoB;MAC1Be,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEL,iBAAiB;MACvBc,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAM6B,UAAU,GAAGA,CAACtB,EAAE,EAAEuB,QAAQ,KAAK,OAAO7C,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE3C;IACR,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI;MACF4C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6E,GAAG,CAAE,iBAAgBD,EAAG,GAAE,EAAEuB,QAAQ,EAAExC,MAAM,CAAC;IAE1EL,QAAQ,CAAC;MACPE,IAAI,EAAE1C,mBAAmB;MACzBmD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEzC,gBAAgB;MACtBkD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAM+B,oBAAoB,GAAGA,CAACxB,EAAE,EAAEyB,UAAU,KAAK,OAAO/C,QAAQ,EAAEC,QAAQ,KAAK;EACpF,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAExC;IACR,CAAC,CAAC;IACF,IAAI;MACFyC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IAED,MAAMwC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BF,UAAU,CAACG,OAAO,CAACC,MAAM,IAAI;MAC3BH,QAAQ,CAACI,MAAM,CAAC,eAAe,EAAED,MAAM,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAM;MAAE1C;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6E,GAAG,CAAE,6BAA4BD,EAAG,GAAE,EAAE0B,QAAQ,EAAE3C,MAAM,CAAC;IAEtFL,QAAQ,CAAC;MACPE,IAAI,EAAEvC,0BAA0B;MAChCgD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEtC,uBAAuB;MAC7B+C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMsC,UAAU,GAAI/B,EAAE,IAAK,OAAOtB,QAAQ,EAAEC,QAAQ,KAAK;EAC9D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAErC;IACR,CAAC,CAAC;IAEF,IAAI;MACFsC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACgE,MAAM,CAAE,iBAAgBY,EAAG,GAAE,EAAEjB,MAAM,CAAC;IAEnEL,QAAQ,CAAC;MACPE,IAAI,EAAEpC,mBAAmB;MACzB6C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEnC,gBAAgB;MACtB4C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMuC,UAAU,GAAIT,QAAQ,IAAK,OAAO7C,QAAQ,EAAEC,QAAQ,KAAK;EACpE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEjD;IACR,CAAC,CAAC;IACF,IAAI;MACFkD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAAC6F,IAAI,CAAE,aAAY,EAAEM,QAAQ,EAAExC,MAAM,CAAC;IAElEL,QAAQ,CAAC;MACPE,IAAI,EAAEhD,gBAAgB;MACtByD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE/C,aAAa;MACnBwD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMwC,UAAU,GAAIjC,EAAE,IAAK,OAAOtB,QAAQ,EAAEC,QAAQ,KAAK;EAC9D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE9C;IACR,CAAC,CAAC;IACF,IAAI;MACF+C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAAE,iBAAgBL,EAAG,GAAE,EAAEjB,MAAM,CAAC;IAEhEL,QAAQ,CAAC;MACPE,IAAI,EAAE7C,mBAAmB;MACzBsD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE5C,gBAAgB;MACtBqD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMyC,YAAY,GACvBA,CACE/B,IAAI,EACJ0B,MAAM,GAAG,EAAE,EACXb,MAAM,GAAG,EAAE,EACXmB,OAAO,GAAG,EAAE,EACZC,UAAU,GAAG,EAAE,EACf3B,SAAS,GAAG,EAAE,EACdF,QAAQ,GAAG,EAAE,EACba,WAAW,GAAG,EAAE,EAChBxC,IAAI,GAAG,EAAE,EACTyD,KAAK,GAAG,EAAE,EACVC,UAAU,GAAG,EAAE,KAEjB,OAAO5D,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEpD;IACR,CAAC,CAAC;IACF,IAAI;MACFqD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,gBAAeF,IAAK,WAAU0B,MAAO,YAAWM,OAAQ,eAAcC,UAAW,OAAMpB,MAAO,cAAaP,SAAU,aAAYF,QAAS,gBAAea,WAAY,SAAQxC,IAAK,UAASyD,KAAM,eAAcC,UAAW,gBAAe,EAC1OvD,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEnD,qBAAqB;MAC3B4D,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElD,kBAAkB;MACxB2D,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAM8C,SAAS,GACpBA,CACEpC,IAAI,EACJ0B,MAAM,GAAG,EAAE,EACXb,MAAM,GAAG,EAAE,EACXmB,OAAO,GAAG,EAAE,EACZC,UAAU,GAAG,EAAE,EACf3B,SAAS,GAAG,EAAE,EACdF,QAAQ,GAAG,EAAE,EACba,WAAW,GAAG,EAAE,EAChBxC,IAAI,GAAG,EAAE,EACTyD,KAAK,GAAG,EAAE,EACVC,UAAU,GAAG,EAAE,KAEjB,OAAO5D,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEvD;IACR,CAAC,CAAC;IACF,IAAI;MACFwD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,gBAAeF,IAAK,WAAU0B,MAAO,YAAWM,OAAQ,eAAcC,UAAW,OAAMpB,MAAO,cAAaP,SAAU,aAAYF,QAAS,gBAAea,WAAY,SAAQxC,IAAK,UAASyD,KAAM,eAAcC,UAAW,EAAC,EAC5NvD,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEtD,iBAAiB;MACvB+D,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAErD,cAAc;MACpB8D,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAM+C,kBAAkB,GAC7BA,CACErC,IAAI,EACJ0B,MAAM,GAAG,EAAE,EACXb,MAAM,GAAG,EAAE,EACXmB,OAAO,GAAG,EAAE,EACZC,UAAU,GAAG,EAAE,EACf3B,SAAS,GAAG,EAAE,EACdF,QAAQ,GAAG,EAAE,EACba,WAAW,GAAG,EAAE,EAChBxC,IAAI,GAAG,EAAE,EACTyD,KAAK,GAAG,EAAE,EACVC,UAAU,GAAG,EAAE,KAEjB,OAAO5D,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEvD;IACR,CAAC,CAAC;IACF,IAAI;MACFwD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,gBAAeF,IAAK,WAAU0B,MAAO,YAAWM,OAAQ,eAAcC,UAAW,OAAMpB,MAAO,cAAaP,SAAU,aAAYF,QAAS,gBAAea,WAAY,SAAQxC,IAAK,UAASyD,KAAM,eAAcC,UAAW,mBAAkB,EAC7OvD,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEtD,iBAAiB;MACvB+D,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAErD,cAAc;MACpB8D,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAEH;AACA,OAAO,MAAMgD,mBAAmB,GAC9BA,CACEtC,IAAI,EACJ0B,MAAM,GAAG,EAAE,EACXb,MAAM,GAAG,EAAE,EACXmB,OAAO,GAAG,EAAE,EACZC,UAAU,GAAG,EAAE,EACf3B,SAAS,GAAG,EAAE,EACdF,QAAQ,GAAG,EAAE,EACba,WAAW,GAAG,EAAE,EAChBxC,IAAI,GAAG,EAAE,EACTyD,KAAK,GAAG,EAAE,EACVC,UAAU,GAAG,EAAE,KAEjB,OAAO5D,QAAQ,EAAEC,QAAQ,KAAK;EAC5B,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEvD;IACR,CAAC,CAAC;IACF,IAAI;MACFwD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM/D,KAAK,CAACiF,GAAG,CAC7B,gBAAeF,IAAK,WAAU0B,MAAO,YAAWM,OAAQ,eAAcC,UAAW,OAAMpB,MAAO,cAAaP,SAAU,aAAYF,QAAS,gBAAea,WAAY,SAAQxC,IAAK,UAASyD,KAAM,eAAcC,UAAW,oBAAmB,EAC9OvD,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEtD,iBAAiB;MACvB+D,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAErD,cAAc;MACpB8D,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}