{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesListDashboard, casesListMap, deleteCase } from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersListDashboard } from \"../../redux/actions/providerActions\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\"\n});\nfunction DashboardScreen() {\n  _s();\n  var _providerMapSelect$pr, _providerMapSelect$pr2, _providerMapSelect$pr3;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(searchParams.get(\"filterciaid\") || \"\");\n  const [patientFilter, setPatientFilter] = useState(searchParams.get(\"filterpatient\") || \"\");\n  const [insuranceFilter, setInsuranceFilter] = useState(searchParams.get(\"filterinsurance\") || \"\");\n  const [typeFilter, setTypeFilter] = useState(searchParams.get(\"filtertype\") || \"\");\n  const [providerFilter, setProviderFilter] = useState(searchParams.get(\"filterprovider\") || \"\");\n  const [coordinationFilter, setCoordinatorFilter] = useState(searchParams.get(\"filtercoordination\") || \"\");\n  const [statusFilter, setStatusrFilter] = useState(searchParams.get(\"filterstatus\") || \"\");\n  useEffect(() => {\n    var _insuranceFilter$valu, _providerFilter$value, _coordinationFilter$v;\n    const params = new URLSearchParams();\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", (_insuranceFilter$valu = insuranceFilter.value) !== null && _insuranceFilter$valu !== void 0 ? _insuranceFilter$valu : \"\");\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", (_providerFilter$value = providerFilter.value) !== null && _providerFilter$value !== void 0 ? _providerFilter$value : \"\");\n    if (coordinationFilter) params.set(\"filtercoordination\", (_coordinationFilter$v = coordinationFilter.value) !== null && _coordinationFilter$v !== void 0 ? _coordinationFilter$v : \"\");\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString()\n    });\n  }, [idFilter, patientFilter, statusFilter, insuranceFilter, providerFilter, coordinationFilter, typeFilter, ciaIdFilter, dispatch, navigate, location.pathname]);\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const listCasesMap = useSelector(state => state.caseListMap);\n  const {\n    casesMap,\n    loadingCasesMap,\n    errorCasesMap\n  } = listCasesMap;\n  const caseDelete = useSelector(state => state.deleteCase);\n  const {\n    loadingCaseDelete,\n    errorCaseDelete,\n    successCaseDelete\n  } = caseDelete;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      var _insuranceFilter$valu2, _providerFilter$value2, _coordinationFilter$v2, _insuranceFilter$valu3, _providerFilter$value3, _coordinationFilter$v3;\n      const parser = new UAParser();\n      const result = parser.getResult();\n      const browser = result.browser.name || \"Unknown browser\";\n      const device = result.device.model || result.device.type || \"Unknown device\";\n\n      // get list cases (optimized for dashboard)\n      dispatch(casesListDashboard(page, \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu2 = insuranceFilter.value) !== null && _insuranceFilter$valu2 !== void 0 ? _insuranceFilter$valu2 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value2 = providerFilter.value) !== null && _providerFilter$value2 !== void 0 ? _providerFilter$value2 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v2 = coordinationFilter.value) !== null && _coordinationFilter$v2 !== void 0 ? _coordinationFilter$v2 : \"\" : \"\", typeFilter, ciaIdFilter));\n      // get list case maps \n      dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu3 = insuranceFilter.value) !== null && _insuranceFilter$valu3 !== void 0 ? _insuranceFilter$valu3 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value3 = providerFilter.value) !== null && _providerFilter$value3 !== void 0 ? _providerFilter$value3 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v3 = coordinationFilter.value) !== null && _coordinationFilter$v3 !== void 0 ? _coordinationFilter$v3 : \"\" : \"\", typeFilter, ciaIdFilter));\n      // get List Coordinators\n      dispatch(getListCoordinators(\"0\"));\n      // get providers List (optimized for dashboard)\n      dispatch(providersListDashboard(\"0\"));\n      // get Insuranes List (optimized for dashboard)\n      dispatch(insurancesListDashboard(\"0\"));\n      // \n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successCaseDelete) {\n      var _insuranceFilter$valu4, _providerFilter$value4, _coordinationFilter$v4, _insuranceFilter$valu5, _providerFilter$value5, _coordinationFilter$v5;\n      dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu4 = insuranceFilter.value) !== null && _insuranceFilter$valu4 !== void 0 ? _insuranceFilter$valu4 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value4 = providerFilter.value) !== null && _providerFilter$value4 !== void 0 ? _providerFilter$value4 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v4 = coordinationFilter.value) !== null && _coordinationFilter$v4 !== void 0 ? _coordinationFilter$v4 : \"\" : \"\", typeFilter, ciaIdFilter));\n      dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu5 = insuranceFilter.value) !== null && _insuranceFilter$valu5 !== void 0 ? _insuranceFilter$valu5 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value5 = providerFilter.value) !== null && _providerFilter$value5 !== void 0 ? _providerFilter$value5 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v5 = coordinationFilter.value) !== null && _coordinationFilter$v5 !== void 0 ? _coordinationFilter$v5 : \"\" : \"\", typeFilter, ciaIdFilter));\n    }\n  }, [successCaseDelete]);\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Cases list\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row justify-end\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/cases-list/add\",\n              className: \"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 4.5v15m7.5-7.5h-15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-2\",\n                children: \"Create new case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-2 rounded-lg mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",\n                placeholder: \"Search Case ID...\",\n                type: \"text\",\n                value: idFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu6, _providerFilter$value6, _coordinationFilter$v6, _insuranceFilter$valu7, _providerFilter$value7, _coordinationFilter$v7;\n                  setIdFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", encodeURIComponent(v.target.value), patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu6 = insuranceFilter.value) !== null && _insuranceFilter$valu6 !== void 0 ? _insuranceFilter$valu6 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value6 = providerFilter.value) !== null && _providerFilter$value6 !== void 0 ? _providerFilter$value6 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v6 = coordinationFilter.value) !== null && _coordinationFilter$v6 !== void 0 ? _coordinationFilter$v6 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", encodeURIComponent(v.target.value), patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu7 = insuranceFilter.value) !== null && _insuranceFilter$valu7 !== void 0 ? _insuranceFilter$valu7 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value7 = providerFilter.value) !== null && _providerFilter$value7 !== void 0 ? _providerFilter$value7 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v7 = coordinationFilter.value) !== null && _coordinationFilter$v7 !== void 0 ? _coordinationFilter$v7 : \"\" : \"\", typeFilter, ciaIdFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",\n                placeholder: \"CIA Reference...\",\n                type: \"text\",\n                value: ciaIdFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu8, _providerFilter$value8, _coordinationFilter$v8, _insuranceFilter$valu9, _providerFilter$value9, _coordinationFilter$v9;\n                  setCiaIdFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu8 = insuranceFilter.value) !== null && _insuranceFilter$valu8 !== void 0 ? _insuranceFilter$valu8 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value8 = providerFilter.value) !== null && _providerFilter$value8 !== void 0 ? _providerFilter$value8 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v8 = coordinationFilter.value) !== null && _coordinationFilter$v8 !== void 0 ? _coordinationFilter$v8 : \"\" : \"\", typeFilter, v.target.value));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu9 = insuranceFilter.value) !== null && _insuranceFilter$valu9 !== void 0 ? _insuranceFilter$valu9 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value9 = providerFilter.value) !== null && _providerFilter$value9 !== void 0 ? _providerFilter$value9 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v9 = coordinationFilter.value) !== null && _coordinationFilter$v9 !== void 0 ? _coordinationFilter$v9 : \"\" : \"\", typeFilter, v.target.value));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                className: \"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\",\n                placeholder: \"Patient Name...\",\n                type: \"text\",\n                value: patientFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu10, _providerFilter$value10, _coordinationFilter$v10, _insuranceFilter$valu11, _providerFilter$value11, _coordinationFilter$v11;\n                  setPatientFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, v.target.value, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu10 = insuranceFilter.value) !== null && _insuranceFilter$valu10 !== void 0 ? _insuranceFilter$valu10 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value10 = providerFilter.value) !== null && _providerFilter$value10 !== void 0 ? _providerFilter$value10 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v10 = coordinationFilter.value) !== null && _coordinationFilter$v10 !== void 0 ? _coordinationFilter$v10 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, v.target.value, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu11 = insuranceFilter.value) !== null && _insuranceFilter$valu11 !== void 0 ? _insuranceFilter$valu11 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value11 = providerFilter.value) !== null && _providerFilter$value11 !== void 0 ? _providerFilter$value11 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v11 = coordinationFilter.value) !== null && _coordinationFilter$v11 !== void 0 ? _coordinationFilter$v11 : \"\" : \"\", typeFilter, ciaIdFilter));\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: typeFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu12, _providerFilter$value12, _coordinationFilter$v12, _insuranceFilter$valu13, _providerFilter$value13, _coordinationFilter$v13;\n                  setTypeFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu12 = insuranceFilter.value) !== null && _insuranceFilter$valu12 !== void 0 ? _insuranceFilter$valu12 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value12 = providerFilter.value) !== null && _providerFilter$value12 !== void 0 ? _providerFilter$value12 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v12 = coordinationFilter.value) !== null && _coordinationFilter$v12 !== void 0 ? _coordinationFilter$v12 : \"\" : \"\", v.target.value, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu13 = insuranceFilter.value) !== null && _insuranceFilter$valu13 !== void 0 ? _insuranceFilter$valu13 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value13 = providerFilter.value) !== null && _providerFilter$value13 !== void 0 ? _providerFilter$value13 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v13 = coordinationFilter.value) !== null && _coordinationFilter$v13 !== void 0 ? _coordinationFilter$v13 : \"\" : \"\", v.target.value, ciaIdFilter));\n                },\n                className: \"w-full pl-10 pr-8 py-3 rounded-lg bg-white text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Types\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Medical\",\n                  children: \"Medical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Technical\",\n                  children: \"Technical\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 text-gray-400\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 9l-7 7-7-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 p-6 rounded-lg mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: statusFilter,\n                onChange: v => {\n                  var _insuranceFilter$valu14, _providerFilter$value14, _coordinationFilter$v14, _insuranceFilter$valu15, _providerFilter$value15, _coordinationFilter$v15;\n                  setStatusrFilter(v.target.value);\n                  dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, v.target.value, insuranceFilter !== \"\" ? (_insuranceFilter$valu14 = insuranceFilter.value) !== null && _insuranceFilter$valu14 !== void 0 ? _insuranceFilter$valu14 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value14 = providerFilter.value) !== null && _providerFilter$value14 !== void 0 ? _providerFilter$value14 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v14 = coordinationFilter.value) !== null && _coordinationFilter$v14 !== void 0 ? _coordinationFilter$v14 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, v.target.value, insuranceFilter !== \"\" ? (_insuranceFilter$valu15 = insuranceFilter.value) !== null && _insuranceFilter$valu15 !== void 0 ? _insuranceFilter$valu15 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value15 = providerFilter.value) !== null && _providerFilter$value15 !== void 0 ? _providerFilter$value15 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v15 = coordinationFilter.value) !== null && _coordinationFilter$v15 !== void 0 ? _coordinationFilter$v15 : \"\" : \"\", typeFilter, ciaIdFilter));\n                },\n                className: \"w-full pl-10 pr-8 py-[14px] rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"pending-coordination\",\n                  children: \"Pending Coordination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-m-r\",\n                  children: \"Coordinated, Missing M.R.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-invoice\",\n                  children: \"Coordinated, Missing Invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"waiting-for-insurance-authorization\",\n                  children: \"Waiting for Insurance Authorization\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-patient-not-seen-yet\",\n                  children: \"Coordinated, Patient not seen yet\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"fully-coordinated\",\n                  children: \"Fully Coordinated\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordinated-missing-payment\",\n                  children: \"Coordinated, Missing Payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"coordination-fee\",\n                  children: \"Coordination Fee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"failed\",\n                  children: \"Failed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-4 w-4 text-gray-400\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 9l-7 7-7-7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: insuranceFilter,\n                onChange: option => {\n                  setInsuranceFilter(option);\n                  if (option && option.value) {\n                    var _providerFilter$value16, _coordinationFilter$v16, _providerFilter$value17, _coordinationFilter$v17;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, option.value, providerFilter !== \"\" ? (_providerFilter$value16 = providerFilter.value) !== null && _providerFilter$value16 !== void 0 ? _providerFilter$value16 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v16 = coordinationFilter.value) !== null && _coordinationFilter$v16 !== void 0 ? _coordinationFilter$v16 : \"\" : \"\", typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, option.value, providerFilter !== \"\" ? (_providerFilter$value17 = providerFilter.value) !== null && _providerFilter$value17 !== void 0 ? _providerFilter$value17 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v17 = coordinationFilter.value) !== null && _coordinationFilter$v17 !== void 0 ? _coordinationFilter$v17 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  } else {\n                    var _providerFilter$value18, _coordinationFilter$v18, _providerFilter$value19, _coordinationFilter$v19;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, \"\", providerFilter !== \"\" ? (_providerFilter$value18 = providerFilter.value) !== null && _providerFilter$value18 !== void 0 ? _providerFilter$value18 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v18 = coordinationFilter.value) !== null && _coordinationFilter$v18 !== void 0 ? _coordinationFilter$v18 : \"\" : \"\", typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, \"\", providerFilter !== \"\" ? (_providerFilter$value19 = providerFilter.value) !== null && _providerFilter$value19 !== void 0 ? _providerFilter$value19 : \"\" : \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v19 = coordinationFilter.value) !== null && _coordinationFilter$v19 !== void 0 ? _coordinationFilter$v19 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  }\n                },\n                options: insurances && insurances.length > 0 ? insurances.map(assurance => ({\n                  value: assurance.id,\n                  label: assurance.assurance_name || \"\"\n                })) : [],\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                placeholder: \"Select Insurance...\",\n                isSearchable: true,\n                isClearable: true,\n                className: \"react-select-container\",\n                classNamePrefix: \"react-select\",\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    minHeight: '48px',\n                    paddingLeft: '32px',\n                    border: 'none',\n                    borderRadius: '8px',\n                    backgroundColor: '#ffffff',\n                    boxShadow: 'none',\n                    '&:hover': {\n                      backgroundColor: '#eff6ff'\n                    },\n                    transition: 'all 0.2s'\n                  }),\n                  placeholder: base => ({\n                    ...base,\n                    color: '#9ca3af',\n                    fontSize: '14px'\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    color: '#374151',\n                    fontSize: '14px'\n                  }),\n                  option: (base, state) => ({\n                    ...base,\n                    backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                    color: state.isSelected ? 'white' : '#374151',\n                    fontSize: '14px'\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: providerFilter,\n                onChange: option => {\n                  setProviderFilter(option);\n                  if (option && option.value) {\n                    var _insuranceFilter$valu16, _coordinationFilter$v20, _insuranceFilter$valu17, _coordinationFilter$v21;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu16 = insuranceFilter.value) !== null && _insuranceFilter$valu16 !== void 0 ? _insuranceFilter$valu16 : \"\" : \"\", option.value, coordinationFilter !== \"\" ? (_coordinationFilter$v20 = coordinationFilter.value) !== null && _coordinationFilter$v20 !== void 0 ? _coordinationFilter$v20 : \"\" : \"\", typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu17 = insuranceFilter.value) !== null && _insuranceFilter$valu17 !== void 0 ? _insuranceFilter$valu17 : \"\" : \"\", option.value, coordinationFilter !== \"\" ? (_coordinationFilter$v21 = coordinationFilter.value) !== null && _coordinationFilter$v21 !== void 0 ? _coordinationFilter$v21 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  } else {\n                    var _insuranceFilter$valu18, _coordinationFilter$v22, _insuranceFilter$valu19, _coordinationFilter$v23;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu18 = insuranceFilter.value) !== null && _insuranceFilter$valu18 !== void 0 ? _insuranceFilter$valu18 : \"\" : \"\", \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v22 = coordinationFilter.value) !== null && _coordinationFilter$v22 !== void 0 ? _coordinationFilter$v22 : \"\" : \"\", typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu19 = insuranceFilter.value) !== null && _insuranceFilter$valu19 !== void 0 ? _insuranceFilter$valu19 : \"\" : \"\", \"\", coordinationFilter !== \"\" ? (_coordinationFilter$v23 = coordinationFilter.value) !== null && _coordinationFilter$v23 !== void 0 ? _coordinationFilter$v23 : \"\" : \"\", typeFilter, ciaIdFilter));\n                  }\n                },\n                options: providers && providers.length > 0 ? providers.map(provider => ({\n                  value: provider.id,\n                  label: provider.full_name || \"\"\n                })) : [],\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                placeholder: \"Select Provider...\",\n                isSearchable: true,\n                isClearable: true,\n                className: \"react-select-container\",\n                classNamePrefix: \"react-select\",\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    minHeight: '48px',\n                    paddingLeft: '32px',\n                    border: 'none',\n                    borderRadius: '8px',\n                    backgroundColor: '#ffffff',\n                    boxShadow: 'none',\n                    '&:hover': {\n                      backgroundColor: '#eff6ff'\n                    },\n                    transition: 'all 0.2s'\n                  }),\n                  placeholder: base => ({\n                    ...base,\n                    color: '#9ca3af',\n                    fontSize: '14px'\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    color: '#374151',\n                    fontSize: '14px'\n                  }),\n                  option: (base, state) => ({\n                    ...base,\n                    backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                    color: state.isSelected ? 'white' : '#374151',\n                    fontSize: '14px'\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative group\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  stroke: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 847,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: coordinationFilter,\n                onChange: option => {\n                  setCoordinatorFilter(option);\n                  if (option && option.value) {\n                    var _insuranceFilter$valu20, _providerFilter$value20, _insuranceFilter$valu21, _providerFilter$value21;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu20 = insuranceFilter.value) !== null && _insuranceFilter$valu20 !== void 0 ? _insuranceFilter$valu20 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value20 = providerFilter.value) !== null && _providerFilter$value20 !== void 0 ? _providerFilter$value20 : \"\" : \"\", option.value, typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu21 = insuranceFilter.value) !== null && _insuranceFilter$valu21 !== void 0 ? _insuranceFilter$valu21 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value21 = providerFilter.value) !== null && _providerFilter$value21 !== void 0 ? _providerFilter$value21 : \"\" : \"\", option.value, typeFilter, ciaIdFilter));\n                  } else {\n                    var _insuranceFilter$valu22, _providerFilter$value22, _insuranceFilter$valu23, _providerFilter$value23;\n                    dispatch(casesListDashboard(\"1\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu22 = insuranceFilter.value) !== null && _insuranceFilter$valu22 !== void 0 ? _insuranceFilter$valu22 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value22 = providerFilter.value) !== null && _providerFilter$value22 !== void 0 ? _providerFilter$value22 : \"\" : \"\", '', typeFilter, ciaIdFilter));\n                    dispatch(casesListMap(\"0\", \"\", idFilter, patientFilter, statusFilter, insuranceFilter !== \"\" ? (_insuranceFilter$valu23 = insuranceFilter.value) !== null && _insuranceFilter$valu23 !== void 0 ? _insuranceFilter$valu23 : \"\" : \"\", providerFilter !== \"\" ? (_providerFilter$value23 = providerFilter.value) !== null && _providerFilter$value23 !== void 0 ? _providerFilter$value23 : \"\" : \"\", '', typeFilter, ciaIdFilter));\n                  }\n                },\n                options: coordinators && coordinators.length > 0 ? coordinators.map(coordinator => ({\n                  value: coordinator.id,\n                  label: coordinator.full_name || \"\"\n                })) : [],\n                filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                placeholder: \"Select Coordinator...\",\n                isSearchable: true,\n                isClearable: true,\n                className: \"react-select-container\",\n                classNamePrefix: \"react-select\",\n                styles: {\n                  control: (base, state) => ({\n                    ...base,\n                    minHeight: '48px',\n                    paddingLeft: '32px',\n                    border: 'none',\n                    borderRadius: '8px',\n                    backgroundColor: '#ffffff',\n                    boxShadow: 'none',\n                    '&:hover': {\n                      backgroundColor: '#eff6ff'\n                    },\n                    transition: 'all 0.2s'\n                  }),\n                  placeholder: base => ({\n                    ...base,\n                    color: '#9ca3af',\n                    fontSize: '14px'\n                  }),\n                  singleValue: base => ({\n                    ...base,\n                    color: '#374151',\n                    fontSize: '14px'\n                  }),\n                  option: (base, state) => ({\n                    ...base,\n                    backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                    color: state.isSelected ? 'white' : '#374151',\n                    fontSize: '14px'\n                  })\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setIdFilter(\"\");\n                  setInsuranceFilter(\"\");\n                  setProviderFilter(\"\");\n                  setCoordinatorFilter(\"\");\n                  setStatusrFilter(\"\");\n                  setTypeFilter(\"\");\n                  setPatientFilter(\"\");\n                  setCiaIdFilter(\"\");\n                  dispatch(casesListDashboard(\"1\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                  // get list case maps \n                  dispatch(casesListMap(\"0\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\", \"\"));\n                },\n                className: \"w-full flex items-center justify-center gap-2 bg-danger hover:bg-danger text-white px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 19\n                }, this), \"Reset Filters\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: loadingCases ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1029,\n              columnNumber: 17\n            }, this) : errorCases ? /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"error\",\n              message: errorCases\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-full overflow-x-auto \",\n              children: [/*#__PURE__*/_jsxDEV(\"table\", {\n                className: \"w-full table-auto\",\n                children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                  children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \" bg-[#F3F5FB] text-left \",\n                    children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"ID\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Client\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",\n                      children: \"Patient Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1043,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Assigned Provider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1049,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1052,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",\n                      children: \"Date Created\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1055,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                      className: \"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1058,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                  children: [cases === null || cases === void 0 ? void 0 : cases.map((item, index) => {\n                    var _item$assurance$assur, _item$assurance, _item$patient$full_na, _item$patient, _item$case_type;\n                    return (\n                      /*#__PURE__*/\n                      //  <a href={`/cases/detail/${item.id}`}></a>\n                      _jsxDEV(\"tr\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [\"#\", item.id]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1072,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1066,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$assurance$assur = (_item$assurance = item.assurance) === null || _item$assurance === void 0 ? void 0 : _item$assurance.assurance_name) !== null && _item$assurance$assur !== void 0 ? _item$assurance$assur : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1082,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1076,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$patient$full_na = (_item$patient = item.patient) === null || _item$patient === void 0 ? void 0 : _item$patient.full_name) !== null && _item$patient$full_na !== void 0 ? _item$patient$full_na : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1092,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1086,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: (_item$case_type = item.case_type) !== null && _item$case_type !== void 0 ? _item$case_type : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1102,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1096,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: [item.provider_services || 0, \" Providers\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1112,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1106,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black   text-xs  text-[10px]\",\n                            children: item.case_status && item.case_status.length > 0 ? item.case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: [caseStatus(stat.status_coordination), \"- \"]\n                            }, index, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1126,\n                              columnNumber: 35\n                            }, this)) : \"---\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1123,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1117,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          onClick: () => {\n                            navigate(\"/cases-list/detail/\" + item.id);\n                          },\n                          className: \" py-3 px-4 min-w-[120px] cursor-pointer  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max  \",\n                            children: formatDate(item.case_date)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1139,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1133,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                          className: \" py-3 px-4 min-w-[120px]  \",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-black  text-xs w-max flex flex-row  \",\n                            children: [/*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 detail-class\",\n                              to: \"/cases-list/detail/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",\n                                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1157,\n                                  columnNumber: 35\n                                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1162,\n                                  columnNumber: 35\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1149,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1145,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(Link, {\n                              className: \"mx-1 update-class\",\n                              to: \"/cases-list/edit/\" + item.id,\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                strokeWidth: \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  strokeLinecap: \"round\",\n                                  strokeLinejoin: \"round\",\n                                  d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1181,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1173,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1169,\n                              columnNumber: 31\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              onClick: () => {\n                                setEventType(\"delete\");\n                                setCaseId(item.id);\n                                setIsDelete(true);\n                              },\n                              className: \"mx-1 delete-class cursor-pointer\",\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1204,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1196,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1188,\n                              columnNumber: 31\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1144,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1143,\n                          columnNumber: 27\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1065,\n                        columnNumber: 25\n                      }, this)\n                    );\n                  }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                    className: \"h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1062,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxDEV(Paginate, {\n                  route: \"/dashboard?\",\n                  search: \"\",\n                  page: page,\n                  pages: pages\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Providers Map\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \" w-full  px-1 py-3 \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"py-4 px-2 shadow-1 bg-white\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" relative\",\n              children: [/*#__PURE__*/_jsxDEV(MapContainer, {\n                center: [0, 0],\n                zoom: 2,\n                style: {\n                  height: \"500px\",\n                  width: \"100%\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(TileLayer, {\n                  url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\",\n                  attribution: \"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1244,\n                  columnNumber: 19\n                }, this), casesMap && casesMap.length > 0 ? casesMap.map((caseitem, caseIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: caseitem.provider_services && caseitem.provider_services.length > 0 ? caseitem.provider_services.filter(provider => provider.provider && provider.provider.location_x && provider.provider.location_y).map((provider, index) => /*#__PURE__*/_jsxDEV(Marker, {\n                    eventHandlers: {\n                      click: () => {\n                        setIsOpenMap(true);\n                        setProviderMapSelect(provider);\n                      } // Trigger onClick event\n                    },\n                    position: [provider.provider.location_x, provider.provider.location_y],\n                    children: /*#__PURE__*/_jsxDEV(Popup, {\n                      children: [provider.provider.full_name, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1274,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1272,\n                      columnNumber: 31\n                    }, this)\n                  }, `${caseIndex}-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1259,\n                    columnNumber: 29\n                  }, this)) : null\n                }, caseIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1249,\n                  columnNumber: 21\n                }, this)) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1239,\n                columnNumber: 17\n              }, this), isOpenMap ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white shadow-1 w-full h-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" p-3 float-right \",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setIsOpenMap(false);\n                        setProviderMapSelect(null);\n                      },\n                      className: \"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-4\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"M6 18 18 6M6 6l12 12\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1302,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1294,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1287,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1286,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pt-10 py-4 px-3\",\n                    children: providerMapSelect && /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1323,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1315,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1314,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: providerMapSelect.provider.services && providerMapSelect.provider.services.length > 0 ? providerMapSelect.provider.services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"my-1\",\n                            children: [\"-\", \" \", service.service_type + (service.service_specialist !== \"\" && service.service_specialist !== null ? \": \" + service.service_specialist : \"\")]\n                          }, index, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1335,\n                            columnNumber: 39\n                          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"my-1\",\n                            children: \"No services available\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1346,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1331,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1313,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1362,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1354,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1353,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr = providerMapSelect.provider.full_name) !== null && _providerMapSelect$pr !== void 0 ? _providerMapSelect$pr : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1369,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 29\n                      }, this), providerMapSelect.provider.provider_infos && providerMapSelect.provider.provider_infos.length > 0 ? providerMapSelect.provider.provider_infos.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center text-xs my-3\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [\"Main Phone\", \"Whatsapp\", \"Billing Phone\"].includes(item.info_type) ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"size-4\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1393,\n                                columnNumber: 45\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1385,\n                              columnNumber: 43\n                            }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              \"stroke-width\": \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"size-4\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                \"stroke-linecap\": \"round\",\n                                \"stroke-linejoin\": \"round\",\n                                d: \"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1408,\n                                columnNumber: 45\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1400,\n                              columnNumber: 43\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1379,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 px-2\",\n                            children: [item.info_type, \" : \", item.info_value]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1416,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1378,\n                          columnNumber: 37\n                        }, this)\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1377,\n                        columnNumber: 35\n                      }, this)) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-5\",\n                            children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1436,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1441,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1428,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1427,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr2 = providerMapSelect.provider.address) !== null && _providerMapSelect$pr2 !== void 0 ? _providerMapSelect$pr2 : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1448,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1426,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-row items-center text-xs my-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            \"stroke-width\": \"1.5\",\n                            stroke: \"currentColor\",\n                            class: \"size-4\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              \"stroke-linecap\": \"round\",\n                              \"stroke-linejoin\": \"round\",\n                              d: \"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1462,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1454,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1453,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1 px-2\",\n                          children: (_providerMapSelect$pr3 = providerMapSelect.provider.payment_method) !== null && _providerMapSelect$pr3 !== void 0 ? _providerMapSelect$pr3 : \"---\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1469,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1452,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-black  text-xs w-max flex flex-row my-4 \",\n                        children: /*#__PURE__*/_jsxDEV(Link, {\n                          className: \"mx-1 update-class \",\n                          to: \"/providers-list/edit/\" + providerMapSelect.provider.id,\n                          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            strokeWidth: \"1.5\",\n                            stroke: \"currentColor\",\n                            className: \"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                            children: /*#__PURE__*/_jsxDEV(\"path\", {\n                              strokeLinecap: \"round\",\n                              strokeLinejoin: \"round\",\n                              d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1490,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1482,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1475,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1474,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1310,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1284,\n                columnNumber: 19\n              }, this) : null]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Are you sure you want to delete this case?\" : \"Are you sure ?\",\n        onConfirm: async () => {\n          if (eventType === \"cancel\") {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else if (eventType === \"delete\" && caseId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteCase(caseId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1541,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 264,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"EsthKQKXMuv/T4GMaJTvdzECQWI=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesListDashboard", "casesListMap", "deleteCase", "ConfirmationModal", "Paginate", "<PERSON><PERSON>", "Loader", "DefaultLayout", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Popup", "L", "Select", "getListCoordinators", "providersListDashboard", "insurancesListDashboard", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Icon", "<PERSON><PERSON><PERSON>", "prototype", "_getIconUrl", "mergeOptions", "iconRetinaUrl", "iconUrl", "shadowUrl", "DashboardScreen", "_s", "_providerMapSelect$pr", "_providerMapSelect$pr2", "_providerMapSelect$pr3", "navigate", "location", "searchParams", "page", "get", "dispatch", "providerMapSelect", "setProviderMapSelect", "isOpenMap", "setIsOpenMap", "idFilter", "setIdFilter", "ciaIdFilter", "setCiaIdFilter", "patientFilter", "set<PERSON>atient<PERSON><PERSON>er", "insuranceFilter", "setInsuranceFilter", "typeFilter", "setTypeFilter", "providerFilter", "setProviderFilter", "<PERSON><PERSON><PERSON>er", "setCoordinator<PERSON><PERSON><PERSON>", "statusFilter", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_insuranceFilter$valu", "_providerFilter$value", "_coordinationFilter$v", "params", "URLSearchParams", "set", "value", "pathname", "search", "toString", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "listCasesMap", "caseListMap", "casesMap", "loadingCasesMap", "errorCasesMap", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "_insuranceFilter$valu2", "_providerFilter$value2", "_coordinationFilter$v2", "_insuranceFilter$valu3", "_providerFilter$value3", "_coordinationFilter$v3", "parser", "result", "getResult", "browser", "name", "device", "model", "type", "_insuranceFilter$valu4", "_providerFilter$value4", "_coordinationFilter$v4", "_insuranceFilter$valu5", "_providerFilter$value5", "_coordinationFilter$v5", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "class", "strokeWidth", "placeholder", "onChange", "v", "_insuranceFilter$valu6", "_providerFilter$value6", "_coordinationFilter$v6", "_insuranceFilter$valu7", "_providerFilter$value7", "_coordinationFilter$v7", "target", "encodeURIComponent", "_insuranceFilter$valu8", "_providerFilter$value8", "_coordinationFilter$v8", "_insuranceFilter$valu9", "_providerFilter$value9", "_coordinationFilter$v9", "_insuranceFilter$valu10", "_providerFilter$value10", "_coordinationFilter$v10", "_insuranceFilter$valu11", "_providerFilter$value11", "_coordinationFilter$v11", "_insuranceFilter$valu12", "_providerFilter$value12", "_coordinationFilter$v12", "_insuranceFilter$valu13", "_providerFilter$value13", "_coordinationFilter$v13", "_insuranceFilter$valu14", "_providerFilter$value14", "_coordinationFilter$v14", "_insuranceFilter$valu15", "_providerFilter$value15", "_coordinationFilter$v15", "option", "_providerFilter$value16", "_coordinationFilter$v16", "_providerFilter$value17", "_coordinationFilter$v17", "_providerFilter$value18", "_coordinationFilter$v18", "_providerFilter$value19", "_coordinationFilter$v19", "options", "length", "map", "assurance", "id", "label", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "isSearchable", "isClearable", "classNamePrefix", "styles", "control", "base", "minHeight", "paddingLeft", "border", "borderRadius", "backgroundColor", "boxShadow", "transition", "color", "fontSize", "singleValue", "isSelected", "isFocused", "_insuranceFilter$valu16", "_coordinationFilter$v20", "_insuranceFilter$valu17", "_coordinationFilter$v21", "_insuranceFilter$valu18", "_coordinationFilter$v22", "_insuranceFilter$valu19", "_coordinationFilter$v23", "provider", "full_name", "_insuranceFilter$valu20", "_providerFilter$value20", "_insuranceFilter$valu21", "_providerFilter$value21", "_insuranceFilter$valu22", "_providerFilter$value22", "_insuranceFilter$valu23", "_providerFilter$value23", "coordinator", "onClick", "message", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "patient", "case_type", "provider_services", "case_status", "stat", "status_coordination", "case_date", "to", "route", "center", "zoom", "style", "height", "width", "url", "attribution", "caseitem", "caseIndex", "filter", "location_x", "location_y", "eventHandlers", "click", "position", "services", "service", "service_type", "service_specialist", "provider_infos", "info_type", "info_value", "address", "payment_method", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  casesListDashboard,\n  casesListMap,\n  deleteCase,\n} from \"../../redux/actions/caseActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport Paginate from \"../../components/Paginate\";\nimport Alert from \"../../components/Alert\";\nimport Loader from \"../../components/Loader\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\n\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from \"react-leaflet\";\nimport \"leaflet/dist/leaflet.css\";\nimport L from \"leaflet\";\nimport Select from \"react-select\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { providersListDashboard } from \"../../redux/actions/providerActions\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { UAParser } from \"ua-parser-js\";\n\ndelete L.Icon.Default.prototype._getIconUrl;\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl:\n    \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png\",\n  iconUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png\",\n  shadowUrl: \"https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png\",\n});\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [providerMapSelect, setProviderMapSelect] = useState(null);\n  const [isOpenMap, setIsOpenMap] = useState(false);\n\n  const [idFilter, setIdFilter] = useState(searchParams.get(\"filterid\") || \"\");\n  const [ciaIdFilter, setCiaIdFilter] = useState(\n    searchParams.get(\"filterciaid\") || \"\"\n  );\n  const [patientFilter, setPatientFilter] = useState(\n    searchParams.get(\"filterpatient\") || \"\"\n  );\n  const [insuranceFilter, setInsuranceFilter] = useState(\n    searchParams.get(\"filterinsurance\") || \"\"\n  );\n  const [typeFilter, setTypeFilter] = useState(\n    searchParams.get(\"filtertype\") || \"\"\n  );\n  const [providerFilter, setProviderFilter] = useState(\n    searchParams.get(\"filterprovider\") || \"\"\n  );\n  const [coordinationFilter, setCoordinatorFilter] = useState(\n    searchParams.get(\"filtercoordination\") || \"\"\n  );\n  const [statusFilter, setStatusrFilter] = useState(\n    searchParams.get(\"filterstatus\") || \"\"\n  );\n\n  useEffect(() => {\n    \n    const params = new URLSearchParams();\n\n    if (idFilter) params.set(\"filterid\", idFilter);\n    if (ciaIdFilter) params.set(\"filterciaid\", ciaIdFilter);\n    if (patientFilter) params.set(\"filterpatient\", patientFilter);\n    if (insuranceFilter) params.set(\"filterinsurance\", insuranceFilter.value??\"\");\n    if (typeFilter) params.set(\"filtertype\", typeFilter);\n    if (providerFilter) params.set(\"filterprovider\", providerFilter.value??\"\");\n    if (coordinationFilter)\n      params.set(\"filtercoordination\", coordinationFilter.value??\"\");\n    if (statusFilter) params.set(\"filterstatus\", statusFilter);\n\n    // Add default page\n    params.set(\"page\", \"1\");\n\n    // Update URL\n    navigate({\n      pathname: location.pathname,\n      search: params.toString(),\n    });\n  }, [\n    idFilter,\n    patientFilter,\n    statusFilter,\n    insuranceFilter,\n    providerFilter,\n    coordinationFilter,\n    typeFilter,\n    ciaIdFilter,\n    dispatch,\n    navigate,\n    location.pathname,\n  ]);\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const listCasesMap = useSelector((state) => state.caseListMap);\n  const { casesMap, loadingCasesMap, errorCasesMap } = listCasesMap;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      const parser = new UAParser();\n      const result = parser.getResult();\n\n      const browser = result.browser.name || \"Unknown browser\";\n      const device =\n        result.device.model || result.device.type || \"Unknown device\";\n\n      // get list cases (optimized for dashboard)\n      dispatch(\n        casesListDashboard(\n          page,\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get list case maps \n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      // get List Coordinators\n      dispatch(getListCoordinators(\"0\"));\n      // get providers List (optimized for dashboard)\n      dispatch(providersListDashboard(\"0\"));\n      // get Insuranes List (optimized for dashboard)\n      dispatch(insurancesListDashboard(\"0\"));\n      // \n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    \n  ]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(\n        casesListDashboard(\n          \"1\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          idFilter,\n          patientFilter,\n          statusFilter,\n          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n          providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n          typeFilter,\n          ciaIdFilter\n        )\n      );\n    }\n  }, [successCaseDelete]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString && dateString !== \"\" ? dateString : \"----\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n        </div>\n\n        \n        {/*  */}\n        <div className=\"rounded-sm border border-stroke px-2 pt-6 pb-2.5 shadow-default   dark:bg-boxdark \">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex md:flex-row flex-col justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Cases list\n            </h4>\n            <div className=\"flex flex-row justify-end\">\n              <a\n                href=\"/cases-list/add\"\n                className=\"px-4 py-3 rounded-full text-white bg-[#0388A6] flex flex-row text-xs items-center\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n\n                <div className=\"mx-2\">Create new case</div>\n              </a>\n            </div>\n          </div>\n          {/* Clean Filter Section */}\n          <div className=\"bg-gray-50 p-2 rounded-lg mb-2\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Search ID Case */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"Search Case ID...\"\n                  type=\"text\"\n                  value={idFilter}\n                  onChange={(v) => {\n                    setIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        encodeURIComponent(v.target.value),\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        encodeURIComponent(v.target.value),\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* CIA Reference */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"CIA Reference...\"\n                  type=\"text\"\n                  value={ciaIdFilter}\n                  onChange={(v) => {\n                    setCiaIdFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        v.target.value\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* Patient Name */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>\n                </div>\n                <input\n                  className=\"w-full pl-10 pr-4 py-3 rounded-lg bg-white text-sm text-gray-700 placeholder-gray-400 focus:outline-none focus:bg-blue-50 transition-all duration-200\"\n                  placeholder=\"Patient Name...\"\n                  type=\"text\"\n                  value={patientFilter}\n                  onChange={(v) => {\n                    setPatientFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        v.target.value,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                />\n              </div>\n\n              {/* Case Type */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <select\n                  value={typeFilter}\n                  onChange={(v) => {\n                    setTypeFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        statusFilter,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        v.target.value,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"w-full pl-10 pr-8 py-3 rounded-lg bg-white text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\"\n                >\n                  <option value=\"\">All Types</option>\n                  <option value=\"Medical\">Medical</option>\n                  <option value=\"Technical\">Technical</option>\n                </select>\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n          {/* Advanced Filters Row */}\n          <div className=\"bg-gray-50 p-6 rounded-lg mb-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Status Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                </div>\n                <select\n                  value={statusFilter}\n                  onChange={(v) => {\n                    setStatusrFilter(v.target.value);\n                    dispatch(\n                      casesListDashboard(\n                        \"1\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                    dispatch(\n                      casesListMap(\n                        \"0\",\n                        \"\",\n                        idFilter,\n                        patientFilter,\n                        v.target.value,\n                        insuranceFilter !== \"\"\n                          ? insuranceFilter.value ?? \"\"\n                          : \"\",\n                        providerFilter !== \"\" ? providerFilter.value ?? \"\" : \"\",\n                        coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                        typeFilter,\n                        ciaIdFilter\n                      )\n                    );\n                  }}\n                  className=\"w-full pl-10 pr-8 py-[14px] rounded-lg bg-gray-50 text-sm text-gray-700 focus:outline-none focus:bg-blue-50 transition-all duration-200 appearance-none cursor-pointer\"\n                >\n                  <option value=\"\">All Status</option>\n                  <option value=\"pending-coordination\">Pending Coordination</option>\n                  <option value=\"coordinated-missing-m-r\">Coordinated, Missing M.R.</option>\n                  <option value=\"coordinated-missing-invoice\">Coordinated, Missing Invoice</option>\n                  <option value=\"waiting-for-insurance-authorization\">Waiting for Insurance Authorization</option>\n                  <option value=\"coordinated-patient-not-seen-yet\">Coordinated, Patient not seen yet</option>\n                  <option value=\"fully-coordinated\">Fully Coordinated</option>\n                  <option value=\"coordinated-missing-payment\">Coordinated, Missing Payment</option>\n                  <option value=\"coordination-fee\">Coordination Fee</option>\n                  <option value=\"failed\">Failed</option>\n                </select>\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                  <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </div>\n\n              {/* Insurance Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                  </svg>\n                </div>\n                <Select\n                  value={insuranceFilter}\n                  onChange={(option) => {\n                    setInsuranceFilter(option);\n                    if (option && option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          option.value,\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          \"\",\n                          providerFilter !== \"\"\n                            ? providerFilter.value ?? \"\"\n                            : \"\",\n                          coordinationFilter!==\"\"? coordinationFilter.value??\"\":\"\" ,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={insurances && insurances.length > 0 ? insurances.map((assurance) => ({\n                    value: assurance.id,\n                    label: assurance.assurance_name || \"\",\n                  })) : []}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  placeholder=\"Select Insurance...\"\n                  isSearchable\n                  isClearable\n                  className=\"react-select-container\"\n                  classNamePrefix=\"react-select\"\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      minHeight: '48px',\n                      paddingLeft: '32px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      backgroundColor: '#ffffff',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#eff6ff',\n                      },\n                      transition: 'all 0.2s',\n                    }),\n                    placeholder: (base) => ({\n                      ...base,\n                      color: '#9ca3af',\n                      fontSize: '14px',\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      color: '#374151',\n                      fontSize: '14px',\n                    }),\n                    option: (base, state) => ({\n                      ...base,\n                      backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                      color: state.isSelected ? 'white' : '#374151',\n                      fontSize: '14px',\n                    }),\n                  }}\n                />\n              </div>\n\n              {/* Provider Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <Select\n                  value={providerFilter}\n                  onChange={(option) => {\n                    setProviderFilter(option);\n                    if (option && option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          option.value,\n                          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          option.value,\n                          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          \"\",\n                          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          \"\",\n                          coordinationFilter !== \"\" ? coordinationFilter.value ?? \"\" : \"\",\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={providers && providers.length > 0 ? providers.map((provider) => ({\n                    value: provider.id,\n                    label: provider.full_name || \"\",\n                  })) : []}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  placeholder=\"Select Provider...\"\n                  isSearchable\n                  isClearable\n                  className=\"react-select-container\"\n                  classNamePrefix=\"react-select\"\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      minHeight: '48px',\n                      paddingLeft: '32px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      backgroundColor: '#ffffff',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#eff6ff',\n                      },\n                      transition: 'all 0.2s',\n                    }),\n                    placeholder: (base) => ({\n                      ...base,\n                      color: '#9ca3af',\n                      fontSize: '14px',\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      color: '#374151',\n                      fontSize: '14px',\n                    }),\n                    option: (base, state) => ({\n                      ...base,\n                      backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                      color: state.isSelected ? 'white' : '#374151',\n                      fontSize: '14px',\n                    }),\n                  }}\n                />\n              </div>\n\n{/* Provider Filter */}\n              <div className=\"relative group\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-20\">\n                  <svg className=\"h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <Select\n                  value={coordinationFilter}\n                  onChange={(option) => {\n                    setCoordinatorFilter(option);\n                    if (option && option.value) {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                           providerFilter!==\"\"?providerFilter.value??\"\":\"\",\n                          option.value,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                           providerFilter!==\"\"?providerFilter.value??\"\":\"\",\n                          option.value,\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    } else {\n                      dispatch(\n                        casesListDashboard(\n                          \"1\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                           providerFilter!==\"\"?providerFilter.value??\"\":\"\",\n                          '',\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                      dispatch(\n                        casesListMap(\n                          \"0\",\n                          \"\",\n                          idFilter,\n                          patientFilter,\n                          statusFilter,\n                          insuranceFilter !== \"\" ? insuranceFilter.value ?? \"\" : \"\",\n                          providerFilter!==\"\"?providerFilter.value??\"\":\"\",\n                          '',\n                          typeFilter,\n                          ciaIdFilter\n                        )\n                      );\n                    }\n                  }}\n                  options={coordinators && coordinators.length > 0 ? coordinators.map((coordinator) => ({\n                    value: coordinator.id,\n                    label: coordinator.full_name || \"\",\n                  })) : []}\n                  filterOption={(option, inputValue) =>\n                    option.label\n                      .toLowerCase()\n                      .includes(inputValue.toLowerCase())\n                  }\n                  placeholder=\"Select Coordinator...\"\n                  isSearchable\n                  isClearable\n                  className=\"react-select-container\"\n                  classNamePrefix=\"react-select\"\n                  styles={{\n                    control: (base, state) => ({\n                      ...base,\n                      minHeight: '48px',\n                      paddingLeft: '32px',\n                      border: 'none',\n                      borderRadius: '8px',\n                      backgroundColor: '#ffffff',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#eff6ff',\n                      },\n                      transition: 'all 0.2s',\n                    }),\n                    placeholder: (base) => ({\n                      ...base,\n                      color: '#9ca3af',\n                      fontSize: '14px',\n                    }),\n                    singleValue: (base) => ({\n                      ...base,\n                      color: '#374151',\n                      fontSize: '14px',\n                    }),\n                    option: (base, state) => ({\n                      ...base,\n                      backgroundColor: state.isSelected ? '#3b82f6' : state.isFocused ? '#eff6ff' : 'white',\n                      color: state.isSelected ? 'white' : '#374151',\n                      fontSize: '14px',\n                    }),\n                  }}\n                />\n              </div>\n              {/* Reset Button */}\n              <div className=\"flex items-end\">\n                <button\n                  onClick={() => {\n                    setIdFilter(\"\");\n                    setInsuranceFilter(\"\");\n                    setProviderFilter(\"\");\n                    setCoordinatorFilter(\"\");\n                    setStatusrFilter(\"\");\n                    setTypeFilter(\"\");\n                    setPatientFilter(\"\");\n                    setCiaIdFilter(\"\");\n\n                    dispatch(\n        casesListDashboard(\n          \"1\",\n          \"\",\n          \"\",\n          \"\",\n          \"\",\n           \"\",\n           \"\",\n           \"\",\n          \"\",\n          \"\"\n        )\n      );\n      // get list case maps \n      dispatch(\n        casesListMap(\n          \"0\",\n          \"\",\n          \"\",\n          \"\",\n          \"\",\n           \"\",\n           \"\",\n           \"\",\n          \"\",\n          \"\"\n        )\n      );\n                  }}\n                  className=\"w-full flex items-center justify-center gap-2 bg-danger hover:bg-danger text-white px-4 py-3 rounded-lg font-medium text-sm transition-all duration-200\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-4 h-4\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99\"\n                    />\n                  </svg>\n                  Reset Filters\n                </button>\n              </div>\n            </div>\n          </div>\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              {loadingCases ? (\n                <Loader />\n              ) : errorCases ? (\n                <Alert type=\"error\" message={errorCases} />\n              ) : (\n                <div className=\"max-w-full overflow-x-auto \">\n                  <table className=\"w-full table-auto\">\n                    <thead>\n                      <tr className=\" bg-[#F3F5FB] text-left \">\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          ID\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Client\n                        </th>\n                        <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                          Patient Name\n                        </th>\n                        <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Type\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Assigned Provider\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Status\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                          Date Created\n                        </th>\n                        <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                      </tr>\n                    </thead>\n                    {/*  */}\n                    <tbody>\n                      {cases?.map((item, index) => (\n                        //  <a href={`/cases/detail/${item.id}`}></a>\n                        <tr key={index}>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              #{item.id}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.assurance?.assurance_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.patient?.full_name ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {item.case_type ?? \"---\"}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {/* {item.provider?.full_name ?? \"---\"} */}\n                              {item.provider_services || 0} Providers\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black   text-xs  text-[10px]\">\n                              {item.case_status && item.case_status.length > 0 ? (\n                                item.case_status.map((stat, index) => (\n                                  <span key={index}>{caseStatus(stat.status_coordination)}- </span>\n                                ))\n                              ) : (\n                                \"---\"\n                              )}\n                            </p>\n                          </td>\n                          <td\n                            onClick={() => {\n                              navigate(\"/cases-list/detail/\" + item.id);\n                            }}\n                            className=\" py-3 px-4 min-w-[120px] cursor-pointer  \"\n                          >\n                            <p className=\"text-black  text-xs w-max  \">\n                              {formatDate(item.case_date)}\n                            </p>\n                          </td>\n                          <td className=\" py-3 px-4 min-w-[120px]  \">\n                            <p className=\"text-black  text-xs w-max flex flex-row  \">\n                              <Link\n                                className=\"mx-1 detail-class\"\n                                to={\"/cases-list/detail/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                </svg>\n                              </Link>\n                              <Link\n                                className=\"mx-1 update-class\"\n                                to={\"/cases-list/edit/\" + item.id}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                              <div\n                                onClick={() => {\n                                  setEventType(\"delete\");\n                                  setCaseId(item.id);\n                                  setIsDelete(true);\n                                }}\n                                className=\"mx-1 delete-class cursor-pointer\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                  />\n                                </svg>\n                              </div>\n                            </p>\n                          </td>\n                        </tr>\n                      ))}\n                      <tr className=\"h-5\"></tr>\n                    </tbody>\n                  </table>\n                  <div className=\"\">\n                    <Paginate\n                      route={\"/dashboard?\"}\n                      search={\"\"}\n                      page={page}\n                      pages={pages}\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Providers Map \n            </h4>\n          </div>\n\n          <div className=\" w-full  px-1 py-3 \">\n            <div className=\"py-4 px-2 shadow-1 bg-white\">\n              <div className=\" relative\">\n                <MapContainer\n                  center={[0, 0]}\n                  zoom={2}\n                  style={{ height: \"500px\", width: \"100%\" }}\n                >\n                  <TileLayer\n                    url=\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                  />\n                  {casesMap && casesMap.length > 0 ? casesMap.map((caseitem, caseIndex) => (\n                    <div key={caseIndex}>\n                      {caseitem.provider_services && caseitem.provider_services.length > 0 ? (\n                        caseitem.provider_services\n                          .filter(\n                            (provider) =>\n                              provider.provider &&\n                              provider.provider.location_x &&\n                              provider.provider.location_y\n                          )\n                          .map((provider, index) => (\n                            <Marker\n                              eventHandlers={{\n                                click: () => {\n                                  setIsOpenMap(true);\n                                  setProviderMapSelect(provider);\n                                }, // Trigger onClick event\n                              }}\n                              key={`${caseIndex}-${index}`}\n                              position={[\n                                provider.provider.location_x,\n                                provider.provider.location_y,\n                              ]}\n                            >\n                              <Popup>\n                                {provider.provider.full_name}\n                                <br />\n                              </Popup>\n                            </Marker>\n                          ))\n                      ) : null}\n                    </div>\n                  )) : null}\n                  \n                </MapContainer>\n                {isOpenMap ? (\n                  <div className=\" absolute top-0 left-0 z-99999  p-2 md:w-1/3 w-2/3 h-full \">\n                    <div className=\"bg-white shadow-1 w-full h-full\">\n                      <div className=\" p-3 float-right \">\n                        <button\n                          onClick={() => {\n                            setIsOpenMap(false);\n                            setProviderMapSelect(null);\n                          }}\n                          className=\"rounded-full p-1 bg-danger shadow-1 text-white flex items-center w-max \"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"pt-10 py-4 px-3\">\n                        {providerMapSelect && (\n                          <div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                                  />\n                                </svg>\n                              </div>\n\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.services && providerMapSelect.provider.services.length > 0 ? (\n                                  providerMapSelect.provider.services.map(\n                                    (service, index) => (\n                                      <div key={index} className=\"my-1\">\n                                        -{\" \"}\n                                        {service.service_type +\n                                          (service.service_specialist !== \"\" &&\n                                          service.service_specialist !== null\n                                            ? \": \" + service.service_specialist\n                                            : \"\")}\n                                      </div>\n                                    )\n                                  )\n                                ) : (\n                                  <div className=\"my-1\">No services available</div>\n                                )}\n\n                              </div>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.full_name ?? \"---\"}\n                              </div>\n                            </div>\n                            {/*  */}\n                            {providerMapSelect.provider.provider_infos && providerMapSelect.provider.provider_infos.length > 0 ? (\n                              providerMapSelect.provider.provider_infos.map(\n                                (item, index) => (\n                                  <div key={index}>\n                                    <div className=\"flex flex-row items-center text-xs my-3\">\n                                      <div>\n                                        {[\n                                          \"Main Phone\",\n                                          \"Whatsapp\",\n                                          \"Billing Phone\",\n                                        ].includes(item.info_type) ? (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                            />\n                                          </svg>\n                                        ) : (\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"size-4\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75\"\n                                            />\n                                          </svg>\n                                        )}\n                                      </div>\n                                      <div className=\"flex-1 px-2\">\n                                        {item.info_type} : {item.info_value}\n                                      </div>\n                                    </div>\n                                  </div>\n                                )\n                              )\n                            ) : null}\n                            \n                            {/*  */}\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                  />\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.address ?? \"---\"}\n                              </div>\n                            </div>\n                            <div className=\"flex flex-row items-center text-xs my-3\">\n                              <div>\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z\"\n                                  />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-2\">\n                                {providerMapSelect.provider.payment_method ??\n                                  \"---\"}\n                              </div>\n                            </div>\n                            <p className=\"text-black  text-xs w-max flex flex-row my-4 \">\n                              <Link\n                                className=\"mx-1 update-class \"\n                                to={\n                                  \"/providers-list/edit/\" +\n                                  providerMapSelect.provider.id\n                                }\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  strokeWidth=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  className=\"w-8 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                >\n                                  <path\n                                    strokeLinecap=\"round\"\n                                    strokeLinejoin=\"round\"\n                                    d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                  />\n                                </svg>\n                              </Link>\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ) : null}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Are you sure you want to delete this case?\"\n              : \"Are you sure ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SACEC,kBAAkB,EAClBC,YAAY,EACZC,UAAU,QACL,iCAAiC;AACxC,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,aAAa,MAAM,6BAA6B;AAEvD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,QAAQ,eAAe;AACtE,OAAO,0BAA0B;AACjC,OAAOC,CAAC,MAAM,SAAS;AACvB,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,OAAOP,CAAC,CAACQ,IAAI,CAACC,OAAO,CAACC,SAAS,CAACC,WAAW;AAC3CX,CAAC,CAACQ,IAAI,CAACC,OAAO,CAACG,YAAY,CAAC;EAC1BC,aAAa,EACX,gEAAgE;EAClEC,OAAO,EAAE,6DAA6D;EACtEC,SAAS,EAAE;AACb,CAAC,CAAC;AAEF,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsC,YAAY,CAAC,GAAGpC,eAAe,CAAC,CAAC;EACxC,MAAMqC,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC0C,YAAY,CAACE,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;EAC5E,MAAM,CAACQ,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAC5C0C,YAAY,CAACE,GAAG,CAAC,aAAa,CAAC,IAAI,EACrC,CAAC;EACD,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAChD0C,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC,IAAI,EACvC,CAAC;EACD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CACpD0C,YAAY,CAACE,GAAG,CAAC,iBAAiB,CAAC,IAAI,EACzC,CAAC;EACD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAC1C0C,YAAY,CAACE,GAAG,CAAC,YAAY,CAAC,IAAI,EACpC,CAAC;EACD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAClD0C,YAAY,CAACE,GAAG,CAAC,gBAAgB,CAAC,IAAI,EACxC,CAAC;EACD,MAAM,CAACkB,kBAAkB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CACzD0C,YAAY,CAACE,GAAG,CAAC,oBAAoB,CAAC,IAAI,EAC5C,CAAC;EACD,MAAM,CAACoB,YAAY,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAC/C0C,YAAY,CAACE,GAAG,CAAC,cAAc,CAAC,IAAI,EACtC,CAAC;EAED7C,SAAS,CAAC,MAAM;IAAA,IAAAmE,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAEd,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEpC,IAAIpB,QAAQ,EAAEmB,MAAM,CAACE,GAAG,CAAC,UAAU,EAAErB,QAAQ,CAAC;IAC9C,IAAIE,WAAW,EAAEiB,MAAM,CAACE,GAAG,CAAC,aAAa,EAAEnB,WAAW,CAAC;IACvD,IAAIE,aAAa,EAAEe,MAAM,CAACE,GAAG,CAAC,eAAe,EAAEjB,aAAa,CAAC;IAC7D,IAAIE,eAAe,EAAEa,MAAM,CAACE,GAAG,CAAC,iBAAiB,GAAAL,qBAAA,GAAEV,eAAe,CAACgB,KAAK,cAAAN,qBAAA,cAAAA,qBAAA,GAAE,EAAE,CAAC;IAC7E,IAAIR,UAAU,EAAEW,MAAM,CAACE,GAAG,CAAC,YAAY,EAAEb,UAAU,CAAC;IACpD,IAAIE,cAAc,EAAES,MAAM,CAACE,GAAG,CAAC,gBAAgB,GAAAJ,qBAAA,GAAEP,cAAc,CAACY,KAAK,cAAAL,qBAAA,cAAAA,qBAAA,GAAE,EAAE,CAAC;IAC1E,IAAIL,kBAAkB,EACpBO,MAAM,CAACE,GAAG,CAAC,oBAAoB,GAAAH,qBAAA,GAAEN,kBAAkB,CAACU,KAAK,cAAAJ,qBAAA,cAAAA,qBAAA,GAAE,EAAE,CAAC;IAChE,IAAIJ,YAAY,EAAEK,MAAM,CAACE,GAAG,CAAC,cAAc,EAAEP,YAAY,CAAC;;IAE1D;IACAK,MAAM,CAACE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;;IAEvB;IACA/B,QAAQ,CAAC;MACPiC,QAAQ,EAAEhC,QAAQ,CAACgC,QAAQ;MAC3BC,MAAM,EAAEL,MAAM,CAACM,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CACDzB,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,EACfI,cAAc,EACdE,kBAAkB,EAClBJ,UAAU,EACVN,WAAW,EACXP,QAAQ,EACRL,QAAQ,EACRC,QAAQ,CAACgC,QAAQ,CAClB,CAAC;EAEF,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkF,MAAM,EAAEC,SAAS,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMoF,SAAS,GAAGlF,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGrF,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,YAAY,GAAG3F,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAACS,WAAW,CAAC;EAC9D,MAAM;IAAEC,QAAQ;IAAEC,eAAe;IAAEC;EAAc,CAAC,GAAGJ,YAAY;EAEjE,MAAMK,UAAU,GAAGhG,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAAC5E,UAAU,CAAC;EAC3D,MAAM;IAAE0F,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,aAAa,GAAGpG,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAACkB,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,cAAc,GAAGzG,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAACuB,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAG9G,WAAW,CAAEmF,KAAK,IAAKA,KAAK,CAAC4B,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EAEpBtH,SAAS,CAAC,MAAM;IACd,IAAI,CAACuF,QAAQ,EAAE;MACb9C,QAAQ,CAAC6E,QAAQ,CAAC;IACpB,CAAC,MAAM;MAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACL,MAAMC,MAAM,GAAG,IAAIpG,QAAQ,CAAC,CAAC;MAC7B,MAAMqG,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;MAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;MACxD,MAAMC,MAAM,GACVJ,MAAM,CAACI,MAAM,CAACC,KAAK,IAAIL,MAAM,CAACI,MAAM,CAACE,IAAI,IAAI,gBAAgB;;MAE/D;MACAtF,QAAQ,CACNtC,kBAAkB,CAChBoC,IAAI,EACJ,EAAE,EACFO,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA8D,sBAAA,GAAG9D,eAAe,CAACgB,KAAK,cAAA8C,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzD1D,cAAc,KAAK,EAAE,IAAA2D,sBAAA,GAAG3D,cAAc,CAACY,KAAK,cAAA+C,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDzD,kBAAkB,KAAK,EAAE,IAAA0D,sBAAA,GAAG1D,kBAAkB,CAACU,KAAK,cAAAgD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EAC/D9D,UAAU,EACVN,WACF,CACF,CAAC;MACD;MACAP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAiE,sBAAA,GAAGjE,eAAe,CAACgB,KAAK,cAAAiD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzD7D,cAAc,KAAK,EAAE,IAAA8D,sBAAA,GAAG9D,cAAc,CAACY,KAAK,cAAAkD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD5D,kBAAkB,KAAK,EAAE,IAAA6D,sBAAA,GAAG7D,kBAAkB,CAACU,KAAK,cAAAmD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EAC/DjE,UAAU,EACVN,WACF,CACF,CAAC;MACD;MACAP,QAAQ,CAACxB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClC;MACAwB,QAAQ,CAACvB,sBAAsB,CAAC,GAAG,CAAC,CAAC;MACrC;MACAuB,QAAQ,CAACtB,uBAAuB,CAAC,GAAG,CAAC,CAAC;MACtC;IACF;EACF,CAAC,EAAE,CACDiB,QAAQ,EACR8C,QAAQ,EACRzC,QAAQ,EACRF,IAAI,CAEL,CAAC;EAEF5C,SAAS,CAAC,MAAM;IACd,IAAIsG,iBAAiB,EAAE;MAAA,IAAA+B,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACrB5F,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA4E,sBAAA,GAAG5E,eAAe,CAACgB,KAAK,cAAA4D,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzDxE,cAAc,KAAK,EAAE,IAAAyE,sBAAA,GAAGzE,cAAc,CAACY,KAAK,cAAA6D,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDvE,kBAAkB,KAAK,EAAE,IAAAwE,sBAAA,GAAGxE,kBAAkB,CAACU,KAAK,cAAA8D,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EAC/D5E,UAAU,EACVN,WACF,CACF,CAAC;MACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA+E,sBAAA,GAAG/E,eAAe,CAACgB,KAAK,cAAA+D,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACzD3E,cAAc,KAAK,EAAE,IAAA4E,sBAAA,GAAG5E,cAAc,CAACY,KAAK,cAAAgE,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD1E,kBAAkB,KAAK,EAAE,IAAA2E,sBAAA,GAAG3E,kBAAkB,CAACU,KAAK,cAAAiE,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EAC/D/E,UAAU,EACVN,WACF,CACF,CAAC;IACH;EACF,CAAC,EAAE,CAACiD,iBAAiB,CAAC,CAAC;EAEvB,MAAMqC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU,IAAIA,UAAU,KAAK,EAAE,GAAGA,UAAU,GAAG,MAAM;IAC9D;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,oBACEzH,OAAA,CAACZ,aAAa;IAAAsI,QAAA,eACZ1H,OAAA;MAAA0H,QAAA,gBACE1H,OAAA;QAAK2H,SAAS,EAAC,yCAAyC;QAAAD,QAAA,eAEtD1H,OAAA;UAAG4H,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB1H,OAAA;YAAK2H,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D1H,OAAA;cACE6H,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB1H,OAAA;gBACEiI,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNvI,OAAA;cAAM2H,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAINvI,OAAA;QAAK2H,SAAS,EAAC,oFAAoF;QAAAD,QAAA,gBACjG1H,OAAA;UAAK2H,SAAS,EAAC,uEAAuE;UAAAD,QAAA,gBACpF1H,OAAA;YAAI2H,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvI,OAAA;YAAK2H,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC1H,OAAA;cACE4H,IAAI,EAAC,iBAAiB;cACtBD,SAAS,EAAC,mFAAmF;cAAAD,QAAA,gBAE7F1H,OAAA;gBACE6H,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBQ,KAAK,EAAC,QAAQ;gBAAAd,QAAA,eAEd1H,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBmI,CAAC,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvI,OAAA;gBAAK2H,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvI,OAAA;UAAK2H,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1H,OAAA;YAAK2H,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBAEnE1H,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,eACnF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAA6C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBACE2H,SAAS,EAAC,uJAAuJ;gBACjKe,WAAW,EAAC,mBAAmB;gBAC/BjC,IAAI,EAAC,MAAM;gBACX3D,KAAK,EAAEtB,QAAS;gBAChBmH,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBACfzH,WAAW,CAACmH,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC;kBAC3B3B,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACFuK,kBAAkB,CAACR,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC,EAClClB,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA+G,sBAAA,GAClB/G,eAAe,CAACgB,KAAK,cAAA+F,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN3G,cAAc,KAAK,EAAE,IAAA4G,sBAAA,GAAG5G,cAAc,CAACY,KAAK,cAAAgG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD1G,kBAAkB,KAAG,EAAE,IAAA2G,sBAAA,GAAE3G,kBAAkB,CAACU,KAAK,cAAAiG,sBAAA,cAAAA,sBAAA,GAAE,EAAE,GAAC,EAAE,EACxD/G,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACFsK,kBAAkB,CAACR,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC,EAClClB,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAkH,sBAAA,GAClBlH,eAAe,CAACgB,KAAK,cAAAkG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACN9G,cAAc,KAAK,EAAE,IAAA+G,sBAAA,GAAG/G,cAAc,CAACY,KAAK,cAAAmG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvD7G,kBAAkB,KAAG,EAAE,IAAA8G,sBAAA,GAAE9G,kBAAkB,CAACU,KAAK,cAAAoG,sBAAA,cAAAA,sBAAA,GAAE,EAAE,GAAC,EAAE,EACxDlH,UAAU,EACVN,WACF,CACF,CAAC;gBACH;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,eACnF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAsH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBACE2H,SAAS,EAAC,uJAAuJ;gBACjKe,WAAW,EAAC,kBAAkB;gBAC9BjC,IAAI,EAAC,MAAM;gBACX3D,KAAK,EAAEpB,WAAY;gBACnBiH,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAS,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBACf/H,cAAc,CAACiH,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC;kBAC9B3B,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAuH,sBAAA,GAClBvH,eAAe,CAACgB,KAAK,cAAAuG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNnH,cAAc,KAAK,EAAE,IAAAoH,sBAAA,GAAGpH,cAAc,CAACY,KAAK,cAAAwG,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDlH,kBAAkB,KAAG,EAAE,IAAAmH,sBAAA,GAAEnH,kBAAkB,CAACU,KAAK,cAAAyG,sBAAA,cAAAA,sBAAA,GAAE,EAAE,GAAC,EAAE,EACxDvH,UAAU,EACV4G,CAAC,CAACO,MAAM,CAACrG,KACX,CACF,CAAC;kBACD3B,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA0H,sBAAA,GAClB1H,eAAe,CAACgB,KAAK,cAAA0G,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAC3B,EAAE,EACNtH,cAAc,KAAK,EAAE,IAAAuH,sBAAA,GAAGvH,cAAc,CAACY,KAAK,cAAA2G,sBAAA,cAAAA,sBAAA,GAAI,EAAE,GAAG,EAAE,EACvDrH,kBAAkB,KAAG,EAAE,IAAAsH,sBAAA,GAAEtH,kBAAkB,CAACU,KAAK,cAAA4G,sBAAA,cAAAA,sBAAA,GAAE,EAAE,GAAC,EAAE,EACxD1H,UAAU,EACV4G,CAAC,CAACO,MAAM,CAACrG,KACX,CACF,CAAC;gBACH;cAAE;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,sEAAsE;gBAAAD,QAAA,eACnF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAqE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBACE2H,SAAS,EAAC,uJAAuJ;gBACjKe,WAAW,EAAC,iBAAiB;gBAC7BjC,IAAI,EAAC,MAAM;gBACX3D,KAAK,EAAElB,aAAc;gBACrB+G,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAe,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACfnI,gBAAgB,CAAC+G,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC;kBAChC3B,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRoH,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdR,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA6H,uBAAA,GAClB7H,eAAe,CAACgB,KAAK,cAAA6G,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNzH,cAAc,KAAK,EAAE,IAAA0H,uBAAA,GAAG1H,cAAc,CAACY,KAAK,cAAA8G,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDxH,kBAAkB,KAAG,EAAE,IAAAyH,uBAAA,GAAEzH,kBAAkB,CAACU,KAAK,cAAA+G,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxD7H,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRoH,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdR,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAgI,uBAAA,GAClBhI,eAAe,CAACgB,KAAK,cAAAgH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACN5H,cAAc,KAAK,EAAE,IAAA6H,uBAAA,GAAG7H,cAAc,CAACY,KAAK,cAAAiH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvD3H,kBAAkB,KAAG,EAAE,IAAA4H,uBAAA,GAAE5H,kBAAkB,CAACU,KAAK,cAAAkH,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDhI,UAAU,EACVN,WACF,CACF,CAAC;gBACH;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,eACxF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAwJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7N;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBACE8C,KAAK,EAAEd,UAAW;gBAClB2G,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAAqB,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACfrI,aAAa,CAAC2G,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC;kBAC7B3B,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAmI,uBAAA,GAClBnI,eAAe,CAACgB,KAAK,cAAAmH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACN/H,cAAc,KAAK,EAAE,IAAAgI,uBAAA,GAAGhI,cAAc,CAACY,KAAK,cAAAoH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvD9H,kBAAkB,KAAG,EAAE,IAAA+H,uBAAA,GAAE/H,kBAAkB,CAACU,KAAK,cAAAqH,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDvB,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdpB,WACF,CACF,CAAC;kBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAsI,uBAAA,GAClBtI,eAAe,CAACgB,KAAK,cAAAsH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNlI,cAAc,KAAK,EAAE,IAAAmI,uBAAA,GAAGnI,cAAc,CAACY,KAAK,cAAAuH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDjI,kBAAkB,KAAG,EAAE,IAAAkI,uBAAA,GAAElI,kBAAkB,CAACU,KAAK,cAAAwH,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxD1B,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdpB,WACF,CACF,CAAC;gBACH,CAAE;gBACFiG,SAAS,EAAC,iKAAiK;gBAAAD,QAAA,gBAE3K1H,OAAA;kBAAQ8C,KAAK,EAAC,EAAE;kBAAA4E,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCvI,OAAA;kBAAQ8C,KAAK,EAAC,SAAS;kBAAA4E,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCvI,OAAA;kBAAQ8C,KAAK,EAAC,WAAW;kBAAA4E,QAAA,EAAC;gBAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACTvI,OAAA;gBAAK2H,SAAS,EAAC,uEAAuE;gBAAAD,QAAA,eACpF1H,OAAA;kBAAK2H,SAAS,EAAC,uBAAuB;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC1F1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvI,OAAA;UAAK2H,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1H,OAAA;YAAK2H,SAAS,EAAC,sDAAsD;YAAAD,QAAA,gBAEnE1H,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,eACxF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAA+C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA;gBACE8C,KAAK,EAAER,YAAa;gBACpBqG,QAAQ,EAAGC,CAAC,IAAK;kBAAA,IAAA2B,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBACfrI,gBAAgB,CAACqG,CAAC,CAACO,MAAM,CAACrG,KAAK,CAAC;kBAChC3B,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbgH,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdhB,eAAe,KAAK,EAAE,IAAAyI,uBAAA,GAClBzI,eAAe,CAACgB,KAAK,cAAAyH,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNrI,cAAc,KAAK,EAAE,IAAAsI,uBAAA,GAAGtI,cAAc,CAACY,KAAK,cAAA0H,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDpI,kBAAkB,KAAG,EAAE,IAAAqI,uBAAA,GAAErI,kBAAkB,CAACU,KAAK,cAAA2H,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDzI,UAAU,EACVN,WACF,CACF,CAAC;kBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbgH,CAAC,CAACO,MAAM,CAACrG,KAAK,EACdhB,eAAe,KAAK,EAAE,IAAA4I,uBAAA,GAClB5I,eAAe,CAACgB,KAAK,cAAA4H,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC3B,EAAE,EACNxI,cAAc,KAAK,EAAE,IAAAyI,uBAAA,GAAGzI,cAAc,CAACY,KAAK,cAAA6H,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACvDvI,kBAAkB,KAAG,EAAE,IAAAwI,uBAAA,GAAExI,kBAAkB,CAACU,KAAK,cAAA8H,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxD5I,UAAU,EACVN,WACF,CACF,CAAC;gBACH,CAAE;gBACFiG,SAAS,EAAC,wKAAwK;gBAAAD,QAAA,gBAElL1H,OAAA;kBAAQ8C,KAAK,EAAC,EAAE;kBAAA4E,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCvI,OAAA;kBAAQ8C,KAAK,EAAC,sBAAsB;kBAAA4E,QAAA,EAAC;gBAAoB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClEvI,OAAA;kBAAQ8C,KAAK,EAAC,yBAAyB;kBAAA4E,QAAA,EAAC;gBAAyB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1EvI,OAAA;kBAAQ8C,KAAK,EAAC,6BAA6B;kBAAA4E,QAAA,EAAC;gBAA4B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjFvI,OAAA;kBAAQ8C,KAAK,EAAC,qCAAqC;kBAAA4E,QAAA,EAAC;gBAAmC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChGvI,OAAA;kBAAQ8C,KAAK,EAAC,kCAAkC;kBAAA4E,QAAA,EAAC;gBAAiC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3FvI,OAAA;kBAAQ8C,KAAK,EAAC,mBAAmB;kBAAA4E,QAAA,EAAC;gBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5DvI,OAAA;kBAAQ8C,KAAK,EAAC,6BAA6B;kBAAA4E,QAAA,EAAC;gBAA4B;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjFvI,OAAA;kBAAQ8C,KAAK,EAAC,kBAAkB;kBAAA4E,QAAA,EAAC;gBAAgB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1DvI,OAAA;kBAAQ8C,KAAK,EAAC,QAAQ;kBAAA4E,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACTvI,OAAA;gBAAK2H,SAAS,EAAC,uEAAuE;gBAAAD,QAAA,eACpF1H,OAAA;kBAAK2H,SAAS,EAAC,uBAAuB;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC1F1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,eACxF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAAgM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA,CAACN,MAAM;gBACLoD,KAAK,EAAEhB,eAAgB;gBACvB6G,QAAQ,EAAGkC,MAAM,IAAK;kBACpB9I,kBAAkB,CAAC8I,MAAM,CAAC;kBAC1B,IAAIA,MAAM,IAAIA,MAAM,CAAC/H,KAAK,EAAE;oBAAA,IAAAgI,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBAC1B9J,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZuI,MAAM,CAAC/H,KAAK,EACZZ,cAAc,KAAK,EAAE,IAAA4I,uBAAA,GACjB5I,cAAc,CAACY,KAAK,cAAAgI,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACN1I,kBAAkB,KAAG,EAAE,IAAA2I,uBAAA,GAAE3I,kBAAkB,CAACU,KAAK,cAAAiI,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxD/I,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZuI,MAAM,CAAC/H,KAAK,EACZZ,cAAc,KAAK,EAAE,IAAA8I,uBAAA,GACjB9I,cAAc,CAACY,KAAK,cAAAkI,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACN5I,kBAAkB,KAAG,EAAE,IAAA6I,uBAAA,GAAE7I,kBAAkB,CAACU,KAAK,cAAAmI,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDjJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH,CAAC,MAAM;oBAAA,IAAAwJ,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBACLlK,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZ,EAAE,EACFJ,cAAc,KAAK,EAAE,IAAAgJ,uBAAA,GACjBhJ,cAAc,CAACY,KAAK,cAAAoI,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACN9I,kBAAkB,KAAG,EAAE,IAAA+I,uBAAA,GAAE/I,kBAAkB,CAACU,KAAK,cAAAqI,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDnJ,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZ,EAAE,EACFJ,cAAc,KAAK,EAAE,IAAAkJ,uBAAA,GACjBlJ,cAAc,CAACY,KAAK,cAAAsI,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAC1B,EAAE,EACNhJ,kBAAkB,KAAG,EAAE,IAAAiJ,uBAAA,GAAEjJ,kBAAkB,CAACU,KAAK,cAAAuI,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EACxDrJ,UAAU,EACVN,WACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACF4J,OAAO,EAAEnG,UAAU,IAAIA,UAAU,CAACoG,MAAM,GAAG,CAAC,GAAGpG,UAAU,CAACqG,GAAG,CAAEC,SAAS,KAAM;kBAC5E3I,KAAK,EAAE2I,SAAS,CAACC,EAAE;kBACnBC,KAAK,EAAEF,SAAS,CAACG,cAAc,IAAI;gBACrC,CAAC,CAAC,CAAC,GAAG,EAAG;gBACTC,YAAY,EAAEA,CAAChB,MAAM,EAAEiB,UAAU,KAC/BjB,MAAM,CAACc,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDrD,WAAW,EAAC,qBAAqB;gBACjCuD,YAAY;gBACZC,WAAW;gBACXvE,SAAS,EAAC,wBAAwB;gBAClCwE,eAAe,EAAC,cAAc;gBAC9BC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE3I,KAAK,MAAM;oBACzB,GAAG2I,IAAI;oBACPC,SAAS,EAAE,MAAM;oBACjBC,WAAW,EAAE,MAAM;oBACnBC,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,SAAS;oBAC1BC,SAAS,EAAE,MAAM;oBACjB,SAAS,EAAE;sBACTD,eAAe,EAAE;oBACnB,CAAC;oBACDE,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFnE,WAAW,EAAG4D,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFC,WAAW,EAAGV,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFlC,MAAM,EAAEA,CAACyB,IAAI,EAAE3I,KAAK,MAAM;oBACxB,GAAG2I,IAAI;oBACPK,eAAe,EAAEhJ,KAAK,CAACsJ,UAAU,GAAG,SAAS,GAAGtJ,KAAK,CAACuJ,SAAS,GAAG,SAAS,GAAG,OAAO;oBACrFJ,KAAK,EAAEnJ,KAAK,CAACsJ,UAAU,GAAG,OAAO,GAAG,SAAS;oBAC7CF,QAAQ,EAAE;kBACZ,CAAC;gBACH;cAAE;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,eACxF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAA+L;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA,CAACN,MAAM;gBACLoD,KAAK,EAAEZ,cAAe;gBACtByG,QAAQ,EAAGkC,MAAM,IAAK;kBACpB1I,iBAAiB,CAAC0I,MAAM,CAAC;kBACzB,IAAIA,MAAM,IAAIA,MAAM,CAAC/H,KAAK,EAAE;oBAAA,IAAAqK,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBAC1BnM,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAqL,uBAAA,GAAGrL,eAAe,CAACgB,KAAK,cAAAqK,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACzDtC,MAAM,CAAC/H,KAAK,EACZV,kBAAkB,KAAK,EAAE,IAAAgL,uBAAA,GAAGhL,kBAAkB,CAACU,KAAK,cAAAsK,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EAC/DpL,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAuL,uBAAA,GAAGvL,eAAe,CAACgB,KAAK,cAAAuK,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACzDxC,MAAM,CAAC/H,KAAK,EACZV,kBAAkB,KAAK,EAAE,IAAAkL,uBAAA,GAAGlL,kBAAkB,CAACU,KAAK,cAAAwK,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EAC/DtL,UAAU,EACVN,WACF,CACF,CAAC;kBACH,CAAC,MAAM;oBAAA,IAAA6L,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBACLvM,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAyL,uBAAA,GAAGzL,eAAe,CAACgB,KAAK,cAAAyK,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACzD,EAAE,EACFnL,kBAAkB,KAAK,EAAE,IAAAoL,uBAAA,GAAGpL,kBAAkB,CAACU,KAAK,cAAA0K,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EAC/DxL,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA2L,uBAAA,GAAG3L,eAAe,CAACgB,KAAK,cAAA2K,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACzD,EAAE,EACFrL,kBAAkB,KAAK,EAAE,IAAAsL,uBAAA,GAAGtL,kBAAkB,CAACU,KAAK,cAAA4K,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EAC/D1L,UAAU,EACVN,WACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACF4J,OAAO,EAAExG,SAAS,IAAIA,SAAS,CAACyG,MAAM,GAAG,CAAC,GAAGzG,SAAS,CAAC0G,GAAG,CAAEmC,QAAQ,KAAM;kBACxE7K,KAAK,EAAE6K,QAAQ,CAACjC,EAAE;kBAClBC,KAAK,EAAEgC,QAAQ,CAACC,SAAS,IAAI;gBAC/B,CAAC,CAAC,CAAC,GAAG,EAAG;gBACT/B,YAAY,EAAEA,CAAChB,MAAM,EAAEiB,UAAU,KAC/BjB,MAAM,CAACc,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDrD,WAAW,EAAC,oBAAoB;gBAChCuD,YAAY;gBACZC,WAAW;gBACXvE,SAAS,EAAC,wBAAwB;gBAClCwE,eAAe,EAAC,cAAc;gBAC9BC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE3I,KAAK,MAAM;oBACzB,GAAG2I,IAAI;oBACPC,SAAS,EAAE,MAAM;oBACjBC,WAAW,EAAE,MAAM;oBACnBC,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,SAAS;oBAC1BC,SAAS,EAAE,MAAM;oBACjB,SAAS,EAAE;sBACTD,eAAe,EAAE;oBACnB,CAAC;oBACDE,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFnE,WAAW,EAAG4D,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFC,WAAW,EAAGV,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFlC,MAAM,EAAEA,CAACyB,IAAI,EAAE3I,KAAK,MAAM;oBACxB,GAAG2I,IAAI;oBACPK,eAAe,EAAEhJ,KAAK,CAACsJ,UAAU,GAAG,SAAS,GAAGtJ,KAAK,CAACuJ,SAAS,GAAG,SAAS,GAAG,OAAO;oBACrFJ,KAAK,EAAEnJ,KAAK,CAACsJ,UAAU,GAAG,OAAO,GAAG,SAAS;oBAC7CF,QAAQ,EAAE;kBACZ,CAAC;gBACH;cAAE;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B1H,OAAA;gBAAK2H,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,eACxF1H,OAAA;kBAAK2H,SAAS,EAAC,0EAA0E;kBAACG,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACC,MAAM,EAAC,cAAc;kBAAAN,QAAA,eAC7I1H,OAAA;oBAAMiI,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACO,WAAW,EAAE,CAAE;oBAACN,CAAC,EAAC;kBAA+L;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvI,OAAA,CAACN,MAAM;gBACLoD,KAAK,EAAEV,kBAAmB;gBAC1BuG,QAAQ,EAAGkC,MAAM,IAAK;kBACpBxI,oBAAoB,CAACwI,MAAM,CAAC;kBAC5B,IAAIA,MAAM,IAAIA,MAAM,CAAC/H,KAAK,EAAE;oBAAA,IAAA+K,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBAC1B7M,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAA+L,uBAAA,GAAG/L,eAAe,CAACgB,KAAK,cAAA+K,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACxD3L,cAAc,KAAG,EAAE,IAAA4L,uBAAA,GAAC5L,cAAc,CAACY,KAAK,cAAAgL,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EAChDjD,MAAM,CAAC/H,KAAK,EACZd,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAiM,uBAAA,GAAGjM,eAAe,CAACgB,KAAK,cAAAiL,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACxD7L,cAAc,KAAG,EAAE,IAAA8L,uBAAA,GAAC9L,cAAc,CAACY,KAAK,cAAAkL,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EAChDnD,MAAM,CAAC/H,KAAK,EACZd,UAAU,EACVN,WACF,CACF,CAAC;kBACH,CAAC,MAAM;oBAAA,IAAAuM,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;oBACLjN,QAAQ,CACNtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF2C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAmM,uBAAA,GAAGnM,eAAe,CAACgB,KAAK,cAAAmL,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACxD/L,cAAc,KAAG,EAAE,IAAAgM,uBAAA,GAAChM,cAAc,CAACY,KAAK,cAAAoL,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EAChD,EAAE,EACFlM,UAAU,EACVN,WACF,CACF,CAAC;oBACDP,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF0C,QAAQ,EACRI,aAAa,EACbU,YAAY,EACZR,eAAe,KAAK,EAAE,IAAAqM,uBAAA,GAAGrM,eAAe,CAACgB,KAAK,cAAAqL,uBAAA,cAAAA,uBAAA,GAAI,EAAE,GAAG,EAAE,EACzDjM,cAAc,KAAG,EAAE,IAAAkM,uBAAA,GAAClM,cAAc,CAACY,KAAK,cAAAsL,uBAAA,cAAAA,uBAAA,GAAE,EAAE,GAAC,EAAE,EAC/C,EAAE,EACFpM,UAAU,EACVN,WACF,CACF,CAAC;kBACH;gBACF,CAAE;gBACF4J,OAAO,EAAE9F,YAAY,IAAIA,YAAY,CAAC+F,MAAM,GAAG,CAAC,GAAG/F,YAAY,CAACgG,GAAG,CAAE6C,WAAW,KAAM;kBACpFvL,KAAK,EAAEuL,WAAW,CAAC3C,EAAE;kBACrBC,KAAK,EAAE0C,WAAW,CAACT,SAAS,IAAI;gBAClC,CAAC,CAAC,CAAC,GAAG,EAAG;gBACT/B,YAAY,EAAEA,CAAChB,MAAM,EAAEiB,UAAU,KAC/BjB,MAAM,CAACc,KAAK,CACTI,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;gBACDrD,WAAW,EAAC,uBAAuB;gBACnCuD,YAAY;gBACZC,WAAW;gBACXvE,SAAS,EAAC,wBAAwB;gBAClCwE,eAAe,EAAC,cAAc;gBAC9BC,MAAM,EAAE;kBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE3I,KAAK,MAAM;oBACzB,GAAG2I,IAAI;oBACPC,SAAS,EAAE,MAAM;oBACjBC,WAAW,EAAE,MAAM;oBACnBC,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,SAAS;oBAC1BC,SAAS,EAAE,MAAM;oBACjB,SAAS,EAAE;sBACTD,eAAe,EAAE;oBACnB,CAAC;oBACDE,UAAU,EAAE;kBACd,CAAC,CAAC;kBACFnE,WAAW,EAAG4D,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFC,WAAW,EAAGV,IAAI,KAAM;oBACtB,GAAGA,IAAI;oBACPQ,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE;kBACZ,CAAC,CAAC;kBACFlC,MAAM,EAAEA,CAACyB,IAAI,EAAE3I,KAAK,MAAM;oBACxB,GAAG2I,IAAI;oBACPK,eAAe,EAAEhJ,KAAK,CAACsJ,UAAU,GAAG,SAAS,GAAGtJ,KAAK,CAACuJ,SAAS,GAAG,SAAS,GAAG,OAAO;oBACrFJ,KAAK,EAAEnJ,KAAK,CAACsJ,UAAU,GAAG,OAAO,GAAG,SAAS;oBAC7CF,QAAQ,EAAE;kBACZ,CAAC;gBACH;cAAE;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENvI,OAAA;cAAK2H,SAAS,EAAC,gBAAgB;cAAAD,QAAA,eAC7B1H,OAAA;gBACEsO,OAAO,EAAEA,CAAA,KAAM;kBACb7M,WAAW,CAAC,EAAE,CAAC;kBACfM,kBAAkB,CAAC,EAAE,CAAC;kBACtBI,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,oBAAoB,CAAC,EAAE,CAAC;kBACxBE,gBAAgB,CAAC,EAAE,CAAC;kBACpBN,aAAa,CAAC,EAAE,CAAC;kBACjBJ,gBAAgB,CAAC,EAAE,CAAC;kBACpBF,cAAc,CAAC,EAAE,CAAC;kBAElBR,QAAQ,CACpBtC,kBAAkB,CAChB,GAAG,EACH,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACH,EAAE,EACF,EACF,CACF,CAAC;kBACD;kBACAsC,QAAQ,CACNrC,YAAY,CACV,GAAG,EACH,EAAE,EACF,EAAE,EACF,EAAE,EACF,EAAE,EACD,EAAE,EACF,EAAE,EACF,EAAE,EACH,EAAE,EACF,EACF,CACF,CAAC;gBACW,CAAE;gBACF6I,SAAS,EAAC,yJAAyJ;gBAAAD,QAAA,gBAEnK1H,OAAA;kBACE6H,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnBU,WAAW,EAAC,KAAK;kBACjBT,MAAM,EAAC,cAAc;kBACrBL,SAAS,EAAC,SAAS;kBAAAD,QAAA,eAEnB1H,OAAA;oBACEiI,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,CAAC,EAAC;kBAAyK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5K;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,iBAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvI,OAAA;UAAK2H,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC1H,OAAA;YAAK2H,SAAS,EAAC,6BAA6B;YAAAD,QAAA,EACzC1D,YAAY,gBACXhE,OAAA,CAACb,MAAM;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GACRtE,UAAU,gBACZjE,OAAA,CAACd,KAAK;cAACuH,IAAI,EAAC,OAAO;cAAC8H,OAAO,EAAEtK;YAAW;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE3CvI,OAAA;cAAK2H,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAC1C1H,OAAA;gBAAO2H,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAClC1H,OAAA;kBAAA0H,QAAA,eACE1H,OAAA;oBAAI2H,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,gBACtC1H,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,+DAA+D;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC,gEAAgE;sBAAAD,QAAA,EAAC;oBAE/E;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLvI,OAAA;sBAAI2H,SAAS,EAAC;oBAAgE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eAERvI,OAAA;kBAAA0H,QAAA,GACG3D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyH,GAAG,CAAC,CAACgD,IAAI,EAAEC,KAAK;oBAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,eAAA;oBAAA;sBAAA;sBACtB;sBACA9O,OAAA;wBAAA0H,QAAA,gBACE1H,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAC,GACxC,EAAC8G,IAAI,CAAC9C,EAAE;0BAAA;4BAAAtD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACR;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAgH,qBAAA,IAAAC,eAAA,GACvCH,IAAI,CAAC/C,SAAS,cAAAkD,eAAA,uBAAdA,eAAA,CAAgB/C,cAAc,cAAA8C,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAtG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAkH,qBAAA,IAAAC,aAAA,GACvCL,IAAI,CAACO,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAcjB,SAAS,cAAAgB,qBAAA,cAAAA,qBAAA,GAAI;0BAAK;4BAAAxG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAAAoH,eAAA,GACvCN,IAAI,CAACQ,SAAS,cAAAF,eAAA,cAAAA,eAAA,GAAI;0BAAK;4BAAA1G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,GAEvC8G,IAAI,CAACS,iBAAiB,IAAI,CAAC,EAAC,YAC/B;0BAAA;4BAAA7G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,mCAAmC;4BAAAD,QAAA,EAC7C8G,IAAI,CAACU,WAAW,IAAIV,IAAI,CAACU,WAAW,CAAC3D,MAAM,GAAG,CAAC,GAC9CiD,IAAI,CAACU,WAAW,CAAC1D,GAAG,CAAC,CAAC2D,IAAI,EAAEV,KAAK,kBAC/BzO,OAAA;8BAAA0H,QAAA,GAAmBF,UAAU,CAAC2H,IAAI,CAACC,mBAAmB,CAAC,EAAC,IAAE;4BAAA,GAA/CX,KAAK;8BAAArG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAgD,CACjE,CAAC,GAEF;0BACD;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BACEsO,OAAO,EAAEA,CAAA,KAAM;4BACbxN,QAAQ,CAAC,qBAAqB,GAAG0N,IAAI,CAAC9C,EAAE,CAAC;0BAC3C,CAAE;0BACF/D,SAAS,EAAC,2CAA2C;0BAAAD,QAAA,eAErD1H,OAAA;4BAAG2H,SAAS,EAAC,6BAA6B;4BAAAD,QAAA,EACvCV,UAAU,CAACwH,IAAI,CAACa,SAAS;0BAAC;4BAAAjH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACLvI,OAAA;0BAAI2H,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,eACxC1H,OAAA;4BAAG2H,SAAS,EAAC,2CAA2C;4BAAAD,QAAA,gBACtD1H,OAAA,CAACvB,IAAI;8BACHkJ,SAAS,EAAC,mBAAmB;8BAC7B2H,EAAE,EAAE,qBAAqB,GAAGd,IAAI,CAAC9C,EAAG;8BAAAhE,QAAA,eAEpC1H,OAAA;gCACE6H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,gBAEzE1H,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmI,CAAC,EAAC;gCAA0L;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC7L,CAAC,eACFvI,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmI,CAAC,EAAC;gCAAqC;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxC,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPvI,OAAA,CAACvB,IAAI;8BACHkJ,SAAS,EAAC,mBAAmB;8BAC7B2H,EAAE,EAAE,mBAAmB,GAAGd,IAAI,CAAC9C,EAAG;8BAAAhE,QAAA,eAElC1H,OAAA;gCACE6H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnBU,WAAW,EAAC,KAAK;gCACjBT,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,eAEzE1H,OAAA;kCACEiI,aAAa,EAAC,OAAO;kCACrBC,cAAc,EAAC,OAAO;kCACtBC,CAAC,EAAC;gCAAkQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACrQ;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,eACPvI,OAAA;8BACEsO,OAAO,EAAEA,CAAA,KAAM;gCACb/K,YAAY,CAAC,QAAQ,CAAC;gCACtBE,SAAS,CAAC+K,IAAI,CAAC9C,EAAE,CAAC;gCAClBvI,WAAW,CAAC,IAAI,CAAC;8BACnB,CAAE;8BACFwE,SAAS,EAAC,kCAAkC;8BAAAD,QAAA,eAE5C1H,OAAA;gCACE6H,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBL,SAAS,EAAC,8DAA8D;gCAAAD,QAAA,eAExE1H,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBmI,CAAC,EAAC;gCAA+T;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAClU;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC;sBAAA,GAnJEkG,KAAK;wBAAArG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAoJV;oBAAC;kBAAA,CACN,CAAC,eACFvI,OAAA;oBAAI2H,SAAS,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACRvI,OAAA;gBAAK2H,SAAS,EAAC,EAAE;gBAAAD,QAAA,eACf1H,OAAA,CAACf,QAAQ;kBACPsQ,KAAK,EAAE,aAAc;kBACrBvM,MAAM,EAAE,EAAG;kBACX/B,IAAI,EAAEA,IAAK;kBACXiD,KAAK,EAAEA;gBAAM;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvI,OAAA;UAAK2H,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D1H,OAAA;YAAI2H,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENvI,OAAA;UAAK2H,SAAS,EAAC,qBAAqB;UAAAD,QAAA,eAClC1H,OAAA;YAAK2H,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C1H,OAAA;cAAK2H,SAAS,EAAC,WAAW;cAAAD,QAAA,gBACxB1H,OAAA,CAACX,YAAY;gBACXmQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;gBACfC,IAAI,EAAE,CAAE;gBACRC,KAAK,EAAE;kBAAEC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAlI,QAAA,gBAE1C1H,OAAA,CAACV,SAAS;kBACRuQ,GAAG,EAAC,oDAAoD;kBACxDC,WAAW,EAAC;gBAAyF;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtG,CAAC,EACDlE,QAAQ,IAAIA,QAAQ,CAACkH,MAAM,GAAG,CAAC,GAAGlH,QAAQ,CAACmH,GAAG,CAAC,CAACuE,QAAQ,EAAEC,SAAS,kBAClEhQ,OAAA;kBAAA0H,QAAA,EACGqI,QAAQ,CAACd,iBAAiB,IAAIc,QAAQ,CAACd,iBAAiB,CAAC1D,MAAM,GAAG,CAAC,GAClEwE,QAAQ,CAACd,iBAAiB,CACvBgB,MAAM,CACJtC,QAAQ,IACPA,QAAQ,CAACA,QAAQ,IACjBA,QAAQ,CAACA,QAAQ,CAACuC,UAAU,IAC5BvC,QAAQ,CAACA,QAAQ,CAACwC,UACtB,CAAC,CACA3E,GAAG,CAAC,CAACmC,QAAQ,EAAEc,KAAK,kBACnBzO,OAAA,CAACT,MAAM;oBACL6Q,aAAa,EAAE;sBACbC,KAAK,EAAEA,CAAA,KAAM;wBACX9O,YAAY,CAAC,IAAI,CAAC;wBAClBF,oBAAoB,CAACsM,QAAQ,CAAC;sBAChC,CAAC,CAAE;oBACL,CAAE;oBAEF2C,QAAQ,EAAE,CACR3C,QAAQ,CAACA,QAAQ,CAACuC,UAAU,EAC5BvC,QAAQ,CAACA,QAAQ,CAACwC,UAAU,CAC5B;oBAAAzI,QAAA,eAEF1H,OAAA,CAACR,KAAK;sBAAAkI,QAAA,GACHiG,QAAQ,CAACA,QAAQ,CAACC,SAAS,eAC5B5N,OAAA;wBAAAoI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GATF,GAAEyH,SAAU,IAAGvB,KAAM,EAAC;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUtB,CACT,CAAC,GACF;gBAAI,GA7BAyH,SAAS;kBAAA5H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8Bd,CACN,CAAC,GAAG,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEG,CAAC,EACdjH,SAAS,gBACRtB,OAAA;gBAAK2H,SAAS,EAAC,4DAA4D;gBAAAD,QAAA,eACzE1H,OAAA;kBAAK2H,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAC9C1H,OAAA;oBAAK2H,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,eAChC1H,OAAA;sBACEsO,OAAO,EAAEA,CAAA,KAAM;wBACb/M,YAAY,CAAC,KAAK,CAAC;wBACnBF,oBAAoB,CAAC,IAAI,CAAC;sBAC5B,CAAE;sBACFsG,SAAS,EAAC,yEAAyE;sBAAAD,QAAA,eAEnF1H,OAAA;wBACE6H,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBQ,KAAK,EAAC,QAAQ;wBAAAd,QAAA,eAEd1H,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBmI,CAAC,EAAC;wBAAsB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNvI,OAAA;oBAAK2H,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,EAC7BtG,iBAAiB,iBAChBpB,OAAA;sBAAA0H,QAAA,gBACE1H,OAAA;wBAAK2H,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1H,OAAA;0BAAA0H,QAAA,eACE1H,OAAA;4BACE6H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,QAAQ;4BAAAD,QAAA,eAElB1H,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmI,CAAC,EAAC;4BAA2gB;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9gB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAENvI,OAAA;0BAAK2H,SAAS,EAAC,aAAa;0BAAAD,QAAA,EACzBtG,iBAAiB,CAACuM,QAAQ,CAAC4C,QAAQ,IAAInP,iBAAiB,CAACuM,QAAQ,CAAC4C,QAAQ,CAAChF,MAAM,GAAG,CAAC,GACpFnK,iBAAiB,CAACuM,QAAQ,CAAC4C,QAAQ,CAAC/E,GAAG,CACrC,CAACgF,OAAO,EAAE/B,KAAK,kBACbzO,OAAA;4BAAiB2H,SAAS,EAAC,MAAM;4BAAAD,QAAA,GAAC,GAC/B,EAAC,GAAG,EACJ8I,OAAO,CAACC,YAAY,IAClBD,OAAO,CAACE,kBAAkB,KAAK,EAAE,IAClCF,OAAO,CAACE,kBAAkB,KAAK,IAAI,GAC/B,IAAI,GAAGF,OAAO,CAACE,kBAAkB,GACjC,EAAE,CAAC;0BAAA,GANDjC,KAAK;4BAAArG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAOV,CAET,CAAC,gBAEDvI,OAAA;4BAAK2H,SAAS,EAAC,MAAM;4BAAAD,QAAA,EAAC;0BAAqB;4BAAAU,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK;wBACjD;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAEE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENvI,OAAA;wBAAK2H,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1H,OAAA;0BAAA0H,QAAA,eACE1H,OAAA;4BACE6H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEd1H,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmI,CAAC,EAAC;4BAAyJ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5J;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvI,OAAA;0BAAK2H,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA/G,qBAAA,GACzBS,iBAAiB,CAACuM,QAAQ,CAACC,SAAS,cAAAjN,qBAAA,cAAAA,qBAAA,GAAI;wBAAK;0BAAAyH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,EAELnH,iBAAiB,CAACuM,QAAQ,CAACgD,cAAc,IAAIvP,iBAAiB,CAACuM,QAAQ,CAACgD,cAAc,CAACpF,MAAM,GAAG,CAAC,GAChGnK,iBAAiB,CAACuM,QAAQ,CAACgD,cAAc,CAACnF,GAAG,CAC3C,CAACgD,IAAI,EAAEC,KAAK,kBACVzO,OAAA;wBAAA0H,QAAA,eACE1H,OAAA;0BAAK2H,SAAS,EAAC,yCAAyC;0BAAAD,QAAA,gBACtD1H,OAAA;4BAAA0H,QAAA,EACG,CACC,YAAY,EACZ,UAAU,EACV,eAAe,CAChB,CAACsE,QAAQ,CAACwC,IAAI,CAACoC,SAAS,CAAC,gBACxB5Q,OAAA;8BACE6H,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBL,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eAElB1H,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBmI,CAAC,EAAC;8BAAmW;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACtW;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC,gBAENvI,OAAA;8BACE6H,KAAK,EAAC,4BAA4B;8BAClCC,IAAI,EAAC,MAAM;8BACXC,OAAO,EAAC,WAAW;8BACnB,gBAAa,KAAK;8BAClBC,MAAM,EAAC,cAAc;8BACrBL,SAAS,EAAC,QAAQ;8BAAAD,QAAA,eAElB1H,OAAA;gCACE,kBAAe,OAAO;gCACtB,mBAAgB,OAAO;gCACvBmI,CAAC,EAAC;8BAAgQ;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACnQ;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC;0BACN;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eACNvI,OAAA;4BAAK2H,SAAS,EAAC,aAAa;4BAAAD,QAAA,GACzB8G,IAAI,CAACoC,SAAS,EAAC,KAAG,EAACpC,IAAI,CAACqC,UAAU;0BAAA;4BAAAzI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC,GA1CEkG,KAAK;wBAAArG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2CV,CAET,CAAC,GACC,IAAI,eAGRvI,OAAA;wBAAK2H,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1H,OAAA;0BAAA0H,QAAA,eACE1H,OAAA;4BACE6H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,gBAEd1H,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmI,CAAC,EAAC;4BAAuC;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC1C,CAAC,eACFvI,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmI,CAAC,EAAC;4BAAgF;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvI,OAAA;0BAAK2H,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA9G,sBAAA,GACzBQ,iBAAiB,CAACuM,QAAQ,CAACmD,OAAO,cAAAlQ,sBAAA,cAAAA,sBAAA,GAAI;wBAAK;0BAAAwH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNvI,OAAA;wBAAK2H,SAAS,EAAC,yCAAyC;wBAAAD,QAAA,gBACtD1H,OAAA;0BAAA0H,QAAA,eACE1H,OAAA;4BACE6H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnB,gBAAa,KAAK;4BAClBC,MAAM,EAAC,cAAc;4BACrBQ,KAAK,EAAC,QAAQ;4BAAAd,QAAA,eAEd1H,OAAA;8BACE,kBAAe,OAAO;8BACtB,mBAAgB,OAAO;8BACvBmI,CAAC,EAAC;4BAAoL;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvL;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNvI,OAAA;0BAAK2H,SAAS,EAAC,aAAa;0BAAAD,QAAA,GAAA7G,sBAAA,GACzBO,iBAAiB,CAACuM,QAAQ,CAACoD,cAAc,cAAAlQ,sBAAA,cAAAA,sBAAA,GACxC;wBAAK;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNvI,OAAA;wBAAG2H,SAAS,EAAC,+CAA+C;wBAAAD,QAAA,eAC1D1H,OAAA,CAACvB,IAAI;0BACHkJ,SAAS,EAAC,oBAAoB;0BAC9B2H,EAAE,EACA,uBAAuB,GACvBlO,iBAAiB,CAACuM,QAAQ,CAACjC,EAC5B;0BAAAhE,QAAA,eAED1H,OAAA;4BACE6H,KAAK,EAAC,4BAA4B;4BAClCC,IAAI,EAAC,MAAM;4BACXC,OAAO,EAAC,WAAW;4BACnBU,WAAW,EAAC,KAAK;4BACjBT,MAAM,EAAC,cAAc;4BACrBL,SAAS,EAAC,+DAA+D;4BAAAD,QAAA,eAEzE1H,OAAA;8BACEiI,aAAa,EAAC,OAAO;8BACrBC,cAAc,EAAC,OAAO;8BACtBC,CAAC,EAAC;4BAAkQ;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GACJ,IAAI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvI,OAAA,CAAChB,iBAAiB;QAChBgS,MAAM,EAAE9N,QAAS;QACjBqL,OAAO,EACLjL,SAAS,KAAK,QAAQ,GAClB,4CAA4C,GAC5C,gBACL;QACD2N,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAI3N,SAAS,KAAK,QAAQ,EAAE;YAC1BH,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM,IAAIC,SAAS,KAAK,QAAQ,IAAIE,MAAM,KAAK,EAAE,EAAE;YAClDH,YAAY,CAAC,IAAI,CAAC;YAClBlC,QAAQ,CAACpC,UAAU,CAACyE,MAAM,CAAC,CAAC;YAC5BL,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACF6N,QAAQ,EAAEA,CAAA,KAAM;UACd/N,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFvI,OAAA;QAAK2H,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC7H,EAAA,CAp+CQD,eAAe;EAAA,QACL9B,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBL,WAAW,EAqEVC,WAAW,EAGXA,WAAW,EAGRA,WAAW,EAGbA,WAAW,EAGRA,WAAW,EAGVA,WAAW,EAGTA,WAAW;AAAA;AAAA2S,EAAA,GA5F7B1Q,eAAe;AAs+CxB,eAAeA,eAAe;AAAC,IAAA0Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}