{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersListEditCase}from\"../../redux/actions/providerActions\";import{addNewCase}from\"../../redux/actions/caseActions\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{insurancesListDashboard}from\"../../redux/actions/insuranceActions\";import{coordinatorsListDashboard}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";import GoogleComponent from\"react-google-autocomplete\";// Country to Currency mapping - using exact country names from COUNTRIES constant\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const COUNTRY_CURRENCY_MAP={\"Morocco\":\"MAD\",\"United States\":\"USD\",\"Canada\":\"CAD\",\"United Kingdom\":\"GBP\",\"France\":\"EUR\",\"Germany\":\"EUR\",\"Spain\":\"EUR\",\"Italy\":\"EUR\",\"Netherlands\":\"EUR\",\"Belgium\":\"EUR\",\"Portugal\":\"EUR\",\"Greece\":\"EUR\",\"Austria\":\"EUR\",\"Ireland\":\"EUR\",\"Finland\":\"EUR\",\"Luxembourg\":\"EUR\",\"Estonia\":\"EUR\",\"Slovenia\":\"EUR\",\"Slovakia\":\"EUR\",\"Malta\":\"EUR\",\"Cyprus\":\"EUR\",\"Lithuania\":\"EUR\",\"Latvia\":\"EUR\",\"Japan\":\"JPY\",\"China\":\"CNY\",\"India\":\"INR\",\"Australia\":\"AUD\",\"New Zealand\":\"NZD\",\"South Africa\":\"ZAR\",\"Brazil\":\"BRL\",\"Mexico\":\"MXN\",\"Argentina\":\"ARS\",\"Chile\":\"CLP\",\"Colombia\":\"COP\",\"Peru\":\"PEN\",\"Russia\":\"RUB\",\"Turkey\":\"TRY\",\"Egypt\":\"EGP\",\"Saudi Arabia\":\"SAR\",\"United Arab Emirates\":\"AED\",\"Qatar\":\"QAR\",\"Kuwait\":\"KWD\",\"Bahrain\":\"BHD\",\"Oman\":\"OMR\",\"Jordan\":\"JOD\",\"Lebanon\":\"LBP\",\"Israel\":\"ILS\",\"South Korea\":\"KRW\",\"Thailand\":\"THB\",\"Malaysia\":\"MYR\",\"Singapore\":\"SGD\",\"Indonesia\":\"IDR\",\"Philippines\":\"PHP\",\"Vietnam\":\"VND\",\"Pakistan\":\"PKR\",\"Bangladesh\":\"BDT\",\"Sri Lanka\":\"LKR\",\"Nepal\":\"NPR\",\"Switzerland\":\"CHF\",\"Norway\":\"NOK\",\"Sweden\":\"SEK\",\"Denmark\":\"DKK\",\"Iceland\":\"ISK\",\"Poland\":\"PLN\",\"Czech Republic\":\"CZK\",\"Hungary\":\"HUF\",\"Romania\":\"RON\",\"Bulgaria\":\"BGN\",\"Croatia\":\"HRK\",\"Serbia\":\"RSD\",\"Ukraine\":\"UAH\",\"Belarus\":\"BYN\",\"Algeria\":\"DZD\",\"Tunisia\":\"TND\",\"Libya\":\"LYD\",\"Sudan\":\"SDG\",\"Ethiopia\":\"ETB\",\"Kenya\":\"KES\",\"Uganda\":\"UGX\",\"Tanzania\":\"TZS\",\"Rwanda\":\"RWF\",\"Ghana\":\"GHS\",\"Nigeria\":\"NGN\",\"Senegal\":\"XOF\",\"Ivory Coast\":\"XOF\",\"Mali\":\"XOF\",\"Burkina Faso\":\"XOF\",\"Niger\":\"XOF\",\"Guinea\":\"GNF\",\"Sierra Leone\":\"SLL\",\"Liberia\":\"LRD\",\"Cameroon\":\"XAF\",\"Chad\":\"XAF\",\"Central African Republic\":\"XAF\",\"Democratic Republic of the Congo\":\"CDF\",\"Republic of the Congo\":\"XAF\",\"Gabon\":\"XAF\",\"Angola\":\"AOA\",\"Zambia\":\"ZMK\",\"Zimbabwe\":\"ZWL\",\"Botswana\":\"BWP\",\"Namibia\":\"NAD\",\"Lesotho\":\"LSL\",\"Swaziland\":\"SZL\",\"Mozambique\":\"MZN\",\"Madagascar\":\"MGA\",\"Mauritius\":\"MUR\",\"Seychelles\":\"SCR\",\"Afghanistan\":\"AFN\",\"Albania\":\"ALL\",\"Armenia\":\"AMD\",\"Azerbaijan\":\"AZN\",\"Brunei\":\"BND\",\"Cambodia\":\"KHR\",\"Cape Verde\":\"CVE\",\"Comoros\":\"KMF\",\"Costa Rica\":\"CRC\",\"Cuba\":\"CUP\",\"Dominican Republic\":\"DOP\",\"Ecuador\":\"USD\",\"El Salvador\":\"USD\",\"Eritrea\":\"ERN\",\"Fiji\":\"FJD\",\"Georgia\":\"GEL\",\"Guatemala\":\"GTQ\",\"Guinea-Bissau\":\"XOF\",\"Guyana\":\"GYD\",\"Haiti\":\"HTG\",\"Honduras\":\"HNL\",\"Hong Kong\":\"HKD\",\"Iran\":\"IRR\",\"Iraq\":\"IQD\",\"Jamaica\":\"JMD\",\"Kazakhstan\":\"KZT\",\"Kyrgyzstan\":\"KGS\",\"Laos\":\"LAK\",\"Macau\":\"MOP\",\"Macedonia\":\"MKD\",\"Malawi\":\"MWK\",\"Maldives\":\"MVR\",\"Marshall Islands\":\"USD\",\"Mauritania\":\"MRU\",\"Micronesia\":\"USD\",\"Moldova\":\"MDL\",\"Monaco\":\"EUR\",\"Mongolia\":\"MNT\",\"Montenegro\":\"EUR\",\"Myanmar\":\"MMK\",\"Nicaragua\":\"NIO\",\"North Korea\":\"KPW\",\"Panama\":\"PAB\",\"Papua New Guinea\":\"PGK\",\"Paraguay\":\"PYG\",\"Puerto Rico\":\"USD\",\"Samoa\":\"WST\",\"San Marino\":\"EUR\",\"Sao Tome and Principe\":\"STN\",\"Somalia\":\"SOS\",\"South Sudan\":\"SSP\",\"Suriname\":\"SRD\",\"Syria\":\"SYP\",\"Taiwan\":\"TWD\",\"Tajikistan\":\"TJS\",\"Togo\":\"XOF\",\"Tonga\":\"TOP\",\"Trinidad and Tobago\":\"TTD\",\"Turkmenistan\":\"TMT\",\"Tuvalu\":\"AUD\",\"Uruguay\":\"UYU\",\"Uzbekistan\":\"UZS\",\"Vanuatu\":\"VUV\",\"Venezuela\":\"VES\",\"Western Sahara\":\"MAD\",\"Yemen\":\"YER\"};const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & Assistance Details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function AddCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[providerDate,setProviderDate]=useState(\"\");const[providerDateError,setProviderDateError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseTypeItem,setCaseTypeItem]=useState(\"\");const[caseTypeItemError,setCaseTypeItemError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const[isLoading,setIsLoading]=useState(true);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const createCase=useSelector(state=>state.createNewCase);const{loadingCaseAdd,successCaseAdd,errorCaseAdd}=createCase;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// Set loading state to true when starting to fetch data\nsetIsLoading(true);setStepSelect(0);dispatch(coordinatorsListDashboard(\"0\"));dispatch(providersListEditCase(\"0\"));dispatch(insurancesListDashboard(\"0\"));//   dispatch(clientList(\"0\"));\n// Set a maximum timeout for the loading indicator (30 seconds) as a fallback\nconst timeoutId=setTimeout(()=>{setIsLoading(false);console.log(\"Maximum loading time reached, hiding loading indicator\");},4000);// Clean up the timeout when the component unmounts\nreturn()=>clearTimeout(timeoutId);}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseAdd){setStepSelect(5);setIsLoading(false);}},[successCaseAdd]);// Update loading state when case add is in progress\nuseEffect(()=>{if(loadingCaseAdd){setIsLoading(true);}},[loadingCaseAdd]);// Update loading state based on data loading status\nuseEffect(()=>{// Check if essential data is loaded\nif(!loadingProviders&&!loadingInsurances&&!loadingCoordinators&&providers&&providers.length>0&&coordinators&&coordinators.length>0){// Hide loading indicator as soon as we have the essential data\nsetIsLoading(false);}},[loadingProviders,loadingInsurances,loadingCoordinators,providers,coordinators]);return/*#__PURE__*/_jsxs(DefaultLayout,{children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-700 font-medium\",children:\"Loading data...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:`flex flex-row mb-3 md:min-h-20 ${stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\"} md:items-start items-center`,children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${firstNameError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${emailError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${phoneError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);// Auto-update currency based on selected country\nif(option&&option.value){// The option.value contains the country title (name)\nconst countryName=option.value;const mappedCurrencyCode=COUNTRY_CURRENCY_MAP[countryName];if(mappedCurrencyCode){// Find the currency option in CURRENCYITEMS\nconst currencyOption=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(currency=>currency.code===mappedCurrencyCode);if(currencyOption){// Use setTimeout to ensure the state update happens after the country is set\nsetTimeout(()=>{setCurrencyCode({value:currencyOption.code,label:currencyOption.name!==\"\"?currencyOption.name+\" (\"+currencyOption.code+\")\":currencyOption.code});},100);}}}},options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"py-2\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),className:\"text-sm\",placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:` outline-none border ${cityError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// Extract country from Google Places result\nif(place.address_components){const countryComponent=place.address_components.find(component=>component.types.includes('country'));if(countryComponent){const countryName=countryComponent.long_name;// Find matching country in COUNTRIES constant\nconst foundCountry=COUNTRIES.find(country=>country.title===countryName);if(foundCountry){// Set the country\nsetCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});// Auto-update currency based on selected country\nconst currencyCode=COUNTRY_CURRENCY_MAP[countryName];if(currencyCode){// Find the currency option in CURRENCYITEMS\nconst currencyOption=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(currency=>currency.code===currencyCode);if(currencyOption){setCurrencyCode({value:currencyOption.code,label:currencyOption.name!==\"\"?currencyOption.name+\" (\"+currencyOption.code+\")\":currencyOption.code});// Show success message\ntoast.success(`Country and currency automatically updated: ${countryName} - ${currencyOption.name} (${currencyOption.code})`);}}else{// Show message if country is set but currency mapping not found\ntoast.info(`Country updated to ${countryName}. Please select currency manually.`);}}else{// Show message if country not found in our list\ntoast.info(`City selected. Country \"${countryName}\" not found in our list. Please select country and currency manually.`);}}}// Optional: Extract coordinates if needed in the future\n// const latitude = place.geometry.location.lat();\n// const longitude = place.geometry.location.lng();\n// setLocationX(latitude ?? \"\");\n// setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${caseDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:` outline-none border ${caseTypeError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),caseType===\"Medical\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type Item \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseTypeItem,onChange:v=>setCaseTypeItem(v.target.value),className:` outline-none border ${caseTypeItemError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type Item\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Outpatient\",children:\"Outpatient\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Inpatient\",children:\"Inpatient\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeItemError?caseTypeItemError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${priceTotalError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseTypeItemError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(city===\"\"){setCityError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}else if(caseType===\"Medical\"&&caseTypeItem===\"\"){setCaseTypeItemError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordination-fee\")){setCoordinatStatusList([...coordinatStatusList,\"coordination-fee\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordination-fee\"));}},checked:coordinatStatusList.includes(\"coordination-fee\"),id:\"coordination-fee\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordination-fee\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordination Fee\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-payment\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-payment\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-payment\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-payment\"),id:\"coordinated-missing-payment\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-payment\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Payment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col w-full \",children:caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Starting Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Starting Date\",value:startDate,onChange:v=>{setStartDate(v.target.value);// If end date is earlier than new start date, update end date\nif(endDate&&endDate<v.target.value){setEndDate(v.target.value);}}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Hospital Ending Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Hospital Ending Date\",value:endDate,onChange:v=>setEndDate(v.target.value),disabled:!startDate,min:startDate})})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";// Show loading indicator while fetching provider services\nsetIsLoading(true);const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);// Hide loading indicator after services are loaded\nsetTimeout(()=>{setIsLoading(false);},100);}else{setProviderServices([]);setIsLoading(false);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\",city:item.city||\"\",country:item.country||\"\"})),filterOption:(option,inputValue)=>{var _option$label,_option$city,_option$country;// تحسين البحث ليشمل الاسم والمدينة والبلد\nconst searchTerm=inputValue===null||inputValue===void 0?void 0:inputValue.toLowerCase();return((_option$label=option.label)===null||_option$label===void 0?void 0:_option$label.toLowerCase().includes(searchTerm))||((_option$city=option.city)===null||_option$city===void 0?void 0:_option$city.toLowerCase().includes(searchTerm))||((_option$country=option.country)===null||_option$country===void 0?void 0:_option$country.toLowerCase().includes(searchTerm));},placeholder:\"Select Provider...\",isSearchable:true// Add loading indicator\n,isLoading:loadingProviders,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Service\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:`outline-none border ${providerServiceError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Visit Date\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${providerDateError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Visit Date\",value:providerDate,onChange:v=>setProviderDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerDateError?providerDateError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");setProviderDateError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\"Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\"Provider Service is required\");check=false;}if(providerDate===\"\"){setProviderDateError(\"These fields are required.\");toast.error(\"Visit Date is required\");check=false;}if(check){const exists=false;// const exists = providerMultiSelect.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.service?.id) ===\n//       String(providerService)\n// );\nif(!exists){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));console.log(foundProvider);if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService,date:providerDate}]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#000000bf] font-bold text-xs  mb-1\",children:\"Providers\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-black text-sm\",children:providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4,_itemProvider$date;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$date=itemProvider.date)!==null&&_itemProvider$date!==void 0?_itemProvider$date:\"---\"]})]})]},index);})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusListError(\"\");setProviderNameError(\"\");// if (coordinatStatusList.length === 0) {\n//   setCoordinatStatusListError(\n//     \"This fields is required.\"\n//   );\n//   check = false;\n// }\n// if (providerMultiSelect.length === 0) {\n//   setProviderNameError(\n//     \"Please select this and click Add Provider.\"\n//   );\n//   check = false;\n// }\nif(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseAdd,onClick:async()=>{var _currencyCode$value;const providerItems=providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id,date:item.date};});await dispatch(addNewCase({first_name:firstName.trim(),last_name:lastName.trim(),full_name:firstName.trim()+\" \"+lastName.trim(),birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:coordinator.value,case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:providerName.value,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany.value,assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,//\nproviders:providerItems!==null&&providerItems!==void 0?providerItems:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseAdd?\"Loading..\":\"Submit\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Created Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully created and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})]});}export default AddCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersListEditCase", "addNewCase", "Select", "useDropzone", "insurancesListDashboard", "coordinatorsList<PERSON>ash<PERSON>", "COUNTRIES", "CURRENCYITEMS", "GoogleComponent", "jsx", "_jsx", "jsxs", "_jsxs", "COUNTRY_CURRENCY_MAP", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "timeoutId", "setTimeout", "console", "log", "clearTimeout", "length", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "countryName", "mappedCurrencyCode", "currencyOption", "find", "currency", "code", "label", "name", "options", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "address_components", "countryComponent", "component", "types", "includes", "long_name", "foundCountry", "success", "info", "defaultValue", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "item", "full_name", "min", "checked", "for", "rows", "check", "error", "filter", "status", "disabled", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "_foundProvider$servic", "services", "_option$label", "_option$city", "_option$country", "searchTerm", "service", "_service$service_type", "service_type", "service_specialist", "exists", "_providerName$value", "String", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "provider", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "style", "size", "toFixed", "indexToRemove", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "first_name", "trim", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_type_item", "case_description", "status_coordination", "case_status", "appointment_date", "start_date", "end_date", "service_location", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "is_pay", "price_tatal", "currency_price"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListEditCase } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\n// Country to Currency mapping - using exact country names from COUNTRIES constant\nconst COUNTRY_CURRENCY_MAP = {\n  \"Morocco\": \"MAD\",\n  \"United States\": \"USD\",\n  \"Canada\": \"CAD\",\n  \"United Kingdom\": \"GBP\",\n  \"France\": \"EUR\",\n  \"Germany\": \"EUR\",\n  \"Spain\": \"EUR\",\n  \"Italy\": \"EUR\",\n  \"Netherlands\": \"EUR\",\n  \"Belgium\": \"EUR\",\n  \"Portugal\": \"EUR\",\n  \"Greece\": \"EUR\",\n  \"Austria\": \"EUR\",\n  \"Ireland\": \"EUR\",\n  \"Finland\": \"EUR\",\n  \"Luxembourg\": \"EUR\",\n  \"Estonia\": \"EUR\",\n  \"Slovenia\": \"EUR\",\n  \"Slovakia\": \"EUR\",\n  \"Malta\": \"EUR\",\n  \"Cyprus\": \"EUR\",\n  \"Lithuania\": \"EUR\",\n  \"Latvia\": \"EUR\",\n  \"Japan\": \"JPY\",\n  \"China\": \"CNY\",\n  \"India\": \"INR\",\n  \"Australia\": \"AUD\",\n  \"New Zealand\": \"NZD\",\n  \"South Africa\": \"ZAR\",\n  \"Brazil\": \"BRL\",\n  \"Mexico\": \"MXN\",\n  \"Argentina\": \"ARS\",\n  \"Chile\": \"CLP\",\n  \"Colombia\": \"COP\",\n  \"Peru\": \"PEN\",\n  \"Russia\": \"RUB\",\n  \"Turkey\": \"TRY\",\n  \"Egypt\": \"EGP\",\n  \"Saudi Arabia\": \"SAR\",\n  \"United Arab Emirates\": \"AED\",\n  \"Qatar\": \"QAR\",\n  \"Kuwait\": \"KWD\",\n  \"Bahrain\": \"BHD\",\n  \"Oman\": \"OMR\",\n  \"Jordan\": \"JOD\",\n  \"Lebanon\": \"LBP\",\n  \"Israel\": \"ILS\",\n  \"South Korea\": \"KRW\",\n  \"Thailand\": \"THB\",\n  \"Malaysia\": \"MYR\",\n  \"Singapore\": \"SGD\",\n  \"Indonesia\": \"IDR\",\n  \"Philippines\": \"PHP\",\n  \"Vietnam\": \"VND\",\n  \"Pakistan\": \"PKR\",\n  \"Bangladesh\": \"BDT\",\n  \"Sri Lanka\": \"LKR\",\n  \"Nepal\": \"NPR\",\n  \"Switzerland\": \"CHF\",\n  \"Norway\": \"NOK\",\n  \"Sweden\": \"SEK\",\n  \"Denmark\": \"DKK\",\n  \"Iceland\": \"ISK\",\n  \"Poland\": \"PLN\",\n  \"Czech Republic\": \"CZK\",\n  \"Hungary\": \"HUF\",\n  \"Romania\": \"RON\",\n  \"Bulgaria\": \"BGN\",\n  \"Croatia\": \"HRK\",\n  \"Serbia\": \"RSD\",\n  \"Ukraine\": \"UAH\",\n  \"Belarus\": \"BYN\",\n  \"Algeria\": \"DZD\",\n  \"Tunisia\": \"TND\",\n  \"Libya\": \"LYD\",\n  \"Sudan\": \"SDG\",\n  \"Ethiopia\": \"ETB\",\n  \"Kenya\": \"KES\",\n  \"Uganda\": \"UGX\",\n  \"Tanzania\": \"TZS\",\n  \"Rwanda\": \"RWF\",\n  \"Ghana\": \"GHS\",\n  \"Nigeria\": \"NGN\",\n  \"Senegal\": \"XOF\",\n  \"Ivory Coast\": \"XOF\",\n  \"Mali\": \"XOF\",\n  \"Burkina Faso\": \"XOF\",\n  \"Niger\": \"XOF\",\n  \"Guinea\": \"GNF\",\n  \"Sierra Leone\": \"SLL\",\n  \"Liberia\": \"LRD\",\n  \"Cameroon\": \"XAF\",\n  \"Chad\": \"XAF\",\n  \"Central African Republic\": \"XAF\",\n  \"Democratic Republic of the Congo\": \"CDF\",\n  \"Republic of the Congo\": \"XAF\",\n  \"Gabon\": \"XAF\",\n  \"Angola\": \"AOA\",\n  \"Zambia\": \"ZMK\",\n  \"Zimbabwe\": \"ZWL\",\n  \"Botswana\": \"BWP\",\n  \"Namibia\": \"NAD\",\n  \"Lesotho\": \"LSL\",\n  \"Swaziland\": \"SZL\",\n  \"Mozambique\": \"MZN\",\n  \"Madagascar\": \"MGA\",\n  \"Mauritius\": \"MUR\",\n  \"Seychelles\": \"SCR\",\n  \"Afghanistan\": \"AFN\",\n  \"Albania\": \"ALL\",\n  \"Armenia\": \"AMD\",\n  \"Azerbaijan\": \"AZN\",\n  \"Brunei\": \"BND\",\n  \"Cambodia\": \"KHR\",\n  \"Cape Verde\": \"CVE\",\n  \"Comoros\": \"KMF\",\n  \"Costa Rica\": \"CRC\",\n  \"Cuba\": \"CUP\",\n  \"Dominican Republic\": \"DOP\",\n  \"Ecuador\": \"USD\",\n  \"El Salvador\": \"USD\",\n  \"Eritrea\": \"ERN\",\n  \"Fiji\": \"FJD\",\n  \"Georgia\": \"GEL\",\n  \"Guatemala\": \"GTQ\",\n  \"Guinea-Bissau\": \"XOF\",\n  \"Guyana\": \"GYD\",\n  \"Haiti\": \"HTG\",\n  \"Honduras\": \"HNL\",\n  \"Hong Kong\": \"HKD\",\n  \"Iran\": \"IRR\",\n  \"Iraq\": \"IQD\",\n  \"Jamaica\": \"JMD\",\n  \"Kazakhstan\": \"KZT\",\n  \"Kyrgyzstan\": \"KGS\",\n  \"Laos\": \"LAK\",\n  \"Macau\": \"MOP\",\n  \"Macedonia\": \"MKD\",\n  \"Malawi\": \"MWK\",\n  \"Maldives\": \"MVR\",\n  \"Marshall Islands\": \"USD\",\n  \"Mauritania\": \"MRU\",\n  \"Micronesia\": \"USD\",\n  \"Moldova\": \"MDL\",\n  \"Monaco\": \"EUR\",\n  \"Mongolia\": \"MNT\",\n  \"Montenegro\": \"EUR\",\n  \"Myanmar\": \"MMK\",\n  \"Nicaragua\": \"NIO\",\n  \"North Korea\": \"KPW\",\n  \"Panama\": \"PAB\",\n  \"Papua New Guinea\": \"PGK\",\n  \"Paraguay\": \"PYG\",\n  \"Puerto Rico\": \"USD\",\n  \"Samoa\": \"WST\",\n  \"San Marino\": \"EUR\",\n  \"Sao Tome and Principe\": \"STN\",\n  \"Somalia\": \"SOS\",\n  \"South Sudan\": \"SSP\",\n  \"Suriname\": \"SRD\",\n  \"Syria\": \"SYP\",\n  \"Taiwan\": \"TWD\",\n  \"Tajikistan\": \"TJS\",\n  \"Togo\": \"XOF\",\n  \"Tonga\": \"TOP\",\n  \"Trinidad and Tobago\": \"TTD\",\n  \"Turkmenistan\": \"TMT\",\n  \"Tuvalu\": \"AUD\",\n  \"Uruguay\": \"UYU\",\n  \"Uzbekistan\": \"UZS\",\n  \"Vanuatu\": \"VUV\",\n  \"Venezuela\": \"VES\",\n  \"Western Sahara\": \"MAD\",\n  \"Yemen\": \"YER\"\n};\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & Assistance Details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      setStepSelect(0);\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListEditCase(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 4000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingInsurances &&\n      !loadingCoordinators &&\n      providers &&\n      providers.length > 0 &&\n      coordinators &&\n      coordinators.length > 0\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [\n    loadingProviders,\n    loadingInsurances,\n    loadingCoordinators,\n    providers,\n    coordinators,\n  ]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n\n                              // Auto-update currency based on selected country\n                              if (option && option.value) {\n                                // The option.value contains the country title (name)\n                                const countryName = option.value;\n                                const mappedCurrencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                if (mappedCurrencyCode) {\n                                  // Find the currency option in CURRENCYITEMS\n                                  const currencyOption = CURRENCYITEMS?.find(\n                                    (currency) => currency.code === mappedCurrencyCode\n                                  );\n\n                                  if (currencyOption) {\n                                    // Use setTimeout to ensure the state update happens after the country is set\n                                    setTimeout(() => {\n                                      setCurrencyCode({\n                                        value: currencyOption.code,\n                                        label: currencyOption.name !== \"\"\n                                          ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                          : currencyOption.code\n                                      });\n\n                                    }, 100);\n                                  } \n                                } \n                              }\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n\n                                // Extract country from Google Places result\n                                if (place.address_components) {\n                                  const countryComponent = place.address_components.find(\n                                    component => component.types.includes('country')\n                                  );\n\n                                  if (countryComponent) {\n                                    const countryName = countryComponent.long_name;\n\n                                    // Find matching country in COUNTRIES constant\n                                    const foundCountry = COUNTRIES.find(\n                                      country => country.title === countryName\n                                    );\n\n                                    if (foundCountry) {\n                                      // Set the country\n                                      setCountry({\n                                        value: foundCountry.title,\n                                        label: (\n                                          <div className=\"flex flex-row items-center\">\n                                            <span className=\"mr-2\">{foundCountry.icon}</span>\n                                            <span>{foundCountry.title}</span>\n                                          </div>\n                                        ),\n                                      });\n\n                                      // Auto-update currency based on selected country\n                                      const currencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                      if (currencyCode) {\n                                        // Find the currency option in CURRENCYITEMS\n                                        const currencyOption = CURRENCYITEMS?.find(\n                                          (currency) => currency.code === currencyCode\n                                        );\n\n                                        if (currencyOption) {\n                                          setCurrencyCode({\n                                            value: currencyOption.code,\n                                            label: currencyOption.name !== \"\"\n                                              ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                              : currencyOption.code\n                                          });\n\n                                          // Show success message\n                                          toast.success(`Country and currency automatically updated: ${countryName} - ${currencyOption.name} (${currencyOption.code})`);\n                                        }\n                                      } else {\n                                        // Show message if country is set but currency mapping not found\n                                        toast.info(`Country updated to ${countryName}. Please select currency manually.`);\n                                      }\n                                    } else {\n                                      // Show message if country not found in our list\n                                      toast.info(`City selected. Country \"${countryName}\" not found in our list. Please select country and currency manually.`);\n                                    }\n                                  }\n                                }\n\n                                // Optional: Extract coordinates if needed in the future\n                                // const latitude = place.geometry.location.lat();\n                                // const longitude = place.geometry.location.lng();\n                                // setLocationX(latitude ?? \"\");\n                                // setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Assistance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                   {/* Appointment Details: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Appointment Details:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col w-full \">\n                      {caseType === \"Medical\" &&\n                      caseTypeItem === \"Inpatient\" ? (\n                        <div className=\"flex md:flex-row flex-col w-full\">\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Starting Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Starting Date\"\n                                value={startDate}\n                                onChange={(v) => {\n                                  setStartDate(v.target.value);\n                                  // If end date is earlier than new start date, update end date\n                                  if (endDate && endDate < v.target.value) {\n                                    setEndDate(v.target.value);\n                                  }\n                                }}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Ending Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Ending Date\"\n                                value={endDate}\n                                onChange={(v) => setEndDate(v.target.value)}\n                                disabled={!startDate}\n                                min={startDate}\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Appointment Date\n                          </div>\n                          <div>\n                            <input\n                              className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                              type=\"date\"\n                              placeholder=\"Appointment Date\"\n                              value={appointmentDate}\n                              onChange={(v) =>\n                                setAppointmentDate(v.target.value)\n                              }\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex md:flex-row flex-col  \">\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Provider Information: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Provider Information:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                              city: item.city || \"\",\n                              country: item.country || \"\",\n                            }))}\n                            filterOption={(option, inputValue) => {\n                              // تحسين البحث ليشمل الاسم والمدينة والبلد\n                              const searchTerm = inputValue?.toLowerCase();\n                              return (\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.city\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.country\n                                  ?.toLowerCase()\n                                  .includes(searchTerm)\n                              );\n                            }}\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\"Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\"Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\"Visit Date is required\");\n                            check = false;\n                          }\n                          if (check) {\n                            const exists = false;\n                            // const exists = providerMultiSelect.some(\n                            //   (provider) =>\n                            //     String(provider?.provider?.id) ===\n                            //       String(providerName.value) &&\n                            //     String(provider?.service?.id) ===\n                            //       String(providerService)\n                            // );\n\n                            if (!exists) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusListError(\"\");\n                        setProviderNameError(\"\");\n\n                        // if (coordinatStatusList.length === 0) {\n                        //   setCoordinatStatusListError(\n                        //     \"This fields is required.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        // if (providerMultiSelect.length === 0) {\n                        //   setProviderNameError(\n                        //     \"Please select this and click Add Provider.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect?.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName.trim(),\n                            last_name: lastName.trim(),\n                            full_name: firstName.trim() + \" \" + lastName.trim(),\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            //\n                            providers: providerItems ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,qBAAqB,KAAQ,qCAAqC,CAC3E,OAASC,UAAU,KAAQ,iCAAiC,CAE5D,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,uBAAuB,KAAQ,sCAAsC,CAC9E,OAASC,yBAAyB,KAAQ,iCAAiC,CAC3E,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAC1D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAEvD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,oBAAoB,CAAG,CAC3B,SAAS,CAAE,KAAK,CAChB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,cAAc,CAAE,KAAK,CACrB,sBAAsB,CAAE,KAAK,CAC7B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,MAAM,CAAE,KAAK,CACb,cAAc,CAAE,KAAK,CACrB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,cAAc,CAAE,KAAK,CACrB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,0BAA0B,CAAE,KAAK,CACjC,kCAAkC,CAAE,KAAK,CACzC,uBAAuB,CAAE,KAAK,CAC9B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,oBAAoB,CAAE,KAAK,CAC3B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,MAAM,CAAE,KAAK,CACb,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,kBAAkB,CAAE,KAAK,CACzB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,kBAAkB,CAAE,KAAK,CACzB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,KAAK,CACpB,OAAO,CAAE,KAAK,CACd,YAAY,CAAE,KAAK,CACnB,uBAAuB,CAAE,KAAK,CAC9B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,qBAAqB,CAAE,KAAK,CAC5B,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,gBAAgB,CAAE,KAAK,CACvB,OAAO,CAAE,KACX,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,wFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6B,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+B,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqC,cAAc,CAAEC,iBAAiB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACuC,QAAQ,CAAEC,WAAW,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACyC,aAAa,CAAEC,gBAAgB,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC2C,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC6C,UAAU,CAAEC,aAAa,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC+C,SAAS,CAAEC,YAAY,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACmD,KAAK,CAAEC,QAAQ,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqD,UAAU,CAAEC,aAAa,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACuD,OAAO,CAAEC,UAAU,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACyD,YAAY,CAAEC,eAAe,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC2D,IAAI,CAAEC,OAAO,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC6D,SAAS,CAAEC,YAAY,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC+D,OAAO,CAAEC,UAAU,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiE,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACmE,KAAK,CAAEC,QAAQ,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACyE,UAAU,CAAEC,aAAa,CAAC,CAAG1E,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAC2E,eAAe,CAAEC,kBAAkB,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAAC6E,WAAW,CAAEC,cAAc,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC+E,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACiF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACmF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACqF,eAAe,CAAEC,kBAAkB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACuF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACyF,YAAY,CAAEC,eAAe,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC2F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC6F,QAAQ,CAAEC,WAAW,CAAC,CAAG9F,QAAQ,CACtC,GAAI,CAAA+F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACoG,QAAQ,CAAEC,WAAW,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACsG,aAAa,CAAEC,gBAAgB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACwG,YAAY,CAAEC,eAAe,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC0G,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC4G,eAAe,CAAEC,kBAAkB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgH,eAAe,CAAEC,kBAAkB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoH,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACsH,wBAAwB,CAAEC,2BAA2B,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAACwH,eAAe,CAAEC,kBAAkB,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0H,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC4H,SAAS,CAAEC,YAAY,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8H,cAAc,CAAEC,iBAAiB,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgI,OAAO,CAAEC,UAAU,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkI,YAAY,CAAEC,eAAe,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACoI,eAAe,CAAEC,kBAAkB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACwI,YAAY,CAAEC,eAAe,CAAC,CAAGzI,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC0I,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3I,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC4I,aAAa,CAAEC,gBAAgB,CAAC,CAAG7I,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC8I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/I,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACgJ,aAAa,CAAEC,gBAAgB,CAAC,CAAGjJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkJ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGrJ,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvJ,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACwJ,aAAa,CAAEC,gBAAgB,CAAC,CAAGzJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0J,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3J,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC4J,UAAU,CAAEC,aAAa,CAAC,CAAG7J,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC8J,eAAe,CAAEC,kBAAkB,CAAC,CAAG/J,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACgK,MAAM,CAAEC,SAAS,CAAC,CAAGjK,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACkK,WAAW,CAAEC,cAAc,CAAC,CAAGnK,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACoK,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrK,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACsK,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGvK,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACwK,eAAe,CAAEC,kBAAkB,CAAC,CAAGzK,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0K,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3K,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC4K,YAAY,CAAEC,eAAe,CAAC,CAAG7K,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC8K,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/K,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACgL,aAAa,CAAEC,gBAAgB,CAAC,CAAGjL,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkL,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnL,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA;AACA,KAAM,CAACoL,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGrL,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJsL,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG9K,WAAW,CAAC,CACd+K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACLqL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxM,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJsL,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAG/L,WAAW,CAAC,CACd+K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACLwM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG5M,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJsL,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGnM,WAAW,CAAC,CACd+K,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhM,SAAS,CAAC,IAAM,CACd,MAAO,IACL4M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGhN,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACiN,SAAS,CAAEC,YAAY,CAAC,CAAGlN,QAAQ,CAAC,IAAI,CAAC,CAEhD,KAAM,CAAAmN,SAAS,CAAGjN,WAAW,CAAEkN,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGpN,WAAW,CAAEkN,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,UAAU,CAAGzN,WAAW,CAAEkN,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAGJ,UAAU,CAEnE,KAAM,CAAAK,cAAc,CAAG9N,WAAW,CAAEkN,KAAK,EAAKA,KAAK,CAACa,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAGnO,WAAW,CAAEkN,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpB3O,SAAS,CAAC,IAAM,CACd,GAAI,CAACsN,QAAQ,CAAE,CACbrL,QAAQ,CAAC0M,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AACAxB,YAAY,CAAC,IAAI,CAAC,CAElBF,aAAa,CAAC,CAAC,CAAC,CAChB9K,QAAQ,CAACrB,yBAAyB,CAAC,GAAG,CAAC,CAAC,CACxCqB,QAAQ,CAAC1B,qBAAqB,CAAC,GAAG,CAAC,CAAC,CACpC0B,QAAQ,CAACtB,uBAAuB,CAAC,GAAG,CAAC,CAAC,CACtC;AAEA;AACA,KAAM,CAAA+N,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC1B,YAAY,CAAC,KAAK,CAAC,CACnB2B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACvE,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAMC,YAAY,CAACJ,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAAC3M,QAAQ,CAAEqL,QAAQ,CAAEnL,QAAQ,CAAC,CAAC,CAElCnC,SAAS,CAAC,IAAM,CACd,GAAI+N,cAAc,CAAE,CAClBd,aAAa,CAAC,CAAC,CAAC,CAChBE,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACY,cAAc,CAAC,CAAC,CAEpB;AACA/N,SAAS,CAAC,IAAM,CACd,GAAI8N,cAAc,CAAE,CAClBX,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CAACW,cAAc,CAAC,CAAC,CAEpB;AACA9N,SAAS,CAAC,IAAM,CACd;AACA,GACE,CAAC0N,gBAAgB,EACjB,CAACU,iBAAiB,EAClB,CAACK,mBAAmB,EACpBhB,SAAS,EACTA,SAAS,CAACwB,MAAM,CAAG,CAAC,EACpBT,YAAY,EACZA,YAAY,CAACS,MAAM,CAAG,CAAC,CACvB,CACA;AACA9B,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CACDO,gBAAgB,CAChBU,iBAAiB,CACjBK,mBAAmB,CACnBhB,SAAS,CACTe,YAAY,CACb,CAAC,CAEF,mBACEnN,KAAA,CAACf,aAAa,EAAA4O,QAAA,EAEXhC,SAAS,eACR/L,IAAA,QAAKgO,SAAS,CAAC,+FAA+F,CAAAD,QAAA,cAC5G7N,KAAA,QAAK8N,SAAS,CAAC,8DAA8D,CAAAD,QAAA,eAC3E/N,IAAA,QAAKgO,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvGhO,IAAA,QAAKgO,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC7D,CAAC,CACH,CACN,cAED7N,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7N,KAAA,QAAK8N,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD/N,IAAA,MAAGiO,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB7N,KAAA,QAAK8N,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/N,IAAA,SACEsO,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNxO,IAAA,SAAMgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ/N,IAAA,SAAA+N,QAAA,cACE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/N,IAAA,SACEsO,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxO,IAAA,QAAKgO,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7C/N,IAAA,OAAIgO,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJ7N,KAAA,QAAK8N,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7N,KAAA,QAAK8N,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxE/N,IAAA,QAAKgO,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G5N,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwK,GAAG,CAAC,CAAC6D,IAAI,CAAEpO,KAAK,gBAC1BH,KAAA,QACEwO,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI7C,UAAU,CAAG4C,IAAI,CAACpO,KAAK,EAAIwL,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAAC2C,IAAI,CAACpO,KAAK,CAAC,CAC3B,CACF,CAAE,CACF2N,SAAS,CAAG,kCACVnC,UAAU,CAAG4C,IAAI,CAACpO,KAAK,EAAIwL,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EACL,8BAA8B,CAAAkC,QAAA,EAE9BlC,UAAU,CAAG4C,IAAI,CAACpO,KAAK,cACtBL,IAAA,QAAKgO,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH/N,IAAA,QACE2O,GAAG,CAAEvP,eAAgB,CACrB4O,SAAS,CAAC,QAAQ,CAClBY,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJ9C,UAAU,GAAK4C,IAAI,CAACpO,KAAK,cAC3BL,IAAA,QAAKgO,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEhO,IAAA,QAAKgO,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElB/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDtO,KAAA,QAAK8N,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC/N,IAAA,QAAKgO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAACnO,KAAK,CAAM,CAAC,CACtDuL,UAAU,GAAK4C,IAAI,CAACpO,KAAK,cACxBL,IAAA,QAAKgO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAAClO,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNL,KAAA,QAAK8N,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtDlC,UAAU,GAAK,CAAC,cACf3L,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf/N,IAAA,QAAKgO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,wBACV7M,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpC6N,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEjO,SAAU,CACjBkO,QAAQ,CAAGC,CAAC,EAAKlO,YAAY,CAACkO,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC/C,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5M,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE7N,QAAS,CAChB8N,QAAQ,CAAGC,CAAC,EAAK9N,WAAW,CAAC8N,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENhP,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,wBACVrM,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpCqN,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEzN,KAAM,CACb0N,QAAQ,CAAGC,CAAC,EAAK1N,QAAQ,CAAC0N,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpM,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,uBACV7L,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpC6M,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEjN,KAAM,CACbkN,QAAQ,CAAGC,CAAC,EAAKlN,QAAQ,CAACkN,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC3C,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC5L,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNjC,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC7N,KAAA,QAAK8N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAErM,OAAQ,CACfsM,QAAQ,CAAGE,MAAM,EAAK,CACpBvM,UAAU,CAACuM,MAAM,CAAC,CAElB;AACA,GAAIA,MAAM,EAAIA,MAAM,CAACH,KAAK,CAAE,CAC1B;AACA,KAAM,CAAAI,WAAW,CAAGD,MAAM,CAACH,KAAK,CAChC,KAAM,CAAAK,kBAAkB,CAAGpP,oBAAoB,CAACmP,WAAW,CAAC,CAE5D,GAAIC,kBAAkB,CAAE,CACtB;AACA,KAAM,CAAAC,cAAc,CAAG3P,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACvCC,QAAQ,EAAKA,QAAQ,CAACC,IAAI,GAAKJ,kBAClC,CAAC,CAED,GAAIC,cAAc,CAAE,CAClB;AACA9B,UAAU,CAAC,IAAM,CACftK,eAAe,CAAC,CACd8L,KAAK,CAAEM,cAAc,CAACG,IAAI,CAC1BC,KAAK,CAAEJ,cAAc,CAACK,IAAI,GAAK,EAAE,CAC7BL,cAAc,CAACK,IAAI,CAAG,IAAI,CAAGL,cAAc,CAACG,IAAI,CAAG,GAAG,CACtDH,cAAc,CAACG,IACrB,CAAC,CAAC,CAEJ,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CACF,CACF,CAAE,CACFG,OAAO,CAAElQ,SAAS,CAACgL,GAAG,CAAE/H,OAAO,GAAM,CACnCqM,KAAK,CAAErM,OAAO,CAACvC,KAAK,CACpBsP,KAAK,cACH1P,KAAA,QACE8N,SAAS,CAAG,GACVnL,OAAO,CAACvC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EACjC,6BAA6B,CAAAyN,QAAA,eAE9B/N,IAAA,SAAMgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAElL,OAAO,CAACkN,IAAI,CAAO,CAAC,cAC5C/P,IAAA,SAAA+N,QAAA,CAAOlL,OAAO,CAACvC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ0N,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCe,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEtN,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvBuN,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEFxQ,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrChL,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,QAAK8N,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACF,eAAe,EACd4Q,MAAM,CAAC,yCAAyC,CAChD1C,SAAS,CAAG,wBACVrL,SAAS,CAAG,eAAe,CAAG,kBAC/B,mCAAmC,CACpCwM,QAAQ,CAAGC,CAAC,EAAK,CACf1M,OAAO,CAAC0M,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACzB,CAAE,CACFyB,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BpO,OAAO,EAAAoO,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAEtC;AACA,GAAIF,KAAK,CAACI,kBAAkB,CAAE,CAC5B,KAAM,CAAAC,gBAAgB,CAAGL,KAAK,CAACI,kBAAkB,CAACvB,IAAI,CACpDyB,SAAS,EAAIA,SAAS,CAACC,KAAK,CAACC,QAAQ,CAAC,SAAS,CACjD,CAAC,CAED,GAAIH,gBAAgB,CAAE,CACpB,KAAM,CAAA3B,WAAW,CAAG2B,gBAAgB,CAACI,SAAS,CAE9C;AACA,KAAM,CAAAC,YAAY,CAAG1R,SAAS,CAAC6P,IAAI,CACjC5M,OAAO,EAAIA,OAAO,CAACvC,KAAK,GAAKgP,WAC/B,CAAC,CAED,GAAIgC,YAAY,CAAE,CAChB;AACAxO,UAAU,CAAC,CACToM,KAAK,CAAEoC,YAAY,CAAChR,KAAK,CACzBsP,KAAK,cACH1P,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC/N,IAAA,SAAMgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEuD,YAAY,CAACvB,IAAI,CAAO,CAAC,cACjD/P,IAAA,SAAA+N,QAAA,CAAOuD,YAAY,CAAChR,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CAEF;AACA,KAAM,CAAA6C,YAAY,CAAGhD,oBAAoB,CAACmP,WAAW,CAAC,CAEtD,GAAInM,YAAY,CAAE,CAChB;AACA,KAAM,CAAAqM,cAAc,CAAG3P,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACvCC,QAAQ,EAAKA,QAAQ,CAACC,IAAI,GAAKxM,YAClC,CAAC,CAED,GAAIqM,cAAc,CAAE,CAClBpM,eAAe,CAAC,CACd8L,KAAK,CAAEM,cAAc,CAACG,IAAI,CAC1BC,KAAK,CAAEJ,cAAc,CAACK,IAAI,GAAK,EAAE,CAC7BL,cAAc,CAACK,IAAI,CAAG,IAAI,CAAGL,cAAc,CAACG,IAAI,CAAG,GAAG,CACtDH,cAAc,CAACG,IACrB,CAAC,CAAC,CAEF;AACAtQ,KAAK,CAACkS,OAAO,CAAE,+CAA8CjC,WAAY,MAAKE,cAAc,CAACK,IAAK,KAAIL,cAAc,CAACG,IAAK,GAAE,CAAC,CAC/H,CACF,CAAC,IAAM,CACL;AACAtQ,KAAK,CAACmS,IAAI,CAAE,sBAAqBlC,WAAY,oCAAmC,CAAC,CACnF,CACF,CAAC,IAAM,CACL;AACAjQ,KAAK,CAACmS,IAAI,CAAE,2BAA0BlC,WAAY,uEAAsE,CAAC,CAC3H,CACF,CACF,CAEA;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFmC,YAAY,CAAEhP,IAAK,CACnB0O,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBO,QAAQ,CAAC,IAAI,CACd,CAAC,cAUF1R,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCpL,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAEhG,gBAAiB,CACxBiG,QAAQ,CAAGE,MAAM,EAAK,CACpBlG,mBAAmB,CAACkG,MAAM,CAAC,CAC7B,CAAE,CACFS,OAAO,CAAE9C,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEpC,GAAG,CAAE+G,SAAS,GAAM,CACvCzC,KAAK,CAAEyC,SAAS,CAACC,EAAE,CACnBhC,KAAK,CAAE+B,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACzC,MAAM,CAAE0C,UAAU,GAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbZ,QAAQ,CAACW,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhE,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCe,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEjH,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBkH,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFxQ,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3E,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNlJ,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,wBACVxE,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCwF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAE5F,eAAgB,CACvB6F,QAAQ,CAAGC,CAAC,EAAK7F,kBAAkB,CAAC6F,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvE,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAEH,CAAC,cAENxJ,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/N,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxB/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAEvL,WAAY,CACnBwL,QAAQ,CAAGE,MAAM,EAAK,CACpBzL,cAAc,CAACyL,MAAM,CAAC,CACxB,CAAE,CACFrB,SAAS,CAAC,SAAS,CACnB8B,OAAO,CAAEzC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEzC,GAAG,CAAEqH,IAAI,GAAM,CACpC/C,KAAK,CAAE+C,IAAI,CAACL,EAAE,CACdhC,KAAK,CAAEqC,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJJ,YAAY,CAAEA,CAACzC,MAAM,CAAE0C,UAAU,GAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbZ,QAAQ,CAACW,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/C,WAAW,CAAC,uBAAuB,CACnCe,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAExM,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvByM,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFxQ,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrClK,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN3D,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtB/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,wBACVhJ,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CACpCgK,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAEvK,QAAS,CAChBwK,QAAQ,CAAGC,CAAC,EAAKxK,WAAW,CAACwK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC9C,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/I,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cAEN9E,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE7N,KAAA,WACEgP,KAAK,CAAEhK,QAAS,CAChBiK,QAAQ,CAAGC,CAAC,EAAKjK,WAAW,CAACiK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC7ClB,SAAS,CAAG,wBACV5I,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CAAA2I,QAAA,eAEpC/N,IAAA,WAAQkP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvC/N,IAAA,WAAQkP,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C/N,IAAA,WAAQkP,KAAK,CAAE,WAAY,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT/N,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3I,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAELF,QAAQ,GAAK,SAAS,eACrBhF,KAAA,QAAK8N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,YAClC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACjD,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE7N,KAAA,WACEgP,KAAK,CAAE5J,YAAa,CACpB6J,QAAQ,CAAGC,CAAC,EAAK7J,eAAe,CAAC6J,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACjDlB,SAAS,CAAG,wBACVxI,iBAAiB,CACb,eAAe,CACf,kBACL,mCAAmC,CAAAuI,QAAA,eAEpC/N,IAAA,WAAQkP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5C/N,IAAA,WAAQkP,KAAK,CAAE,YAAa,CAAAnB,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChD/N,IAAA,WAAQkP,KAAK,CAAE,WAAY,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT/N,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvI,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CACN,cAEDtF,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjB/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAE/L,YAAa,CACpBgM,QAAQ,CAAGE,MAAM,EAAK,CACpBjM,eAAe,CAACiM,MAAM,CAAC,CACzB,CAAE,CACFS,OAAO,CAAEjQ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE+K,GAAG,CAAE8E,QAAQ,GAAM,CACzCR,KAAK,CAAEQ,QAAQ,CAACC,IAAI,CACpBC,KAAK,CACHF,QAAQ,CAACG,IAAI,GAAK,EAAE,CAChBH,QAAQ,CAACG,IAAI,CACX,IAAI,CACJH,QAAQ,CAACC,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJmC,YAAY,CAAEA,CAACzC,MAAM,CAAE0C,UAAU,GAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbZ,QAAQ,CAACW,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhE,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,0BAA0B,CACtCe,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEhN,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBiN,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFxQ,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1K,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNnD,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpB/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,wBACVvK,eAAe,CACX,eAAe,CACf,kBACL,oCAAoC,CACrCuL,IAAI,CAAC,QAAQ,CACbmD,GAAG,CAAE,CAAE,CACP1D,IAAI,CAAE,IAAK,CACXQ,WAAW,CAAC,MAAM,CAClBC,KAAK,CAAE3L,UAAW,CAClB4L,QAAQ,CAAGC,CAAC,EAAK5L,aAAa,CAAC4L,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtK,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNvD,KAAA,QAAK8N,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC/N,IAAA,QAAKgO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5C7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgP,IAAI,CAAE,UAAW,CACjBa,IAAI,CAAC,OAAO,CACZ+B,EAAE,CAAC,OAAO,CACVQ,OAAO,CAAEnP,KAAK,GAAK,IAAK,CACxBkM,QAAQ,CAAGC,CAAC,EAAK,CACflM,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFlD,IAAA,UACEgO,SAAS,CAAC,6CAA6C,CACvDqE,GAAG,CAAC,OAAO,CAAAtE,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,+BAA+B,CAAAD,QAAA,cAC5C7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgP,IAAI,CAAE,UAAW,CACjBa,IAAI,CAAC,QAAQ,CACb+B,EAAE,CAAC,QAAQ,CACXQ,OAAO,CAAEnP,KAAK,GAAK,KAAM,CACzBkM,QAAQ,CAAGC,CAAC,EAAK,CACflM,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFlD,IAAA,UACEgO,SAAS,CAAC,6CAA6C,CACvDqE,GAAG,CAAC,QAAQ,CAAAtE,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGN/N,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,aACEkP,KAAK,CAAExJ,eAAgB,CACvB4M,IAAI,CAAE,CAAE,CACRnD,QAAQ,CAAGC,CAAC,EAAKzJ,kBAAkB,CAACyJ,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACpDlB,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNhO,IAAA,QAAKgO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1D/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA6D,KAAK,CAAG,IAAI,CAChBnR,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnB6C,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBR,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,mBAAmB,CAAC,EAAE,CAAC,CACvBlB,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBM,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIzC,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CmR,KAAK,CAAG,KAAK,CACf,CAEA,GAAItQ,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCmQ,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1P,OAAO,GAAK,EAAE,EAAIA,OAAO,CAACqM,KAAK,GAAK,EAAE,CAAE,CAC1ClM,eAAe,CAAC,yBAAyB,CAAC,CAC1CuP,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9P,IAAI,GAAK,EAAE,CAAE,CACfG,YAAY,CAAC,yBAAyB,CAAC,CACvC2P,KAAK,CAAG,KAAK,CACf,CAEA,GAAIpP,YAAY,GAAK,EAAE,EAAIA,YAAY,CAAC+L,KAAK,GAAK,EAAE,CAAE,CACpD5L,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CiP,KAAK,CAAG,KAAK,CACf,CACA,GAAIhP,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7C6O,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5O,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuL,KAAK,GAAK,EAAE,CAAE,CAClDpL,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CyO,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5N,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CsN,KAAK,CAAG,KAAK,CACf,CAEA,GAAIrN,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CkN,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACLrN,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,EAAE,CACnB,CACAG,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C8M,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTzG,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLzM,KAAK,CAACmT,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFxE,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf3L,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf/N,IAAA,QAAKgO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD/N,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC7N,KAAA,QAAK8N,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAA/N,IAAA,WAAQgO,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE7N,KAAA,QAAK8N,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7B7N,KAAA,QAAK8N,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,sBACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFd,EAAE,CAAC,sBAAsB,CACzB5C,IAAI,CAAE,UAAW,CACjBoD,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,sBACF,CAAE,CACFpD,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,sBAAsB,CAC1BrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,yBACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,yBACF,CAAE,CACFQ,EAAE,CAAC,yBAAyB,CAC5B5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,yBAAyB,CAC7BrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,6BACF,CAAE,CACFQ,EAAE,CAAC,6BAA6B,CAChC5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,6BAA6B,CACjCrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,qCACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,qCACF,CAAE,CACFQ,EAAE,CAAC,qCAAqC,CACxC5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,qCAAqC,CACzCrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,kCACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,kCACF,CAAE,CACFQ,EAAE,CAAC,kCAAkC,CACrC5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,kCAAkC,CACtCrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cAEN7N,KAAA,QAAK8N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,kBACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kBAAkB,CACnB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,kBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,kBACF,CAAE,CACFQ,EAAE,CAAC,kBAAkB,CACrB5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,kBAAkB,CACtBrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,kBAED,CAAO,CAAC,EACL,CAAC,cAEN7N,KAAA,QAAK8N,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,6BACF,CAAE,CACFQ,EAAE,CAAC,6BAA6B,CAChC5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,6BAA6B,CACjCrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cAGN7N,KAAA,QAAK8N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAClJ,mBAAmB,CAACkL,QAAQ,CAC3B,mBACF,CAAC,CACD,CACAjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CACnC,mBACF,CAAE,CACFQ,EAAE,CAAC,mBAAmB,CACtB5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,mBAAmB,CACvBrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,wDAAwD,CAAAD,QAAA,eACrE/N,IAAA,UACEmP,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAAClJ,mBAAmB,CAACkL,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3CjL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACuM,MAAM,CACvBC,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACFN,OAAO,CAAElM,mBAAmB,CAACkL,QAAQ,CAAC,QAAQ,CAAE,CAChDQ,EAAE,CAAC,QAAQ,CACX5C,IAAI,CAAE,UAAW,CACjBhB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFhO,IAAA,UACEqS,GAAG,CAAC,QAAQ,CACZrE,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCN/N,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3H,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENpG,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,qBAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAEjD/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC/C7I,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BpF,KAAA,QAAK8N,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAExI,SAAU,CACjByI,QAAQ,CAAGC,CAAC,EAAK,CACfzI,YAAY,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CAC5B;AACA,GAAIpI,OAAO,EAAIA,OAAO,CAAGsI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACvCnI,UAAU,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CAC5B,CACF,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cACNhP,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,sBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,sBAAsB,CAClCC,KAAK,CAAEpI,OAAQ,CACfqI,QAAQ,CAAGC,CAAC,EAAKrI,UAAU,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC5CyD,QAAQ,CAAE,CAACjM,SAAU,CACrByL,GAAG,CAAEzL,SAAU,CAChB,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENxG,KAAA,QAAK8N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE5I,eAAgB,CACvB6I,QAAQ,CAAGC,CAAC,EACV7I,kBAAkB,CAAC6I,CAAC,CAACN,MAAM,CAACI,KAAK,CAClC,CACF,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,cAENlP,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAE1C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEhI,eAAgB,CACvBiI,QAAQ,CAAGC,CAAC,EAAKjI,kBAAkB,CAACiI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGNlP,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7N,KAAA,QAAK8N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAE5H,YAAa,CACpB6H,QAAQ,CAAGE,MAAM,EAAK,KAAAuD,aAAA,CACpBrL,eAAe,CAAC8H,MAAM,CAAC,CACvB;AACA,GAAI,CAAAwD,eAAe,EAAAD,aAAA,CAAGvD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEH,KAAK,UAAA0D,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC;AACA5G,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAA8G,aAAa,CAAGxG,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEmD,IAAI,CAClCwC,IAAI,EAAKA,IAAI,CAACL,EAAE,GAAKiB,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAC,qBAAA,CACjB/O,mBAAmB,EAAA+O,qBAAA,CACjBD,aAAa,CAACE,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACD;AACArF,UAAU,CAAC,IAAM,CACf1B,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLhI,mBAAmB,CAAC,EAAE,CAAC,CACvBgI,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFgC,SAAS,CAAC,SAAS,CACnB8B,OAAO,CAAExD,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE1B,GAAG,CAAEqH,IAAI,GAAM,CACjC/C,KAAK,CAAE+C,IAAI,CAACL,EAAE,CACdhC,KAAK,CAAEqC,IAAI,CAACC,SAAS,EAAI,EAAE,CAC3BzP,IAAI,CAAEwP,IAAI,CAACxP,IAAI,EAAI,EAAE,CACrBI,OAAO,CAAEoP,IAAI,CAACpP,OAAO,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJiP,YAAY,CAAEA,CAACzC,MAAM,CAAE0C,UAAU,GAAK,KAAAkB,aAAA,CAAAC,YAAA,CAAAC,eAAA,CACpC;AACA,KAAM,CAAAC,UAAU,CAAGrB,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC,CAC5C,MACE,EAAAiB,aAAA,CAAA5D,MAAM,CAACO,KAAK,UAAAqD,aAAA,iBAAZA,aAAA,CACIjB,WAAW,CAAC,CAAC,CACdZ,QAAQ,CAACgC,UAAU,CAAC,KAAAF,YAAA,CACvB7D,MAAM,CAAC5M,IAAI,UAAAyQ,YAAA,iBAAXA,YAAA,CACIlB,WAAW,CAAC,CAAC,CACdZ,QAAQ,CAACgC,UAAU,CAAC,KAAAD,eAAA,CACvB9D,MAAM,CAACxM,OAAO,UAAAsQ,eAAA,iBAAdA,eAAA,CACInB,WAAW,CAAC,CAAC,CACdZ,QAAQ,CAACgC,UAAU,CAAC,EAE3B,CAAE,CACFnE,WAAW,CAAC,oBAAoB,CAChCe,YAAY,KACZ;AAAA,CACAjE,SAAS,CAAEQ,gBAAiB,CAC5B0D,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE7I,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvB8I,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFxQ,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvG,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAENtH,KAAA,QAAK8N,SAAS,CAAC,gCAAgC,CAAAD,QAAA,eAC7C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE7N,KAAA,WACE8N,SAAS,CAAG,uBACV3J,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrC8K,QAAQ,CAAGC,CAAC,EAAK,CACfhL,kBAAkB,CAACgL,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAE/K,eAAgB,CAAA4J,QAAA,eAEvB/N,IAAA,WAAQkP,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BnL,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6G,GAAG,CAAC,CAACyI,OAAO,CAAEhT,KAAK,QAAAiT,qBAAA,oBACpCpT,KAAA,WAAQgP,KAAK,CAAEmE,OAAO,CAACzB,EAAG,CAAA7D,QAAA,GAAAuF,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACTxT,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC1J,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNrE,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,YAE9C,CAAK,CAAC,cACN7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,UACEgO,SAAS,CAAG,uBACVvJ,iBAAiB,CACb,eAAe,CACf,kBACL,oCAAoC,CACrCuK,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAE3K,YAAa,CACpB4K,QAAQ,CAAGC,CAAC,EAAK5K,eAAe,CAAC4K,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClD,CAAC,cACFlP,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCtJ,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENvE,KAAA,QAAK8N,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B7N,KAAA,WACEwO,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAA6D,KAAK,CAAG,IAAI,CAChB9K,oBAAoB,CAAC,EAAE,CAAC,CACxBnD,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CACxB,GACE4C,YAAY,GAAK,EAAE,EACnBA,YAAY,CAAC4H,KAAK,GAAK,EAAE,CACzB,CACAzH,oBAAoB,CAAC,4BAA4B,CAAC,CAClDpI,KAAK,CAACmT,KAAK,CAAC,sBAAsB,CAAC,CACnCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIpO,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACDjF,KAAK,CAACmT,KAAK,CAAC,8BAA8B,CAAC,CAC3CD,KAAK,CAAG,KAAK,CACf,CACA,GAAIhO,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAAC,4BAA4B,CAAC,CAClDrF,KAAK,CAACmT,KAAK,CAAC,wBAAwB,CAAC,CACrCD,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAkB,MAAM,CAAG,KAAK,CACpB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI,CAACA,MAAM,CAAE,KAAAC,mBAAA,CACX;AACA,GAAI,CAAAb,eAAe,EAAAa,mBAAA,CAAGpM,YAAY,CAAC4H,KAAK,UAAAwE,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAZ,aAAa,CAAGxG,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEmD,IAAI,CAClCwC,IAAI,EACH0B,MAAM,CAAC1B,IAAI,CAACL,EAAE,CAAC,GAAK+B,MAAM,CAACd,eAAe,CAC9C,CAAC,CACDlF,OAAO,CAACC,GAAG,CAACkF,aAAa,CAAC,CAE1B,GAAIA,aAAa,CAAE,KAAAc,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAG3P,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1C2O,aAAa,SAAbA,aAAa,kBAAAc,sBAAA,CAAbd,aAAa,CAAEE,QAAQ,UAAAY,sBAAA,iBAAvBA,sBAAA,CAAyBzI,OAAO,CAAE4I,OAAO,EAAK,CAC5CpG,OAAO,CAACC,GAAG,CAACmG,OAAO,CAACnC,EAAE,CAAC,CACzB,CAAC,CAAC,CAEF,KAAM,CAAAoC,YAAY,CAChBlB,aAAa,SAAbA,aAAa,kBAAAe,sBAAA,CAAbf,aAAa,CAAEE,QAAQ,UAAAa,sBAAA,iBAAvBA,sBAAA,CAAyBpE,IAAI,CAC1BwC,IAAI,EACH0B,MAAM,CAAC1B,IAAI,CAACL,EAAE,CAAC,GAAK+B,MAAM,CAACG,cAAc,CAC7C,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACA9P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACEgQ,QAAQ,CAAEnB,aAAa,CACvBO,OAAO,CAAEW,YAAY,CACrBE,IAAI,CAAE3P,YACR,CAAC,CACF,CAAC,CACFgD,eAAe,CAAC,EAAE,CAAC,CACnBnD,kBAAkB,CAAC,EAAE,CAAC,CACtBI,eAAe,CAAC,EAAE,CAAC,CACnBmJ,OAAO,CAACC,GAAG,CAAC3J,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLwD,oBAAoB,CAClB,kCACF,CAAC,CACDpI,KAAK,CAACmT,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACL/K,oBAAoB,CAClB,0BACF,CAAC,CACDpI,KAAK,CAACmT,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACL/K,oBAAoB,CAClB,4CACF,CAAC,CACDpI,KAAK,CAACmT,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACFxE,SAAS,CAAC,uDAAuD,CAAAD,QAAA,eAEjE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB8F,KAAK,CAAC,QAAQ,CAAApG,QAAA,cAEd/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNxO,IAAA,SAAA+N,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,cACT7N,KAAA,QAAK8N,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,WAE1D,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC9J,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2G,GAAG,CAAC,CAACwJ,YAAY,CAAE/T,KAAK,QAAAgU,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,oBAC5CzU,KAAA,QAEE8N,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAE3C/N,IAAA,QAAKgO,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClC/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAkG,eAAe,CACnB3Q,mBAAmB,CAACwO,MAAM,CACxB,CAACoC,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKzU,KAC5B,CAAC,CACH6D,sBAAsB,CAAC0Q,eAAe,CAAC,CACzC,CAAE,CAAA7G,QAAA,cAEF/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB8F,KAAK,CAAC,QAAQ,CAAApG,QAAA,cAEd/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNtO,KAAA,QAAK8N,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7N,KAAA,QAAA6N,QAAA,eACE/N,IAAA,MAAA+N,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAAsG,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAACH,QAAQ,UAAAK,sBAAA,iBAArBA,sBAAA,CAAuBpC,SAAS,UAAAmC,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNnU,KAAA,QAAA6N,QAAA,eACE/N,IAAA,MAAA+N,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAAwG,qBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACf,OAAO,UAAAmB,sBAAA,iBAApBA,sBAAA,CAAsBjB,YAAY,UAAAgB,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACNrU,KAAA,QAAA6N,QAAA,eACE/N,IAAA,MAAA+N,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAA0G,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACf,OAAO,UAAAqB,sBAAA,iBAApBA,sBAAA,CAAsBlB,kBAAkB,UAAAiB,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,cACNvU,KAAA,QAAA6N,QAAA,eACE/N,IAAA,MAAA+N,QAAA,CAAG,OAAK,CAAG,CAAC,IAAC,EAAA4G,kBAAA,CAACP,YAAY,CAACF,IAAI,UAAAS,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EACrC,CAAC,EACH,CAAC,GA9CDtU,KA+CF,CAAC,EACP,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENH,KAAA,QAAK8N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA6D,KAAK,CAAG,IAAI,CAChBlM,2BAA2B,CAAC,EAAE,CAAC,CAC/BoB,oBAAoB,CAAC,EAAE,CAAC,CAExB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI8K,KAAK,CAAE,CACTzG,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLzM,KAAK,CAACmT,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFxE,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf3L,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf/N,IAAA,QAAKgO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7N,KAAA,WACMmK,0BAA0B,CAAC,CAAE2D,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG/N,IAAA,aAAWuK,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CvK,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxO,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN/N,IAAA,UAAO+U,KAAK,CAAEvU,eAAgB,CAAAuN,QAAA,cAC5B/N,IAAA,QAAKgO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC7D,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAExK,KAAK,gBAC3CH,KAAA,QACE8N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF/N,IAAA,QAAKgO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E7N,KAAA,QACEgO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBgG,KAAK,CAAC,QAAQ,CAAApG,QAAA,eAEd/N,IAAA,SAAMwO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxO,IAAA,SAAMwO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtO,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/N,IAAA,QAAKgO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAACgF,IAAI,CACP,CAAC,cACN3P,KAAA,QAAA6N,QAAA,EACG,CAAClD,IAAI,CAACmK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjV,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACbvE,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC8H,MAAM,CACd,CAACoC,CAAC,CAAEK,aAAa,GACf7U,KAAK,GAAK6U,aACd,CACF,CAAC,CACH,CAAE,CACFlH,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB8F,KAAK,CAAC,QAAQ,CAAApG,QAAA,cAEd/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ3D,IAAI,CAACgF,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN3P,KAAA,QAAK8N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf3L,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf/N,IAAA,QAAKgO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAE5G,aAAc,CACrB6G,QAAQ,CAAGC,CAAC,EAAK7G,gBAAgB,CAAC6G,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENhP,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAExG,UAAW,CAClByG,QAAQ,CAAGC,CAAC,EAAKzG,aAAa,CAACyG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENlP,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAEpG,MAAO,CACdqG,QAAQ,CAAGC,CAAC,EAAKrG,SAAS,CAACqG,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNlP,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7N,KAAA,WACMqL,yBAAyB,CAAC,CAAEyC,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG/N,IAAA,aAAWwL,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CxL,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxO,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN/N,IAAA,UAAO+U,KAAK,CAAEvU,eAAgB,CAAAuN,QAAA,cAC5B/N,IAAA,QAAKgO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC1C,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAExK,KAAK,gBACnCH,KAAA,QACE8N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF/N,IAAA,QAAKgO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E7N,KAAA,QACEgO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBgG,KAAK,CAAC,QAAQ,CAAApG,QAAA,eAEd/N,IAAA,SAAMwO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxO,IAAA,SAAMwO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtO,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/N,IAAA,QAAKgO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAACgF,IAAI,CACP,CAAC,cACN3P,KAAA,QAAA6N,QAAA,EACG,CAAClD,IAAI,CAACmK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjV,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACbpD,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC8H,MAAM,CACd,CAACoC,CAAC,CAAEK,aAAa,GACf7U,KAAK,GAAK6U,aACd,CACF,CAAC,CACH,CAAE,CACFlH,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB8F,KAAK,CAAC,QAAQ,CAAApG,QAAA,cAEd/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ3D,IAAI,CAACgF,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN3P,KAAA,QAAK8N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlC,UAAU,GAAK,CAAC,cACf3L,KAAA,QAAK8N,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf/N,IAAA,QAAKgO,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD7N,KAAA,QAAK8N,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7N,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,CAACR,MAAM,EACL0P,KAAK,CAAEhG,gBAAiB,CACxBiG,QAAQ,CAAGE,MAAM,EAAK,CACpBlG,mBAAmB,CAACkG,MAAM,CAAC,CAC7B,CAAE,CACFS,OAAO,CAAE9C,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEpC,GAAG,CAAE+G,SAAS,GAAM,CACvCzC,KAAK,CAAEyC,SAAS,CAACC,EAAE,CACnBhC,KAAK,CAAE+B,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACzC,MAAM,CAAE0C,UAAU,GAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbZ,QAAQ,CAACW,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhE,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCe,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEjE,KAAK,IAAM,CACzB,GAAGiE,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEjH,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBkH,SAAS,CAAEpE,KAAK,CAACqE,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhB,MAAM,CAAGc,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP1P,OAAO,CAAE,MAAM,CACf+P,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAENtQ,KAAA,QAAK8N,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE/N,IAAA,UACEgO,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAExF,YAAa,CACpByF,QAAQ,CAAGC,CAAC,EAAKzF,eAAe,CAACyF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENlP,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD/N,IAAA,QAAKgO,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7N,KAAA,QAAK8N,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC/N,IAAA,QAAKgO,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN/N,IAAA,QAAA+N,QAAA,cACE7N,KAAA,WACEgP,KAAK,CAAEpF,aAAc,CACrBqF,QAAQ,CAAGC,CAAC,EAAKrF,gBAAgB,CAACqF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE,CAClDlB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF/N,IAAA,WAAQkP,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC/N,IAAA,WAAQkP,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C/N,IAAA,WAAQkP,KAAK,CAAE,UAAW,CAAAnB,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5C/N,IAAA,WAAQkP,KAAK,CAAE,QAAS,CAAAnB,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN/N,IAAA,QAAKgO,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN7N,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7N,KAAA,WACMyL,wCAAwC,CAAC,CAC3CqC,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG/N,IAAA,aAAW4L,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1D5L,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNxO,IAAA,QAAKgO,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN/N,IAAA,UAAO+U,KAAK,CAAEvU,eAAgB,CAAAuN,QAAA,cAC5B/N,IAAA,QAAKgO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCtC,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAExK,KAAK,gBACVH,KAAA,QACE8N,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF/N,IAAA,QAAKgO,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E7N,KAAA,QACEgO,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBgG,KAAK,CAAC,QAAQ,CAAApG,QAAA,eAEd/N,IAAA,SAAMwO,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxO,IAAA,SAAMwO,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtO,KAAA,QAAK8N,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD/N,IAAA,QAAKgO,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FlD,IAAI,CAACgF,IAAI,CACP,CAAC,cACN3P,KAAA,QAAA6N,QAAA,EACG,CAAClD,IAAI,CAACmK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNjV,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM,CACbhD,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC8H,MAAM,CACd,CAACoC,CAAC,CAAEK,aAAa,GACf7U,KAAK,GAAK6U,aACd,CACJ,CAAC,CACH,CAAE,CACFlH,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB8F,KAAK,CAAC,QAAQ,CAAApG,QAAA,cAEd/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ3D,IAAI,CAACgF,IAgDP,CAET,CAAC,CACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN3P,KAAA,QAAK8N,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D/N,IAAA,WACE0O,OAAO,CAAEA,CAAA,GAAM5C,aAAa,CAAC,CAAC,CAAE,CAChCkC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT/N,IAAA,WACE2S,QAAQ,CAAEhG,cAAe,CACzB+B,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAAyG,mBAAA,CACnB,KAAM,CAAAC,aAAa,CAAGnR,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE2G,GAAG,CAC3CqH,IAAI,OAAAoD,aAAA,CAAAC,cAAA,OAAM,CACTjC,OAAO,EAAAgC,aAAA,CAAEpD,IAAI,CAACoB,OAAO,UAAAgC,aAAA,iBAAZA,aAAA,CAAczD,EAAE,CACzBqC,QAAQ,EAAAqB,cAAA,CAAErD,IAAI,CAACgC,QAAQ,UAAAqB,cAAA,iBAAbA,cAAA,CAAe1D,EAAE,CAC3BsC,IAAI,CAAEjC,IAAI,CAACiC,IACb,CAAC,EACH,CAAC,CACD,KAAM,CAAAlT,QAAQ,CACZzB,UAAU,CAAC,CACTgW,UAAU,CAAEtU,SAAS,CAACuU,IAAI,CAAC,CAAC,CAC5BC,SAAS,CAAEpU,QAAQ,CAACmU,IAAI,CAAC,CAAC,CAC1BtD,SAAS,CAAEjR,SAAS,CAACuU,IAAI,CAAC,CAAC,CAAG,GAAG,CAAGnU,QAAQ,CAACmU,IAAI,CAAC,CAAC,CACnDE,SAAS,CAAE7T,SAAS,CACpB8T,aAAa,CAAE1T,KAAK,CACpB2T,aAAa,CAAEnU,KAAK,CACpBoU,eAAe,CAAExT,OAAO,CACxByT,YAAY,CAAErT,IAAI,CAClBsT,eAAe,CAAElT,OAAO,CAACqM,KAAK,CAC9B;AACAvL,WAAW,CAAEA,WAAW,CAACuL,KAAK,CAC9B8G,SAAS,CAAErR,QAAQ,CACnBsR,SAAS,CAAE/Q,QAAQ,CACnBgR,cAAc,CACZhR,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC5C6Q,gBAAgB,CAAEzQ,eAAe,CACjC;AACA0Q,mBAAmB,CAAEtQ,eAAe,CACpCuQ,WAAW,CAAEnQ,mBAAmB,CAChCoQ,gBAAgB,CACdhR,YAAY,GAAK,WAAW,CACxB,EAAE,CACFgB,eAAe,CACrBiQ,UAAU,CACRjR,YAAY,GAAK,WAAW,CAAGoB,SAAS,CAAG,EAAE,CAC/C8P,QAAQ,CACNlR,YAAY,GAAK,WAAW,CAAGwB,OAAO,CAAG,EAAE,CAC7C2P,gBAAgB,CAAEvP,eAAe,CACjC+M,QAAQ,CAAE3M,YAAY,CAAC4H,KAAK,CAC5B;AACAwH,cAAc,CAAEpO,aAAa,CAC7BqO,WAAW,CAAEjO,UAAU,CACvBkO,cAAc,CAAE9N,MAAM,CACtB6I,SAAS,CAAEzI,gBAAgB,CAACgG,KAAK,CACjC2H,gBAAgB,CAAEvN,eAAe,CACjCwN,aAAa,CAAEpN,YAAY,CAC3BqN,gBAAgB,CAAEjN,aAAa,CAC/B;AACAkN,uBAAuB,CAAE9M,0BAA0B,CACnD+M,cAAc,CAAE5L,kBAAkB,CAClC6L,8BAA8B,CAC5BzL,iCAAiC,CACnC;AACAa,SAAS,CAAE8I,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9B;AACA+B,MAAM,CAAElU,KAAK,CAAG,MAAM,CAAG,OAAO,CAChCmU,WAAW,CAAE7T,UAAU,CACvB8T,cAAc,EAAAlC,mBAAA,CAAEhS,YAAY,CAAC+L,KAAK,UAAAiG,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACFnH,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEpB,cAAc,CAAG,WAAW,CAAG,QAAQ,CAClC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPd,UAAU,GAAK,CAAC,cACf7L,IAAA,QAAKgO,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf/N,IAAA,QAAKgO,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD7N,KAAA,QAAK8N,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjE/N,IAAA,QACEkO,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9E/N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwO,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNxO,IAAA,QAAKgO,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACN/N,IAAA,QAAKgO,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1D/N,IAAA,MACEiO,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAAlN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}