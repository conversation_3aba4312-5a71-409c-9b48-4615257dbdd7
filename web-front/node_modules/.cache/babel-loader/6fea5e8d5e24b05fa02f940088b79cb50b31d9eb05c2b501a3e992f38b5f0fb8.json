{"ast": null, "code": "import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\nimport { confirmResetPasswordReducer, coordinatorsListReducer, createCoordinatorReducer, createNewUserReducer, deleteUserReducer, detailCoordinatorReducer, getProfileUserReducer, historyListCoordinatorReducer, historyListLoggedReducer, logoutSavedUserReducer, resetPasswordReducer, updateCoordinatorReducer, updateLastLoginUserReducer, updatePasswordUserReducer, updateProfileUserReducer, userLoginReducer, usersListReducer } from \"./reducers/userReducers\";\nimport { clientListReducer, createNewClientReducer, deleteClientReducer, detailClientReducer, updateClientReducer } from \"./reducers/clientReducers\";\nimport { caseListCoordinatorReducer, caseListInsuranceReducer, caseListLoggedReducer, caseListMapReducer, caseListProviderReducer, caseListReducer, caseHistoryReducer, commentCaseListReducer, createNewCaseReducer, createNewCommentCaseReducer, deleteCaseReducer, deleteCommentCaseReducer, detailCaseReducer, duplicateCaseReducer, updateCaseAssignedReducer, updateCaseReducer, updateCaseStatusReducer, updateCaseStepReducer } from \"./reducers/caseReducers\";\nimport { addNewProviderReducer, deleteProviderReducer, detailProviderReducer, providerListReducer, updateProviderReducer } from \"./reducers/providerReducers\";\nimport { addNewInsuranceReducer, deleteInsuranceReducer, detailInsuranceReducer, insuranceListReducer, updateInsuranceReducer } from \"./reducers/insurancereducers\";\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  updateCaseStep: updateCaseStepReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer\n\n  //\n});\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\") ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")) : null;\nconst initialState = {\n  userLogin: {\n    userInfo: userInfoFromStorage\n  }\n};\nconst middleware = [thunk];\nconst store = createStore(reducer, initialState, applyMiddleware(...middleware));\nexport default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "confirmResetPasswordReducer", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "logoutSavedUserReducer", "resetPasswordReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListMapReducer", "caseListProviderReducer", "caseListReducer", "caseHistoryReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "deleteCommentCaseReducer", "detailCaseReducer", "duplicateCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "updateCaseStatusReducer", "updateCaseStepReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "caseListMap", "detailCase", "createNewCase", "deleteCase", "updateCase", "updateCaseStatus", "updateCaseStep", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "duplicateCase", "caseHistory", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "deleteCommentCase", "logoutSavedUser", "resetPassword", "confirmResetPassword", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  confirmResetPasswordReducer,\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  logoutSavedUserReducer,\n  resetPasswordReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListMapReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  caseHistoryReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  deleteCommentCaseReducer,\n  detailCaseReducer,\n  duplicateCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n  updateCaseStatusReducer,\n  updateCaseStepReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  updateCaseStep: updateCaseStepReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer,\n\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,OAAO;AACrE,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,SACEC,2BAA2B,EAC3BC,uBAAuB,EACvBC,wBAAwB,EACxBC,oBAAoB,EACpBC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,sBAAsB,EACtBC,oBAAoB,EACpBC,wBAAwB,EACxBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAChC,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,QACd,2BAA2B;AAElC,SACEC,0BAA0B,EAC1BC,wBAAwB,EACxBC,qBAAqB,EACrBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,iBAAiB,EACjBC,wBAAwB,EACxBC,iBAAiB,EACjBC,oBAAoB,EACpBC,yBAAyB,EACzBC,iBAAiB,EACjBC,uBAAuB,EACvBC,qBAAqB,QAChB,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,QAChB,6BAA6B;AACpC,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,sBAAsB,QACjB,8BAA8B;AAErC,MAAMC,OAAO,GAAGtD,eAAe,CAAC;EAC9BuD,SAAS,EAAEpC,gBAAgB;EAE3B;EACAqC,QAAQ,EAAEzB,eAAe;EACzB0B,WAAW,EAAE5B,kBAAkB;EAC/B6B,UAAU,EAAEpB,iBAAiB;EAC7BqB,aAAa,EAAEzB,oBAAoB;EACnC0B,UAAU,EAAExB,iBAAiB;EAC7ByB,UAAU,EAAEpB,iBAAiB;EAC7BqB,gBAAgB,EAAEpB,uBAAuB;EACzCqB,cAAc,EAAEpB,qBAAqB;EACrCqB,mBAAmB,EAAEtC,0BAA0B;EAC/CuC,kBAAkB,EAAEzB,yBAAyB;EAC7C0B,iBAAiB,EAAEvC,wBAAwB;EAC3CwC,gBAAgB,EAAErC,uBAAuB;EACzCsC,cAAc,EAAExC,qBAAqB;EACrCyC,aAAa,EAAE9B,oBAAoB;EACnC+B,WAAW,EAAEtC,kBAAkB;EAC/B;EACAuC,YAAY,EAAExB,mBAAmB;EACjCyB,cAAc,EAAE1B,qBAAqB;EACrC2B,cAAc,EAAE7B,qBAAqB;EACrC8B,cAAc,EAAE7B,qBAAqB;EACrC8B,cAAc,EAAE3B,qBAAqB;EACrC;EACA4B,UAAU,EAAEvD,iBAAiB;EAC7BwD,eAAe,EAAEvD,sBAAsB;EACvCwD,YAAY,EAAEtD,mBAAmB;EACjCuD,YAAY,EAAEtD,mBAAmB;EACjCuD,YAAY,EAAEzD,mBAAmB;EACjC;EACA0D,aAAa,EAAE7B,oBAAoB;EACnC8B,eAAe,EAAEjC,sBAAsB;EACvCkC,eAAe,EAAEjC,sBAAsB;EACvCkC,eAAe,EAAEjC,sBAAsB;EACvCkC,eAAe,EAAEhC,sBAAsB;EAEvC;EACAiC,SAAS,EAAElE,gBAAgB;EAC3BmE,aAAa,EAAEhF,oBAAoB;EACnCiF,cAAc,EAAE9E,qBAAqB;EACrC+E,iBAAiB,EAAEvE,wBAAwB;EAC3CwE,UAAU,EAAElF,iBAAiB;EAC7BmF,kBAAkB,EAAE1E,yBAAyB;EAC7C2E,mBAAmB,EAAE5E,0BAA0B;EAC/C6E,iBAAiB,EAAEjF,wBAAwB;EAC3CkF,sBAAsB,EAAEnF,6BAA6B;EACrD;EACAoF,gBAAgB,EAAE1F,uBAAuB;EACzC2F,iBAAiB,EAAE1F,wBAAwB;EAC3C2F,iBAAiB,EAAExF,wBAAwB;EAC3CyF,iBAAiB,EAAEnF,wBAAwB;EAC3C;EACAoF,eAAe,EAAElE,sBAAsB;EACvCmE,oBAAoB,EAAEjE,2BAA2B;EACjDkE,iBAAiB,EAAEhE,wBAAwB;EAC3C;EACAiE,eAAe,EAAEzF,sBAAsB;EACvC0F,aAAa,EAAEzF,oBAAoB;EACnC0F,oBAAoB,EAAEpG;;EAEtB;AACF,CAAC,CAAC;AAEF,MAAMqG,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,GAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GACtD,IAAI;AAER,MAAMG,YAAY,GAAG;EACnBvD,SAAS,EAAE;IAAEwD,QAAQ,EAAEN;EAAoB;AAC7C,CAAC;AAED,MAAMO,UAAU,GAAG,CAAC9G,KAAK,CAAC;AAE1B,MAAM+G,KAAK,GAAGlH,WAAW,CACvBuD,OAAO,EACPwD,YAAY,EACZ7G,eAAe,CAAC,GAAG+G,UAAU,CAC/B,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}