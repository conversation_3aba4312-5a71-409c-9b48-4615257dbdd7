{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListEditCase } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\n// Country to Currency mapping - using exact country names from COUNTRIES constant\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COUNTRY_CURRENCY_MAP = {\n  \"Morocco\": \"MAD\",\n  \"United States\": \"USD\",\n  \"Canada\": \"CAD\",\n  \"United Kingdom\": \"GBP\",\n  \"France\": \"EUR\",\n  \"Germany\": \"EUR\",\n  \"Spain\": \"EUR\",\n  \"Italy\": \"EUR\",\n  \"Netherlands\": \"EUR\",\n  \"Belgium\": \"EUR\",\n  \"Portugal\": \"EUR\",\n  \"Greece\": \"EUR\",\n  \"Austria\": \"EUR\",\n  \"Ireland\": \"EUR\",\n  \"Finland\": \"EUR\",\n  \"Luxembourg\": \"EUR\",\n  \"Estonia\": \"EUR\",\n  \"Slovenia\": \"EUR\",\n  \"Slovakia\": \"EUR\",\n  \"Malta\": \"EUR\",\n  \"Cyprus\": \"EUR\",\n  \"Lithuania\": \"EUR\",\n  \"Latvia\": \"EUR\",\n  \"Japan\": \"JPY\",\n  \"China\": \"CNY\",\n  \"India\": \"INR\",\n  \"Australia\": \"AUD\",\n  \"New Zealand\": \"NZD\",\n  \"South Africa\": \"ZAR\",\n  \"Brazil\": \"BRL\",\n  \"Mexico\": \"MXN\",\n  \"Argentina\": \"ARS\",\n  \"Chile\": \"CLP\",\n  \"Colombia\": \"COP\",\n  \"Peru\": \"PEN\",\n  \"Russia\": \"RUB\",\n  \"Turkey\": \"TRY\",\n  \"Egypt\": \"EGP\",\n  \"Saudi Arabia\": \"SAR\",\n  \"United Arab Emirates\": \"AED\",\n  \"Qatar\": \"QAR\",\n  \"Kuwait\": \"KWD\",\n  \"Bahrain\": \"BHD\",\n  \"Oman\": \"OMR\",\n  \"Jordan\": \"JOD\",\n  \"Lebanon\": \"LBP\",\n  \"Israel\": \"ILS\",\n  \"South Korea\": \"KRW\",\n  \"Thailand\": \"THB\",\n  \"Malaysia\": \"MYR\",\n  \"Singapore\": \"SGD\",\n  \"Indonesia\": \"IDR\",\n  \"Philippines\": \"PHP\",\n  \"Vietnam\": \"VND\",\n  \"Pakistan\": \"PKR\",\n  \"Bangladesh\": \"BDT\",\n  \"Sri Lanka\": \"LKR\",\n  \"Nepal\": \"NPR\",\n  \"Switzerland\": \"CHF\",\n  \"Norway\": \"NOK\",\n  \"Sweden\": \"SEK\",\n  \"Denmark\": \"DKK\",\n  \"Iceland\": \"ISK\",\n  \"Poland\": \"PLN\",\n  \"Czech Republic\": \"CZK\",\n  \"Hungary\": \"HUF\",\n  \"Romania\": \"RON\",\n  \"Bulgaria\": \"BGN\",\n  \"Croatia\": \"HRK\",\n  \"Serbia\": \"RSD\",\n  \"Ukraine\": \"UAH\",\n  \"Belarus\": \"BYN\",\n  \"Algeria\": \"DZD\",\n  \"Tunisia\": \"TND\",\n  \"Libya\": \"LYD\",\n  \"Sudan\": \"SDG\",\n  \"Ethiopia\": \"ETB\",\n  \"Kenya\": \"KES\",\n  \"Uganda\": \"UGX\",\n  \"Tanzania\": \"TZS\",\n  \"Rwanda\": \"RWF\",\n  \"Ghana\": \"GHS\",\n  \"Nigeria\": \"NGN\",\n  \"Senegal\": \"XOF\",\n  \"Ivory Coast\": \"XOF\",\n  \"Mali\": \"XOF\",\n  \"Burkina Faso\": \"XOF\",\n  \"Niger\": \"XOF\",\n  \"Guinea\": \"GNF\",\n  \"Sierra Leone\": \"SLL\",\n  \"Liberia\": \"LRD\",\n  \"Cameroon\": \"XAF\",\n  \"Chad\": \"XAF\",\n  \"Central African Republic\": \"XAF\",\n  \"Democratic Republic of the Congo\": \"CDF\",\n  \"Republic of the Congo\": \"XAF\",\n  \"Gabon\": \"XAF\",\n  \"Angola\": \"AOA\",\n  \"Zambia\": \"ZMK\",\n  \"Zimbabwe\": \"ZWL\",\n  \"Botswana\": \"BWP\",\n  \"Namibia\": \"NAD\",\n  \"Lesotho\": \"LSL\",\n  \"Swaziland\": \"SZL\",\n  \"Mozambique\": \"MZN\",\n  \"Madagascar\": \"MGA\",\n  \"Mauritius\": \"MUR\",\n  \"Seychelles\": \"SCR\",\n  \"Afghanistan\": \"AFN\",\n  \"Albania\": \"ALL\",\n  \"Armenia\": \"AMD\",\n  \"Azerbaijan\": \"AZN\",\n  \"Brunei\": \"BND\",\n  \"Cambodia\": \"KHR\",\n  \"Cape Verde\": \"CVE\",\n  \"Comoros\": \"KMF\",\n  \"Costa Rica\": \"CRC\",\n  \"Cuba\": \"CUP\",\n  \"Dominican Republic\": \"DOP\",\n  \"Ecuador\": \"USD\",\n  \"El Salvador\": \"USD\",\n  \"Eritrea\": \"ERN\",\n  \"Fiji\": \"FJD\",\n  \"Georgia\": \"GEL\",\n  \"Guatemala\": \"GTQ\",\n  \"Guinea-Bissau\": \"XOF\",\n  \"Guyana\": \"GYD\",\n  \"Haiti\": \"HTG\",\n  \"Honduras\": \"HNL\",\n  \"Hong Kong\": \"HKD\",\n  \"Iran\": \"IRR\",\n  \"Iraq\": \"IQD\",\n  \"Jamaica\": \"JMD\",\n  \"Kazakhstan\": \"KZT\",\n  \"Kyrgyzstan\": \"KGS\",\n  \"Laos\": \"LAK\",\n  \"Macau\": \"MOP\",\n  \"Macedonia\": \"MKD\",\n  \"Malawi\": \"MWK\",\n  \"Maldives\": \"MVR\",\n  \"Marshall Islands\": \"USD\",\n  \"Mauritania\": \"MRU\",\n  \"Micronesia\": \"USD\",\n  \"Moldova\": \"MDL\",\n  \"Monaco\": \"EUR\",\n  \"Mongolia\": \"MNT\",\n  \"Montenegro\": \"EUR\",\n  \"Myanmar\": \"MMK\",\n  \"Nicaragua\": \"NIO\",\n  \"North Korea\": \"KPW\",\n  \"Panama\": \"PAB\",\n  \"Papua New Guinea\": \"PGK\",\n  \"Paraguay\": \"PYG\",\n  \"Puerto Rico\": \"USD\",\n  \"Samoa\": \"WST\",\n  \"San Marino\": \"EUR\",\n  \"Sao Tome and Principe\": \"STN\",\n  \"Somalia\": \"SOS\",\n  \"South Sudan\": \"SSP\",\n  \"Suriname\": \"SRD\",\n  \"Syria\": \"SYP\",\n  \"Taiwan\": \"TWD\",\n  \"Tajikistan\": \"TJS\",\n  \"Togo\": \"XOF\",\n  \"Tonga\": \"TOP\",\n  \"Trinidad and Tobago\": \"TTD\",\n  \"Turkmenistan\": \"TMT\",\n  \"Tuvalu\": \"AUD\",\n  \"Uruguay\": \"UYU\",\n  \"Uzbekistan\": \"UZS\",\n  \"Vanuatu\": \"VUV\",\n  \"Venezuela\": \"VES\",\n  \"Western Sahara\": \"MAD\",\n  \"Yemen\": \"YER\"\n};\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & Assistance Details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction AddCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  const [isPay, setIsPay] = useState(false);\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const createCase = useSelector(state => state.createNewCase);\n  const {\n    loadingCaseAdd,\n    successCaseAdd,\n    errorCaseAdd\n  } = createCase;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n      setStepSelect(0);\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListEditCase(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 4000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (!loadingProviders && !loadingInsurances && !loadingCoordinators && providers && providers.length > 0 && coordinators && coordinators.length > 0) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [loadingProviders, loadingInsurances, loadingCoordinators, providers, coordinators]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-700 font-medium\",\n          children: \"Loading data...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index && stepSelect !== 5) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index && stepSelect !== 5 ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 618,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 661,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 664,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 682,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 686,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 685,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 720,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 732,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 742,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n\n                          // Auto-update currency based on selected country\n                          if (option && option.value) {\n                            // The option.value contains the country title (name)\n                            const countryName = option.value;\n                            const mappedCurrencyCode = COUNTRY_CURRENCY_MAP[countryName];\n                            if (mappedCurrencyCode) {\n                              // Find the currency option in CURRENCYITEMS\n                              const currencyOption = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(currency => currency.code === mappedCurrencyCode);\n                              if (currencyOption) {\n                                // Use setTimeout to ensure the state update happens after the country is set\n                                setTimeout(() => {\n                                  setCurrencyCode({\n                                    value: currencyOption.code,\n                                    label: currencyOption.name !== \"\" ? currencyOption.name + \" (\" + currencyOption.code + \")\" : currencyOption.code\n                                  });\n\n                                  // Show success message\n                                  toast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);\n                                }, 100);\n                              } else {\n                                toast.info(`Country updated to ${countryName}. Currency code ${mappedCurrencyCode} not found in currency list.`);\n                              }\n                            } else {\n                              toast.info(`Country updated to ${countryName}. Please select currency manually.`);\n                            }\n                          }\n                        },\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 792,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 793,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 787,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        className: \"text-sm\",\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 825,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 741,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 832,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n\n                            // Extract country from Google Places result\n                            if (place.address_components) {\n                              const countryComponent = place.address_components.find(component => component.types.includes('country'));\n                              if (countryComponent) {\n                                const countryName = countryComponent.long_name;\n\n                                // Find matching country in COUNTRIES constant\n                                const foundCountry = COUNTRIES.find(country => country.title === countryName);\n                                if (foundCountry) {\n                                  // Set the country\n                                  setCountry({\n                                    value: foundCountry.title,\n                                    label: /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex flex-row items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"mr-2\",\n                                        children: foundCountry.icon\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 867,\n                                        columnNumber: 45\n                                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                        children: foundCountry.title\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 868,\n                                        columnNumber: 45\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 866,\n                                      columnNumber: 43\n                                    }, this)\n                                  });\n\n                                  // Auto-update currency based on selected country\n                                  const currencyCode = COUNTRY_CURRENCY_MAP[countryName];\n                                  if (currencyCode) {\n                                    // Find the currency option in CURRENCYITEMS\n                                    const currencyOption = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(currency => currency.code === currencyCode);\n                                    if (currencyOption) {\n                                      setCurrencyCode({\n                                        value: currencyOption.code,\n                                        label: currencyOption.name !== \"\" ? currencyOption.name + \" (\" + currencyOption.code + \")\" : currencyOption.code\n                                      });\n\n                                      // Show success message\n                                      toast.success(`Country and currency automatically updated: ${countryName} - ${currencyOption.name} (${currencyOption.code})`);\n                                    }\n                                  } else {\n                                    // Show message if country is set but currency mapping not found\n                                    toast.info(`Country updated to ${countryName}. Please select currency manually.`);\n                                  }\n                                } else {\n                                  // Show message if country not found in our list\n                                  toast.info(`City selected. Country \"${countryName}\" not found in our list. Please select country and currency manually.`);\n                                }\n                              }\n                            }\n\n                            // Optional: Extract coordinates if needed in the future\n                            // const latitude = place.geometry.location.lat();\n                            // const longitude = place.geometry.location.lng();\n                            // setLocationX(latitude ?? \"\");\n                            // setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 924,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 976,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 997,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1012,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1017,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1058,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1010,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1069,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1072,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1083,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1071,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1091,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1090,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1103,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1104,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1105,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1094,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1107,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1093,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1089,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 21\n                }, this), caseType === \"Medical\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"md:w-1/2  w-full  md:pl-1 my-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#B4B4B4] text-xs  mb-1\",\n                    children: [\"Type Item \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      className: \"text-danger\",\n                      children: \"*\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1117,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                      value: caseTypeItem,\n                      onChange: v => setCaseTypeItem(v.target.value),\n                      className: ` outline-none border ${caseTypeItemError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Type Item\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Outpatient\",\n                        children: \"Outpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1130,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"Inpatient\",\n                        children: \"Inpatient\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1131,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \" text-[8px] text-danger\",\n                      children: caseTypeItemError ? caseTypeItemError : \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1115,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Currency Code\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1144,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1142,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: currencyCode,\n                        onChange: option => {\n                          setCurrencyCode(option);\n                        },\n                        options: CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.map(currency => ({\n                          value: currency.code,\n                          label: currency.name !== \"\" ? currency.name + \" (\" + currency.code + \") \" || \"\" : \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Currency Code ...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: currencyCodeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1147,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: currencyCodeError ? currencyCodeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1194,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1146,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Price of service\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1202,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1200,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${priceTotalError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"number\",\n                        min: 0,\n                        step: 0.01,\n                        placeholder: \"0.00\",\n                        value: priceTotal,\n                        onChange: v => setPriceTotal(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1205,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: priceTotalError ? priceTotalError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1218,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1204,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"ispay\",\n                        id: \"ispay\",\n                        checked: isPay === true,\n                        onChange: v => {\n                          setIsPay(true);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1227,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"ispay\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1236,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1226,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1225,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"notpay\",\n                        id: \"notpay\",\n                        checked: isPay === false,\n                        onChange: v => {\n                          setIsPay(false);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1246,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"notpay\",\n                        children: \"Unpaid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1255,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1245,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1244,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1268,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1272,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1271,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1267,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseTypeItemError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    setCurrencyCodeError(\"\");\n                    setPriceTotalError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (city === \"\") {\n                      setCityError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (currencyCode === \"\" || currencyCode.value === \"\") {\n                      setCurrencyCodeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (priceTotal === \"\") {\n                      setPriceTotalError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    } else if (caseType === \"Medical\" && caseTypeItem === \"\") {\n                      setCaseTypeItemError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1371,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1382,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1381,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"pending-coordination\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"pending-coordination\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"pending-coordination\"));\n                              }\n                            },\n                            id: \"pending-coordination\",\n                            type: \"checkbox\",\n                            checked: coordinatStatusList.includes(\"pending-coordination\"),\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1387,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"pending-coordination\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Pending Coordination\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1414,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1386,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-m-r\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-m-r\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-m-r\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-m-r\"),\n                            id: \"coordinated-Missing-m-r\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1422,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-Missing-m-r\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing M.R.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1449,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1421,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-invoice\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-invoice\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-invoice\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-invoice\"),\n                            id: \"coordinated-missing-invoice\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1457,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-invoice\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1485,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1456,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"waiting-for-insurance-authorization\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"waiting-for-insurance-authorization\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),\n                            id: \"waiting-for-insurance-authorization\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1493,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"waiting-for-insurance-authorization\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Waiting for Insurance Authorization\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1521,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1492,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-patient-not-seen-yet\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-patient-not-seen-yet\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),\n                            id: \"coordinated-patient-not-seen-yet\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1529,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-patient-not-seen-yet\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Patient not seen yet\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1557,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1528,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordination-fee\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordination-fee\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordination-fee\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordination-fee\"),\n                            id: \"coordination-fee\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1566,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordination-fee\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordination Fee\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1593,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1565,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-payment\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-payment\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-payment\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-payment\"),\n                            id: \"coordinated-missing-payment\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1602,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-payment\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Payment\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1630,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1601,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"fully-coordinated\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"fully-coordinated\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"fully-coordinated\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"fully-coordinated\"),\n                            id: \"fully-coordinated\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1640,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"fully-coordinated\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Fully Coordinated\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1667,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1639,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"failed\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"failed\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"failed\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"failed\"),\n                            id: \"failed\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1675,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"failed\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Failed\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1695,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1674,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1385,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusListError ? coordinatStatusListError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1735,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1384,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1380,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Assistance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1745,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-medium mt-2 mb-2 text-black\",\n                  children: \"Appointment Details:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1750,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col w-full \",\n                  children: caseType === \"Medical\" && caseTypeItem === \"Inpatient\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex md:flex-row flex-col w-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-[#B4B4B4] text-xs  mb-1\",\n                        children: \"Hospital Starting Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1758,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"input\", {\n                          className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                          type: \"date\",\n                          placeholder: \"Hospital Starting Date\",\n                          value: startDate,\n                          onChange: v => {\n                            setStartDate(v.target.value);\n                            // If end date is earlier than new start date, update end date\n                            if (endDate && endDate < v.target.value) {\n                              setEndDate(v.target.value);\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1762,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1761,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1757,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-[#B4B4B4] text-xs  mb-1\",\n                        children: \"Hospital Ending Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1778,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(\"input\", {\n                          className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                          type: \"date\",\n                          placeholder: \"Hospital Ending Date\",\n                          value: endDate,\n                          onChange: v => setEndDate(v.target.value),\n                          disabled: !startDate,\n                          min: startDate\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1782,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1781,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1777,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1756,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1796,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1800,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1799,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1795,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1753,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1817,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1821,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1820,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1816,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1814,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-medium mt-2 mb-2 text-black\",\n                  children: \"Provider Information:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1833,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1838,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          // Show loading indicator while fetching provider services\n                          setIsLoading(true);\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                            // Hide loading indicator after services are loaded\n                            setTimeout(() => {\n                              setIsLoading(false);\n                            }, 100);\n                          } else {\n                            setProviderServices([]);\n                            setIsLoading(false);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\",\n                          city: item.city || \"\",\n                          country: item.country || \"\"\n                        })),\n                        filterOption: (option, inputValue) => {\n                          var _option$label, _option$city, _option$country;\n                          // تحسين البحث ليشمل الاسم والمدينة والبلد\n                          const searchTerm = inputValue === null || inputValue === void 0 ? void 0 : inputValue.toLowerCase();\n                          return ((_option$label = option.label) === null || _option$label === void 0 ? void 0 : _option$label.toLowerCase().includes(searchTerm)) || ((_option$city = option.city) === null || _option$city === void 0 ? void 0 : _option$city.toLowerCase().includes(searchTerm)) || ((_option$country = option.country) === null || _option$country === void 0 ? void 0 : _option$country.toLowerCase().includes(searchTerm));\n                        },\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true\n                        // Add loading indicator\n                        ,\n                        isLoading: loadingProviders,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1842,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerNameError ? providerNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1917,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1841,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1924,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: `outline-none border ${providerServiceError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1939,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => {\n                          var _service$service_type;\n                          return /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: service.id,\n                            children: [(_service$service_type = service.service_type) !== null && _service$service_type !== void 0 ? _service$service_type : \"\", service.service_specialist !== \"\" ? \" : \" + service.service_specialist : \"\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1941,\n                            columnNumber: 31\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1928,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerServiceError ? providerServiceError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1949,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1927,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1923,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Visit Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1957,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${providerDateError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Visit Date\",\n                        value: providerDate,\n                        onChange: v => setProviderDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1961,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerDateError ? providerDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1972,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1960,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1956,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1955,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // providerMultiSelect\n                      var check = true;\n                      setProviderNameError(\"\");\n                      setProviderServiceError(\"\");\n                      setProviderDateError(\"\");\n                      if (providerName === \"\" || providerName.value === \"\") {\n                        setProviderNameError(\"These fields are required.\");\n                        toast.error(\"Provider is required\");\n                        check = false;\n                      }\n                      if (providerService === \"\") {\n                        setProviderServiceError(\"These fields are required.\");\n                        toast.error(\"Provider Service is required\");\n                        check = false;\n                      }\n                      if (providerDate === \"\") {\n                        setProviderDateError(\"These fields are required.\");\n                        toast.error(\"Visit Date is required\");\n                        check = false;\n                      }\n                      if (check) {\n                        const exists = false;\n                        // const exists = providerMultiSelect.some(\n                        //   (provider) =>\n                        //     String(provider?.provider?.id) ===\n                        //       String(providerName.value) &&\n                        //     String(provider?.service?.id) ===\n                        //       String(providerService)\n                        // );\n\n                        if (!exists) {\n                          var _providerName$value;\n                          // find provider\n                          var initialProvider = (_providerName$value = providerName.value) !== null && _providerName$value !== void 0 ? _providerName$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => String(item.id) === String(initialProvider));\n                          console.log(foundProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2, _foundProvider$servic3;\n                            // found service\n                            var initialService = providerService !== null && providerService !== void 0 ? providerService : \"\";\n                            foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic2 = foundProvider.services) === null || _foundProvider$servic2 === void 0 ? void 0 : _foundProvider$servic2.forEach(element => {\n                              console.log(element.id);\n                            });\n                            const foundService = foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic3 = foundProvider.services) === null || _foundProvider$servic3 === void 0 ? void 0 : _foundProvider$servic3.find(item => String(item.id) === String(initialService));\n                            if (foundService) {\n                              // Add the new item if it doesn't exist\n                              setProviderMultiSelect([...providerMultiSelect, {\n                                provider: foundProvider,\n                                service: foundService,\n                                date: providerDate\n                              }]);\n                              setProviderName(\"\");\n                              setProviderService(\"\");\n                              setProviderDate(\"\");\n                              console.log(providerMultiSelect);\n                            } else {\n                              setProviderNameError(\"This provider service not exist!\");\n                              toast.error(\"This provider service not exist!\");\n                            }\n                          } else {\n                            setProviderNameError(\"This provider not exist!\");\n                            toast.error(\"This provider not exist!\");\n                          }\n                        } else {\n                          setProviderNameError(\"This provider or service is already added!\");\n                          toast.error(\"This provider or service is already added!\");\n                        }\n                      }\n                    },\n                    className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2088,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2080,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \" Add Provider \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2094,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1980,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                      children: \"Providers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2097,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-black text-sm\",\n                      children: providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map((itemProvider, index) => {\n                        var _itemProvider$provide, _itemProvider$provide2, _itemProvider$service, _itemProvider$service2, _itemProvider$service3, _itemProvider$service4, _itemProvider$date;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelect.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelect(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 2124,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2116,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2107,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2106,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2134,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$provide = (_itemProvider$provide2 = itemProvider.provider) === null || _itemProvider$provide2 === void 0 ? void 0 : _itemProvider$provide2.full_name) !== null && _itemProvider$provide !== void 0 ? _itemProvider$provide : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2133,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2138,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service = (_itemProvider$service2 = itemProvider.service) === null || _itemProvider$service2 === void 0 ? void 0 : _itemProvider$service2.service_type) !== null && _itemProvider$service !== void 0 ? _itemProvider$service : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2137,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2142,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service3 = (_itemProvider$service4 = itemProvider.service) === null || _itemProvider$service4 === void 0 ? void 0 : _itemProvider$service4.service_specialist) !== null && _itemProvider$service3 !== void 0 ? _itemProvider$service3 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2141,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Date:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2147,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$date = itemProvider.date) !== null && _itemProvider$date !== void 0 ? _itemProvider$date : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2146,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2132,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2102,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2100,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2096,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1979,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusListError(\"\");\n                    setProviderNameError(\"\");\n\n                    // if (coordinatStatusList.length === 0) {\n                    //   setCoordinatStatusListError(\n                    //     \"This fields is required.\"\n                    //   );\n                    //   check = false;\n                    // }\n\n                    // if (providerMultiSelect.length === 0) {\n                    //   setProviderNameError(\n                    //     \"Please select this and click Add Provider.\"\n                    //   );\n                    //   check = false;\n                    // }\n\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2164,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1370,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2225,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2217,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2232,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2250,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2251,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2244,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2243,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2255,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2258,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2254,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2281,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2273,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2262,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2239,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2237,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2301,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2294,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2201,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2323,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2327,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2326,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2322,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2338,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2342,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2341,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2337,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2321,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2355,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2359,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2358,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2354,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2353,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2389,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2381,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2380,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2396,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2414,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2415,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2408,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2407,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2419,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2422,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2418,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2445,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2437,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2426,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2403,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2401,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2400,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2460,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2466,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2312,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2478,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2482,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2488,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2492,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2491,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2487,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2537,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2541,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2540,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2536,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2486,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2553,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2559,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2568,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2569,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2570,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2571,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2563,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2562,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2558,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2557,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2556,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2589,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2599,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2591,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2590,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2606,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2582,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2625,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2626,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2619,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2618,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2630,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2633,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2629,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2657,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2649,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2637,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2614,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2611,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2610,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2672,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseAdd,\n                  onClick: async () => {\n                    var _currencyCode$value;\n                    const providerItems = providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map(item => {\n                      var _item$service, _item$provider;\n                      return {\n                        service: (_item$service = item.service) === null || _item$service === void 0 ? void 0 : _item$service.id,\n                        provider: (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.id,\n                        date: item.date\n                      };\n                    });\n                    await dispatch(addNewCase({\n                      first_name: firstName.trim(),\n                      last_name: lastName.trim(),\n                      full_name: firstName.trim() + \" \" + lastName.trim(),\n                      birth_day: birthDate,\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: coordinator.value,\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_type_item: caseType === \"Medical\" ? caseTypeItem : \"\",\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      case_status: coordinatStatusList,\n                      appointment_date: caseTypeItem === \"Inpatient\" ? \"\" : appointmentDate,\n                      start_date: caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                      end_date: caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                      service_location: serviceLocation,\n                      provider: providerName.value,\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: insuranceCompany.value,\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      //\n                      providers: providerItems !== null && providerItems !== void 0 ? providerItems : [],\n                      //\n                      is_pay: isPay ? \"True\" : \"False\",\n                      price_tatal: priceTotal,\n                      currency_price: (_currencyCode$value = currencyCode.value) !== null && _currencyCode$value !== void 0 ? _currencyCode$value : \"\"\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseAdd ? \"Loading..\" : \"Submit\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2678,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2671,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2477,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2761,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2753,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Created Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2767,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully created and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2770,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2783,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2774,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2752,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2751,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2750,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 528,\n    columnNumber: 5\n  }, this);\n}\n_s(AddCaseScreen, \"FrijPcichNkEaviHeRnWd6817eg=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = AddCaseScreen;\nexport default AddCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"AddCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersListEditCase", "addNewCase", "Select", "useDropzone", "insurancesListDashboard", "coordinatorsList<PERSON>ash<PERSON>", "COUNTRIES", "CURRENCYITEMS", "GoogleComponent", "jsxDEV", "_jsxDEV", "COUNTRY_CURRENCY_MAP", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "_s", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "isLoading", "setIsLoading", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "timeoutId", "setTimeout", "console", "log", "clearTimeout", "length", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "value", "onChange", "v", "option", "countryName", "mappedCurrencyCode", "currencyOption", "find", "currency", "code", "label", "name", "success", "info", "options", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "address_components", "countryComponent", "component", "types", "includes", "long_name", "foundCountry", "defaultValue", "language", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "item", "full_name", "min", "checked", "for", "rows", "check", "error", "filter", "status", "disabled", "_option$value", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "_foundProvider$servic", "services", "_option$label", "_option$city", "_option$country", "searchTerm", "service", "_service$service_type", "service_type", "service_specialist", "exists", "_providerName$value", "String", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "provider", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "style", "size", "toFixed", "indexToRemove", "_currencyCode$value", "providerItems", "_item$service", "_item$provider", "first_name", "trim", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_type_item", "case_description", "status_coordination", "case_status", "appointment_date", "start_date", "end_date", "service_location", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "is_pay", "price_tatal", "currency_price", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListEditCase } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\n// Country to Currency mapping - using exact country names from COUNTRIES constant\nconst COUNTRY_CURRENCY_MAP = {\n  \"Morocco\": \"MAD\",\n  \"United States\": \"USD\",\n  \"Canada\": \"CAD\",\n  \"United Kingdom\": \"GBP\",\n  \"France\": \"EUR\",\n  \"Germany\": \"EUR\",\n  \"Spain\": \"EUR\",\n  \"Italy\": \"EUR\",\n  \"Netherlands\": \"EUR\",\n  \"Belgium\": \"EUR\",\n  \"Portugal\": \"EUR\",\n  \"Greece\": \"EUR\",\n  \"Austria\": \"EUR\",\n  \"Ireland\": \"EUR\",\n  \"Finland\": \"EUR\",\n  \"Luxembourg\": \"EUR\",\n  \"Estonia\": \"EUR\",\n  \"Slovenia\": \"EUR\",\n  \"Slovakia\": \"EUR\",\n  \"Malta\": \"EUR\",\n  \"Cyprus\": \"EUR\",\n  \"Lithuania\": \"EUR\",\n  \"Latvia\": \"EUR\",\n  \"Japan\": \"JPY\",\n  \"China\": \"CNY\",\n  \"India\": \"INR\",\n  \"Australia\": \"AUD\",\n  \"New Zealand\": \"NZD\",\n  \"South Africa\": \"ZAR\",\n  \"Brazil\": \"BRL\",\n  \"Mexico\": \"MXN\",\n  \"Argentina\": \"ARS\",\n  \"Chile\": \"CLP\",\n  \"Colombia\": \"COP\",\n  \"Peru\": \"PEN\",\n  \"Russia\": \"RUB\",\n  \"Turkey\": \"TRY\",\n  \"Egypt\": \"EGP\",\n  \"Saudi Arabia\": \"SAR\",\n  \"United Arab Emirates\": \"AED\",\n  \"Qatar\": \"QAR\",\n  \"Kuwait\": \"KWD\",\n  \"Bahrain\": \"BHD\",\n  \"Oman\": \"OMR\",\n  \"Jordan\": \"JOD\",\n  \"Lebanon\": \"LBP\",\n  \"Israel\": \"ILS\",\n  \"South Korea\": \"KRW\",\n  \"Thailand\": \"THB\",\n  \"Malaysia\": \"MYR\",\n  \"Singapore\": \"SGD\",\n  \"Indonesia\": \"IDR\",\n  \"Philippines\": \"PHP\",\n  \"Vietnam\": \"VND\",\n  \"Pakistan\": \"PKR\",\n  \"Bangladesh\": \"BDT\",\n  \"Sri Lanka\": \"LKR\",\n  \"Nepal\": \"NPR\",\n  \"Switzerland\": \"CHF\",\n  \"Norway\": \"NOK\",\n  \"Sweden\": \"SEK\",\n  \"Denmark\": \"DKK\",\n  \"Iceland\": \"ISK\",\n  \"Poland\": \"PLN\",\n  \"Czech Republic\": \"CZK\",\n  \"Hungary\": \"HUF\",\n  \"Romania\": \"RON\",\n  \"Bulgaria\": \"BGN\",\n  \"Croatia\": \"HRK\",\n  \"Serbia\": \"RSD\",\n  \"Ukraine\": \"UAH\",\n  \"Belarus\": \"BYN\",\n  \"Algeria\": \"DZD\",\n  \"Tunisia\": \"TND\",\n  \"Libya\": \"LYD\",\n  \"Sudan\": \"SDG\",\n  \"Ethiopia\": \"ETB\",\n  \"Kenya\": \"KES\",\n  \"Uganda\": \"UGX\",\n  \"Tanzania\": \"TZS\",\n  \"Rwanda\": \"RWF\",\n  \"Ghana\": \"GHS\",\n  \"Nigeria\": \"NGN\",\n  \"Senegal\": \"XOF\",\n  \"Ivory Coast\": \"XOF\",\n  \"Mali\": \"XOF\",\n  \"Burkina Faso\": \"XOF\",\n  \"Niger\": \"XOF\",\n  \"Guinea\": \"GNF\",\n  \"Sierra Leone\": \"SLL\",\n  \"Liberia\": \"LRD\",\n  \"Cameroon\": \"XAF\",\n  \"Chad\": \"XAF\",\n  \"Central African Republic\": \"XAF\",\n  \"Democratic Republic of the Congo\": \"CDF\",\n  \"Republic of the Congo\": \"XAF\",\n  \"Gabon\": \"XAF\",\n  \"Angola\": \"AOA\",\n  \"Zambia\": \"ZMK\",\n  \"Zimbabwe\": \"ZWL\",\n  \"Botswana\": \"BWP\",\n  \"Namibia\": \"NAD\",\n  \"Lesotho\": \"LSL\",\n  \"Swaziland\": \"SZL\",\n  \"Mozambique\": \"MZN\",\n  \"Madagascar\": \"MGA\",\n  \"Mauritius\": \"MUR\",\n  \"Seychelles\": \"SCR\",\n  \"Afghanistan\": \"AFN\",\n  \"Albania\": \"ALL\",\n  \"Armenia\": \"AMD\",\n  \"Azerbaijan\": \"AZN\",\n  \"Brunei\": \"BND\",\n  \"Cambodia\": \"KHR\",\n  \"Cape Verde\": \"CVE\",\n  \"Comoros\": \"KMF\",\n  \"Costa Rica\": \"CRC\",\n  \"Cuba\": \"CUP\",\n  \"Dominican Republic\": \"DOP\",\n  \"Ecuador\": \"USD\",\n  \"El Salvador\": \"USD\",\n  \"Eritrea\": \"ERN\",\n  \"Fiji\": \"FJD\",\n  \"Georgia\": \"GEL\",\n  \"Guatemala\": \"GTQ\",\n  \"Guinea-Bissau\": \"XOF\",\n  \"Guyana\": \"GYD\",\n  \"Haiti\": \"HTG\",\n  \"Honduras\": \"HNL\",\n  \"Hong Kong\": \"HKD\",\n  \"Iran\": \"IRR\",\n  \"Iraq\": \"IQD\",\n  \"Jamaica\": \"JMD\",\n  \"Kazakhstan\": \"KZT\",\n  \"Kyrgyzstan\": \"KGS\",\n  \"Laos\": \"LAK\",\n  \"Macau\": \"MOP\",\n  \"Macedonia\": \"MKD\",\n  \"Malawi\": \"MWK\",\n  \"Maldives\": \"MVR\",\n  \"Marshall Islands\": \"USD\",\n  \"Mauritania\": \"MRU\",\n  \"Micronesia\": \"USD\",\n  \"Moldova\": \"MDL\",\n  \"Monaco\": \"EUR\",\n  \"Mongolia\": \"MNT\",\n  \"Montenegro\": \"EUR\",\n  \"Myanmar\": \"MMK\",\n  \"Nicaragua\": \"NIO\",\n  \"North Korea\": \"KPW\",\n  \"Panama\": \"PAB\",\n  \"Papua New Guinea\": \"PGK\",\n  \"Paraguay\": \"PYG\",\n  \"Puerto Rico\": \"USD\",\n  \"Samoa\": \"WST\",\n  \"San Marino\": \"EUR\",\n  \"Sao Tome and Principe\": \"STN\",\n  \"Somalia\": \"SOS\",\n  \"South Sudan\": \"SSP\",\n  \"Suriname\": \"SRD\",\n  \"Syria\": \"SYP\",\n  \"Taiwan\": \"TWD\",\n  \"Tajikistan\": \"TJS\",\n  \"Togo\": \"XOF\",\n  \"Tonga\": \"TOP\",\n  \"Trinidad and Tobago\": \"TTD\",\n  \"Turkmenistan\": \"TMT\",\n  \"Tuvalu\": \"AUD\",\n  \"Uruguay\": \"UYU\",\n  \"Uzbekistan\": \"UZS\",\n  \"Vanuatu\": \"VUV\",\n  \"Venezuela\": \"VES\",\n  \"Western Sahara\": \"MAD\",\n  \"Yemen\": \"YER\"\n};\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & Assistance Details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const [isLoading, setIsLoading] = useState(true);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      setStepSelect(0);\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListEditCase(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      //   dispatch(clientList(\"0\"));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 4000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n      setIsLoading(false);\n    }\n  }, [successCaseAdd]);\n\n  // Update loading state when case add is in progress\n  useEffect(() => {\n    if (loadingCaseAdd) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseAdd]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingInsurances &&\n      !loadingCoordinators &&\n      providers &&\n      providers.length > 0 &&\n      coordinators &&\n      coordinators.length > 0\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    }\n  }, [\n    loadingProviders,\n    loadingInsurances,\n    loadingCoordinators,\n    providers,\n    coordinators,\n  ]);\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n\n                              // Auto-update currency based on selected country\n                              if (option && option.value) {\n                                // The option.value contains the country title (name)\n                                const countryName = option.value;\n                                const mappedCurrencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                if (mappedCurrencyCode) {\n                                  // Find the currency option in CURRENCYITEMS\n                                  const currencyOption = CURRENCYITEMS?.find(\n                                    (currency) => currency.code === mappedCurrencyCode\n                                  );\n\n                                  if (currencyOption) {\n                                    // Use setTimeout to ensure the state update happens after the country is set\n                                    setTimeout(() => {\n                                      setCurrencyCode({\n                                        value: currencyOption.code,\n                                        label: currencyOption.name !== \"\"\n                                          ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                          : currencyOption.code\n                                      });\n\n                                      // Show success message\n                                      toast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);\n                                    }, 100);\n                                  } else {\n                                    toast.info(`Country updated to ${countryName}. Currency code ${mappedCurrencyCode} not found in currency list.`);\n                                  }\n                                } else {\n                                  toast.info(`Country updated to ${countryName}. Please select currency manually.`);\n                                }\n                              }\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n\n                                // Extract country from Google Places result\n                                if (place.address_components) {\n                                  const countryComponent = place.address_components.find(\n                                    component => component.types.includes('country')\n                                  );\n\n                                  if (countryComponent) {\n                                    const countryName = countryComponent.long_name;\n\n                                    // Find matching country in COUNTRIES constant\n                                    const foundCountry = COUNTRIES.find(\n                                      country => country.title === countryName\n                                    );\n\n                                    if (foundCountry) {\n                                      // Set the country\n                                      setCountry({\n                                        value: foundCountry.title,\n                                        label: (\n                                          <div className=\"flex flex-row items-center\">\n                                            <span className=\"mr-2\">{foundCountry.icon}</span>\n                                            <span>{foundCountry.title}</span>\n                                          </div>\n                                        ),\n                                      });\n\n                                      // Auto-update currency based on selected country\n                                      const currencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                      if (currencyCode) {\n                                        // Find the currency option in CURRENCYITEMS\n                                        const currencyOption = CURRENCYITEMS?.find(\n                                          (currency) => currency.code === currencyCode\n                                        );\n\n                                        if (currencyOption) {\n                                          setCurrencyCode({\n                                            value: currencyOption.code,\n                                            label: currencyOption.name !== \"\"\n                                              ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                              : currencyOption.code\n                                          });\n\n                                          // Show success message\n                                          toast.success(`Country and currency automatically updated: ${countryName} - ${currencyOption.name} (${currencyOption.code})`);\n                                        }\n                                      } else {\n                                        // Show message if country is set but currency mapping not found\n                                        toast.info(`Country updated to ${countryName}. Please select currency manually.`);\n                                      }\n                                    } else {\n                                      // Show message if country not found in our list\n                                      toast.info(`City selected. Country \"${countryName}\" not found in our list. Please select country and currency manually.`);\n                                    }\n                                  }\n                                }\n\n                                // Optional: Extract coordinates if needed in the future\n                                // const latitude = place.geometry.location.lat();\n                                // const longitude = place.geometry.location.lng();\n                                // setLocationX(latitude ?? \"\");\n                                // setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (city === \"\") {\n                          setCityError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Assistance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                   {/* Appointment Details: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Appointment Details:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col w-full \">\n                      {caseType === \"Medical\" &&\n                      caseTypeItem === \"Inpatient\" ? (\n                        <div className=\"flex md:flex-row flex-col w-full\">\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Starting Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Starting Date\"\n                                value={startDate}\n                                onChange={(v) => {\n                                  setStartDate(v.target.value);\n                                  // If end date is earlier than new start date, update end date\n                                  if (endDate && endDate < v.target.value) {\n                                    setEndDate(v.target.value);\n                                  }\n                                }}\n                              />\n                            </div>\n                          </div>\n                          <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Hospital Ending Date\n                            </div>\n                            <div>\n                              <input\n                                className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                                type=\"date\"\n                                placeholder=\"Hospital Ending Date\"\n                                value={endDate}\n                                onChange={(v) => setEndDate(v.target.value)}\n                                disabled={!startDate}\n                                min={startDate}\n                              />\n                            </div>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Appointment Date\n                          </div>\n                          <div>\n                            <input\n                              className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                              type=\"date\"\n                              placeholder=\"Appointment Date\"\n                              value={appointmentDate}\n                              onChange={(v) =>\n                                setAppointmentDate(v.target.value)\n                              }\n                            />\n                          </div>\n                        </div>\n                      )}\n                    </div>\n\n                    <div className=\"flex md:flex-row flex-col  \">\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Provider Information: */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Provider Information:\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              // Show loading indicator while fetching provider services\n                              setIsLoading(true);\n\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                                // Hide loading indicator after services are loaded\n                                setTimeout(() => {\n                                  setIsLoading(false);\n                                }, 100);\n                              } else {\n                                setProviderServices([]);\n                                setIsLoading(false);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                              city: item.city || \"\",\n                              country: item.country || \"\",\n                            }))}\n                            filterOption={(option, inputValue) => {\n                              // تحسين البحث ليشمل الاسم والمدينة والبلد\n                              const searchTerm = inputValue?.toLowerCase();\n                              return (\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.city\n                                  ?.toLowerCase()\n                                  .includes(searchTerm) ||\n                                option.country\n                                  ?.toLowerCase()\n                                  .includes(searchTerm)\n                              );\n                            }}\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            // Add loading indicator\n                            isLoading={loadingProviders}\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Visit Date\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              providerDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Visit Date\"\n                            value={providerDate}\n                            onChange={(v) => setProviderDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerDateError ? providerDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          setProviderDateError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\"Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\"Provider Service is required\");\n                            check = false;\n                          }\n                          if (providerDate === \"\") {\n                            setProviderDateError(\"These fields are required.\");\n                            toast.error(\"Visit Date is required\");\n                            check = false;\n                          }\n                          if (check) {\n                            const exists = false;\n                            // const exists = providerMultiSelect.some(\n                            //   (provider) =>\n                            //     String(provider?.provider?.id) ===\n                            //       String(providerName.value) &&\n                            //     String(provider?.service?.id) ===\n                            //       String(providerService)\n                            // );\n\n                            if (!exists) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                      date: providerDate,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  setProviderDate(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                                <div>\n                                  <b>Date:</b> {itemProvider.date ?? \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusListError(\"\");\n                        setProviderNameError(\"\");\n\n                        // if (coordinatStatusList.length === 0) {\n                        //   setCoordinatStatusListError(\n                        //     \"This fields is required.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        // if (providerMultiSelect.length === 0) {\n                        //   setProviderNameError(\n                        //     \"Please select this and click Add Provider.\"\n                        //   );\n                        //   check = false;\n                        // }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect?.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName.trim(),\n                            last_name: lastName.trim(),\n                            full_name: firstName.trim() + \" \" + lastName.trim(),\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            //\n                            providers: providerItems ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,qBAAqB,QAAQ,qCAAqC;AAC3E,SAASC,UAAU,QAAQ,iCAAiC;AAE5D,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,eAAe,MAAM,2BAA2B;;AAEvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,oBAAoB,GAAG;EAC3B,SAAS,EAAE,KAAK;EAChB,eAAe,EAAE,KAAK;EACtB,QAAQ,EAAE,KAAK;EACf,gBAAgB,EAAE,KAAK;EACvB,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,OAAO,EAAE,KAAK;EACd,OAAO,EAAE,KAAK;EACd,aAAa,EAAE,KAAK;EACpB,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,KAAK;EACjB,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,KAAK;EACnB,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,KAAK;EACjB,UAAU,EAAE,KAAK;EACjB,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,WAAW,EAAE,KAAK;EAClB,QAAQ,EAAE,KAAK;EACf,OAAO,EAAE,KAAK;EACd,OAAO,EAAE,KAAK;EACd,OAAO,EAAE,KAAK;EACd,WAAW,EAAE,KAAK;EAClB,aAAa,EAAE,KAAK;EACpB,cAAc,EAAE,KAAK;EACrB,QAAQ,EAAE,KAAK;EACf,QAAQ,EAAE,KAAK;EACf,WAAW,EAAE,KAAK;EAClB,OAAO,EAAE,KAAK;EACd,UAAU,EAAE,KAAK;EACjB,MAAM,EAAE,KAAK;EACb,QAAQ,EAAE,KAAK;EACf,QAAQ,EAAE,KAAK;EACf,OAAO,EAAE,KAAK;EACd,cAAc,EAAE,KAAK;EACrB,sBAAsB,EAAE,KAAK;EAC7B,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,MAAM,EAAE,KAAK;EACb,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,QAAQ,EAAE,KAAK;EACf,aAAa,EAAE,KAAK;EACpB,UAAU,EAAE,KAAK;EACjB,UAAU,EAAE,KAAK;EACjB,WAAW,EAAE,KAAK;EAClB,WAAW,EAAE,KAAK;EAClB,aAAa,EAAE,KAAK;EACpB,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,KAAK;EACjB,YAAY,EAAE,KAAK;EACnB,WAAW,EAAE,KAAK;EAClB,OAAO,EAAE,KAAK;EACd,aAAa,EAAE,KAAK;EACpB,QAAQ,EAAE,KAAK;EACf,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,QAAQ,EAAE,KAAK;EACf,gBAAgB,EAAE,KAAK;EACvB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,KAAK;EACjB,SAAS,EAAE,KAAK;EAChB,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,OAAO,EAAE,KAAK;EACd,OAAO,EAAE,KAAK;EACd,UAAU,EAAE,KAAK;EACjB,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,QAAQ,EAAE,KAAK;EACf,OAAO,EAAE,KAAK;EACd,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,aAAa,EAAE,KAAK;EACpB,MAAM,EAAE,KAAK;EACb,cAAc,EAAE,KAAK;EACrB,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,cAAc,EAAE,KAAK;EACrB,SAAS,EAAE,KAAK;EAChB,UAAU,EAAE,KAAK;EACjB,MAAM,EAAE,KAAK;EACb,0BAA0B,EAAE,KAAK;EACjC,kCAAkC,EAAE,KAAK;EACzC,uBAAuB,EAAE,KAAK;EAC9B,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,UAAU,EAAE,KAAK;EACjB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,WAAW,EAAE,KAAK;EAClB,YAAY,EAAE,KAAK;EACnB,YAAY,EAAE,KAAK;EACnB,WAAW,EAAE,KAAK;EAClB,YAAY,EAAE,KAAK;EACnB,aAAa,EAAE,KAAK;EACpB,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,KAAK;EACnB,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,YAAY,EAAE,KAAK;EACnB,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,KAAK;EACnB,MAAM,EAAE,KAAK;EACb,oBAAoB,EAAE,KAAK;EAC3B,SAAS,EAAE,KAAK;EAChB,aAAa,EAAE,KAAK;EACpB,SAAS,EAAE,KAAK;EAChB,MAAM,EAAE,KAAK;EACb,SAAS,EAAE,KAAK;EAChB,WAAW,EAAE,KAAK;EAClB,eAAe,EAAE,KAAK;EACtB,QAAQ,EAAE,KAAK;EACf,OAAO,EAAE,KAAK;EACd,UAAU,EAAE,KAAK;EACjB,WAAW,EAAE,KAAK;EAClB,MAAM,EAAE,KAAK;EACb,MAAM,EAAE,KAAK;EACb,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,KAAK;EACnB,YAAY,EAAE,KAAK;EACnB,MAAM,EAAE,KAAK;EACb,OAAO,EAAE,KAAK;EACd,WAAW,EAAE,KAAK;EAClB,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,kBAAkB,EAAE,KAAK;EACzB,YAAY,EAAE,KAAK;EACnB,YAAY,EAAE,KAAK;EACnB,SAAS,EAAE,KAAK;EAChB,QAAQ,EAAE,KAAK;EACf,UAAU,EAAE,KAAK;EACjB,YAAY,EAAE,KAAK;EACnB,SAAS,EAAE,KAAK;EAChB,WAAW,EAAE,KAAK;EAClB,aAAa,EAAE,KAAK;EACpB,QAAQ,EAAE,KAAK;EACf,kBAAkB,EAAE,KAAK;EACzB,UAAU,EAAE,KAAK;EACjB,aAAa,EAAE,KAAK;EACpB,OAAO,EAAE,KAAK;EACd,YAAY,EAAE,KAAK;EACnB,uBAAuB,EAAE,KAAK;EAC9B,SAAS,EAAE,KAAK;EAChB,aAAa,EAAE,KAAK;EACpB,UAAU,EAAE,KAAK;EACjB,OAAO,EAAE,KAAK;EACd,QAAQ,EAAE,KAAK;EACf,YAAY,EAAE,KAAK;EACnB,MAAM,EAAE,KAAK;EACb,OAAO,EAAE,KAAK;EACd,qBAAqB,EAAE,KAAK;EAC5B,cAAc,EAAE,KAAK;EACrB,QAAQ,EAAE,KAAK;EACf,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,KAAK;EACnB,SAAS,EAAE,KAAK;EAChB,WAAW,EAAE,KAAK;EAClB,gBAAgB,EAAE,KAAK;EACvB,OAAO,EAAE;AACX,CAAC;AAED,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC8D,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACoF,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACsF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC4F,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CACtC,IAAI8F,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACmG,QAAQ,EAAEC,WAAW,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqG,aAAa,EAAEC,gBAAgB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACuG,YAAY,EAAEC,eAAe,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC2G,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+G,eAAe,EAAEC,kBAAkB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACmH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqH,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACuH,eAAe,EAAEC,kBAAkB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC2H,SAAS,EAAEC,YAAY,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6H,cAAc,EAAEC,iBAAiB,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC+H,OAAO,EAAEC,UAAU,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACmI,eAAe,EAAEC,kBAAkB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACuI,YAAY,EAAEC,eAAe,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC2I,aAAa,EAAEC,gBAAgB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6I,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC+I,aAAa,EAAEC,gBAAgB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAACuJ,aAAa,EAAEC,gBAAgB,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC2J,UAAU,EAAEC,aAAa,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6J,eAAe,EAAEC,kBAAkB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+J,MAAM,EAAEC,SAAS,CAAC,GAAGhK,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACiK,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACmK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqK,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACuK,eAAe,EAAEC,kBAAkB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC2K,YAAY,EAAEC,eAAe,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6K,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC+K,aAAa,EAAEC,gBAAgB,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA;EACA,MAAM,CAACmL,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGpL,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJqL,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAG7K,WAAW,CAAC;IACd8K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACLoL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvM,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJqL,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAG9L,WAAW,CAAC;IACd8K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACLuM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG3M,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJqL,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAGlM,WAAW,CAAC;IACd8K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACL2M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG/M,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACgN,SAAS,EAAEC,YAAY,CAAC,GAAGjN,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMkN,SAAS,GAAGhN,WAAW,CAAEiN,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAGnN,WAAW,CAAEiN,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,UAAU,GAAGxN,WAAW,CAAEiN,KAAK,IAAKA,KAAK,CAACQ,aAAa,CAAC;EAC9D,MAAM;IAAEC,cAAc;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAGJ,UAAU;EAEnE,MAAMK,cAAc,GAAG7N,WAAW,CAAEiN,KAAK,IAAKA,KAAK,CAACa,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,gBAAgB,GAAGlO,WAAW,CAAEiN,KAAK,IAAKA,KAAK,CAACkB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,QAAQ,GAAG,GAAG;EACpB1O,SAAS,CAAC,MAAM;IACd,IAAI,CAACqN,QAAQ,EAAE;MACbrL,QAAQ,CAAC0M,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL;MACAxB,YAAY,CAAC,IAAI,CAAC;MAElBF,aAAa,CAAC,CAAC,CAAC;MAChB9K,QAAQ,CAACpB,yBAAyB,CAAC,GAAG,CAAC,CAAC;MACxCoB,QAAQ,CAACzB,qBAAqB,CAAC,GAAG,CAAC,CAAC;MACpCyB,QAAQ,CAACrB,uBAAuB,CAAC,GAAG,CAAC,CAAC;MACtC;;MAEA;MACA,MAAM8N,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjC1B,YAAY,CAAC,KAAK,CAAC;QACnB2B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACvE,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,OAAO,MAAMC,YAAY,CAACJ,SAAS,CAAC;IACtC;EACF,CAAC,EAAE,CAAC3M,QAAQ,EAAEqL,QAAQ,EAAEnL,QAAQ,CAAC,CAAC;EAElClC,SAAS,CAAC,MAAM;IACd,IAAI8N,cAAc,EAAE;MAClBd,aAAa,CAAC,CAAC,CAAC;MAChBE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACY,cAAc,CAAC,CAAC;;EAEpB;EACA9N,SAAS,CAAC,MAAM;IACd,IAAI6N,cAAc,EAAE;MAClBX,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACW,cAAc,CAAC,CAAC;;EAEpB;EACA7N,SAAS,CAAC,MAAM;IACd;IACA,IACE,CAACyN,gBAAgB,IACjB,CAACU,iBAAiB,IAClB,CAACK,mBAAmB,IACpBhB,SAAS,IACTA,SAAS,CAACwB,MAAM,GAAG,CAAC,IACpBT,YAAY,IACZA,YAAY,CAACS,MAAM,GAAG,CAAC,EACvB;MACA;MACA9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CACDO,gBAAgB,EAChBU,iBAAiB,EACjBK,mBAAmB,EACnBhB,SAAS,EACTe,YAAY,CACb,CAAC;EAEF,oBACEpN,OAAA,CAACb,aAAa;IAAA2O,QAAA,GAEXhC,SAAS,iBACR9L,OAAA;MAAK+N,SAAS,EAAC,+FAA+F;MAAAD,QAAA,eAC5G9N,OAAA;QAAK+N,SAAS,EAAC,8DAA8D;QAAAD,QAAA,gBAC3E9N,OAAA;UAAK+N,SAAS,EAAC;QAAiF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvGnO,OAAA;UAAK+N,SAAS,EAAC,2BAA2B;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnO,OAAA;MAAK+N,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf9N,OAAA;QAAK+N,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD9N,OAAA;UAAGoO,IAAI,EAAC,YAAY;UAAAN,QAAA,eAClB9N,OAAA;YAAK+N,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D9N,OAAA;cACEqO,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBT,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB9N,OAAA;gBACEyO,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNnO,OAAA;cAAM+N,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJnO,OAAA;UAAA8N,QAAA,eACE9N,OAAA;YACEqO,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBT,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB9N,OAAA;cACEyO,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPnO,OAAA;UAAK+N,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAe;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENnO,OAAA;QAAK+N,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C9N,OAAA;UAAI+N,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENnO,OAAA;QAAK+N,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ9N,OAAA;UAAK+N,SAAS,EAAC,2BAA2B;UAAAD,QAAA,gBACxC9N,OAAA;YAAK+N,SAAS,EAAC,2DAA2D;YAAAD,QAAA,gBACxE9N,OAAA;cAAK+N,SAAS,EAAC;YAAwF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7GjO,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyK,GAAG,CAAC,CAACiE,IAAI,EAAEzO,KAAK,kBAC1BH,OAAA;cACE6O,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIjD,UAAU,GAAGgD,IAAI,CAACzO,KAAK,IAAIyL,UAAU,KAAK,CAAC,EAAE;kBAC/CC,aAAa,CAAC+C,IAAI,CAACzO,KAAK,CAAC;gBAC3B;cACF,CAAE;cACF4N,SAAS,EAAG,kCACVnC,UAAU,GAAGgD,IAAI,CAACzO,KAAK,IAAIyL,UAAU,KAAK,CAAC,GACvC,gBAAgB,GAChB,EACL,8BAA8B;cAAAkC,QAAA,GAE9BlC,UAAU,GAAGgD,IAAI,CAACzO,KAAK,gBACtBH,OAAA;gBAAK+N,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjH9N,OAAA;kBACE8O,GAAG,EAAE1P,eAAgB;kBACrB2O,SAAS,EAAC,QAAQ;kBAClBgB,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJvC,UAAU,KAAKgD,IAAI,CAACzO,KAAK,gBAC3BH,OAAA;gBAAK+N,SAAS,EAAC;cAAkD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExEnO,OAAA;gBAAK+N,SAAS,EAAC,oGAAoG;gBAAAD,QAAA,eACjH9N,OAAA;kBACEqO,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBT,SAAS,EAAC,QAAQ;kBAAAD,QAAA,eAElB9N,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB2O,CAAC,EAAC;kBAAuB;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDnO,OAAA;gBAAK+N,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrC9N,OAAA;kBAAK+N,SAAS,EAAC,qBAAqB;kBAAAD,QAAA,EAAEc,IAAI,CAACxO;gBAAK;kBAAA4N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtDvC,UAAU,KAAKgD,IAAI,CAACzO,KAAK,gBACxBH,OAAA;kBAAK+N,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAChDc,IAAI,CAACvO;gBAAW;kBAAA2N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNnO,OAAA;YAAK+N,SAAS,EAAC,0CAA0C;YAAAD,QAAA,GAEtDlC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf9N,OAAA;gBAAK+N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,aACjC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,wBACV7M,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpCiO,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBC,KAAK,EAAErO,SAAU;wBACjBsO,QAAQ,EAAGC,CAAC,IAAKtO,YAAY,CAACsO,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5M,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAA8M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvBC,KAAK,EAAEjO,QAAS;wBAChBkO,QAAQ,EAAGC,CAAC,IAAKlO,WAAW,CAACkO,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC9N,OAAA;oBAAK+N,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,gBAC3C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,wBACVrM,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCyN,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAE7N,KAAM;wBACb8N,QAAQ,EAAGC,CAAC,IAAK9N,QAAQ,CAAC8N,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpM,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAsM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,QACrC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,uBACV7L,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCiN,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtBC,KAAK,EAAErN,KAAM;wBACbsN,QAAQ,EAAGC,CAAC,IAAKtN,QAAQ,CAACsN,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC5L,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAA8L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnO,OAAA;kBAAK+N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC9N,OAAA;oBAAK+N,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,UACpC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAEzM,OAAQ;wBACf0M,QAAQ,EAAGE,MAAM,IAAK;0BACpB3M,UAAU,CAAC2M,MAAM,CAAC;;0BAElB;0BACA,IAAIA,MAAM,IAAIA,MAAM,CAACH,KAAK,EAAE;4BAC1B;4BACA,MAAMI,WAAW,GAAGD,MAAM,CAACH,KAAK;4BAChC,MAAMK,kBAAkB,GAAGzP,oBAAoB,CAACwP,WAAW,CAAC;4BAE5D,IAAIC,kBAAkB,EAAE;8BACtB;8BACA,MAAMC,cAAc,GAAG9P,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+P,IAAI,CACvCC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,KAAKJ,kBAClC,CAAC;8BAED,IAAIC,cAAc,EAAE;gCAClB;gCACAlC,UAAU,CAAC,MAAM;kCACftK,eAAe,CAAC;oCACdkM,KAAK,EAAEM,cAAc,CAACG,IAAI;oCAC1BC,KAAK,EAAEJ,cAAc,CAACK,IAAI,KAAK,EAAE,GAC7BL,cAAc,CAACK,IAAI,GAAG,IAAI,GAAGL,cAAc,CAACG,IAAI,GAAG,GAAG,GACtDH,cAAc,CAACG;kCACrB,CAAC,CAAC;;kCAEF;kCACAzQ,KAAK,CAAC4Q,OAAO,CAAE,qCAAoCN,cAAc,CAACK,IAAK,KAAIL,cAAc,CAACG,IAAK,GAAE,CAAC;gCACpG,CAAC,EAAE,GAAG,CAAC;8BACT,CAAC,MAAM;gCACLzQ,KAAK,CAAC6Q,IAAI,CAAE,sBAAqBT,WAAY,mBAAkBC,kBAAmB,8BAA6B,CAAC;8BAClH;4BACF,CAAC,MAAM;8BACLrQ,KAAK,CAAC6Q,IAAI,CAAE,sBAAqBT,WAAY,oCAAmC,CAAC;4BACnF;0BACF;wBACF,CAAE;wBACFU,OAAO,EAAEvQ,SAAS,CAAC+K,GAAG,CAAE/H,OAAO,KAAM;0BACnCyM,KAAK,EAAEzM,OAAO,CAACxC,KAAK;0BACpB2P,KAAK,eACH/P,OAAA;4BACE+N,SAAS,EAAG,GACVnL,OAAO,CAACxC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAA0N,QAAA,gBAE9B9N,OAAA;8BAAM+N,SAAS,EAAC,MAAM;8BAAAD,QAAA,EAAElL,OAAO,CAACwN;4BAAI;8BAAApC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5CnO,OAAA;8BAAA8N,QAAA,EAAOlL,OAAO,CAACxC;4BAAK;8BAAA4N,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJJ,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCiB,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE5N,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvB6N,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAEFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrChL,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,gBAClC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACF,eAAe;wBACdiR,MAAM,EAAC,yCAAyC;wBAChDhD,SAAS,EAAG,wBACVrL,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpC4M,QAAQ,EAAGC,CAAC,IAAK;0BACf9M,OAAO,CAAC8M,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACzB,CAAE;wBACF2B,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3B1O,OAAO,EAAA0O,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;;4BAEtC;4BACA,IAAIF,KAAK,CAACI,kBAAkB,EAAE;8BAC5B,MAAMC,gBAAgB,GAAGL,KAAK,CAACI,kBAAkB,CAACzB,IAAI,CACpD2B,SAAS,IAAIA,SAAS,CAACC,KAAK,CAACC,QAAQ,CAAC,SAAS,CACjD,CAAC;8BAED,IAAIH,gBAAgB,EAAE;gCACpB,MAAM7B,WAAW,GAAG6B,gBAAgB,CAACI,SAAS;;gCAE9C;gCACA,MAAMC,YAAY,GAAG/R,SAAS,CAACgQ,IAAI,CACjChN,OAAO,IAAIA,OAAO,CAACxC,KAAK,KAAKqP,WAC/B,CAAC;gCAED,IAAIkC,YAAY,EAAE;kCAChB;kCACA9O,UAAU,CAAC;oCACTwM,KAAK,EAAEsC,YAAY,CAACvR,KAAK;oCACzB2P,KAAK,eACH/P,OAAA;sCAAK+N,SAAS,EAAC,4BAA4B;sCAAAD,QAAA,gBACzC9N,OAAA;wCAAM+N,SAAS,EAAC,MAAM;wCAAAD,QAAA,EAAE6D,YAAY,CAACvB;sCAAI;wCAAApC,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC,eACjDnO,OAAA;wCAAA8N,QAAA,EAAO6D,YAAY,CAACvR;sCAAK;wCAAA4N,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC9B;kCAET,CAAC,CAAC;;kCAEF;kCACA,MAAMjL,YAAY,GAAGjD,oBAAoB,CAACwP,WAAW,CAAC;kCAEtD,IAAIvM,YAAY,EAAE;oCAChB;oCACA,MAAMyM,cAAc,GAAG9P,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+P,IAAI,CACvCC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,KAAK5M,YAClC,CAAC;oCAED,IAAIyM,cAAc,EAAE;sCAClBxM,eAAe,CAAC;wCACdkM,KAAK,EAAEM,cAAc,CAACG,IAAI;wCAC1BC,KAAK,EAAEJ,cAAc,CAACK,IAAI,KAAK,EAAE,GAC7BL,cAAc,CAACK,IAAI,GAAG,IAAI,GAAGL,cAAc,CAACG,IAAI,GAAG,GAAG,GACtDH,cAAc,CAACG;sCACrB,CAAC,CAAC;;sCAEF;sCACAzQ,KAAK,CAAC4Q,OAAO,CAAE,+CAA8CR,WAAY,MAAKE,cAAc,CAACK,IAAK,KAAIL,cAAc,CAACG,IAAK,GAAE,CAAC;oCAC/H;kCACF,CAAC,MAAM;oCACL;oCACAzQ,KAAK,CAAC6Q,IAAI,CAAE,sBAAqBT,WAAY,oCAAmC,CAAC;kCACnF;gCACF,CAAC,MAAM;kCACL;kCACApQ,KAAK,CAAC6Q,IAAI,CAAE,2BAA0BT,WAAY,uEAAsE,CAAC;gCAC3H;8BACF;4BACF;;4BAEA;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFmC,YAAY,EAAEpP,IAAK;wBACnBgP,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBK,QAAQ,EAAC;sBAAI;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCpL,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAsL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAEpG,gBAAiB;wBACxBqG,QAAQ,EAAGE,MAAM,IAAK;0BACpBtG,mBAAmB,CAACsG,MAAM,CAAC;wBAC7B,CAAE;wBACFW,OAAO,EAAEpD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpC,GAAG,CAAEmH,SAAS,KAAM;0BACvCzC,KAAK,EAAEyC,SAAS,CAACC,EAAE;0BACnBhC,KAAK,EAAE+B,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAACzC,MAAM,EAAE0C,UAAU,KAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbV,QAAQ,CAACS,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDpE,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCiB,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEvH,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBwH,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3E,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAA6E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,wBACVxE,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrC4F,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAEhG,eAAgB;wBACvBiG,QAAQ,EAAGC,CAAC,IAAKjG,kBAAkB,CAACiG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCvE,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAyE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEH,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxB9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAE3L,WAAY;wBACnB4L,QAAQ,EAAGE,MAAM,IAAK;0BACpB7L,cAAc,CAAC6L,MAAM,CAAC;wBACxB,CAAE;wBACFzB,SAAS,EAAC,SAAS;wBACnBoC,OAAO,EAAE/C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEzC,GAAG,CAAEyH,IAAI,KAAM;0BACpC/C,KAAK,EAAE+C,IAAI,CAACL,EAAE;0BACdhC,KAAK,EAAEqC,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJJ,YAAY,EAAEA,CAACzC,MAAM,EAAE0C,UAAU,KAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbV,QAAQ,CAACS,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD/C,WAAW,EAAC,uBAAuB;wBACnCiB,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9M,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvB+M,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrClK,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAoK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtB9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,wBACVhJ,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpCoK,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChCC,KAAK,EAAE3K,QAAS;wBAChB4K,QAAQ,EAAGC,CAAC,IAAK5K,WAAW,CAAC4K,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC/I,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,OACvC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACEqP,KAAK,EAAEpK,QAAS;wBAChBqK,QAAQ,EAAGC,CAAC,IAAKrK,WAAW,CAACqK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAC7CtB,SAAS,EAAG,wBACV5I,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAA2I,QAAA,gBAEpC9N,OAAA;0BAAQqP,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvCnO,OAAA;0BAAQqP,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1CnO,OAAA;0BAAQqP,KAAK,EAAE,WAAY;0BAAAvB,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACTnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3I,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELlJ,QAAQ,KAAK,SAAS,iBACrBjF,OAAA;kBAAK+N,SAAS,EAAC,gCAAgC;kBAAAD,QAAA,gBAC7C9N,OAAA;oBAAK+N,SAAS,EAAC,8BAA8B;oBAAAD,QAAA,GAAC,YAClC,eAAA9N,OAAA;sBAAQ+N,SAAS,EAAC,aAAa;sBAAAD,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACNnO,OAAA;oBAAA8N,QAAA,gBACE9N,OAAA;sBACEqP,KAAK,EAAEhK,YAAa;sBACpBiK,QAAQ,EAAGC,CAAC,IAAKjK,eAAe,CAACiK,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;sBACjDtB,SAAS,EAAG,wBACVxI,iBAAiB,GACb,eAAe,GACf,kBACL,mCAAmC;sBAAAuI,QAAA,gBAEpC9N,OAAA;wBAAQqP,KAAK,EAAE,EAAG;wBAAAvB,QAAA,EAAC;sBAAgB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5CnO,OAAA;wBAAQqP,KAAK,EAAE,YAAa;wBAAAvB,QAAA,EAAC;sBAAU;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAChDnO,OAAA;wBAAQqP,KAAK,EAAE,WAAY;wBAAAvB,QAAA,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC,eACTnO,OAAA;sBAAK+N,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,EACrCvI,iBAAiB,GAAGA,iBAAiB,GAAG;oBAAE;sBAAAyI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDnO,OAAA;kBAAK+N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,eAC/B,EAAC,GAAG,eACjB9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAEnM,YAAa;wBACpBoM,QAAQ,EAAGE,MAAM,IAAK;0BACpBrM,eAAe,CAACqM,MAAM,CAAC;wBACzB,CAAE;wBACFW,OAAO,EAAEtQ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE8K,GAAG,CAAEkF,QAAQ,KAAM;0BACzCR,KAAK,EAAEQ,QAAQ,CAACC,IAAI;0BACpBC,KAAK,EACHF,QAAQ,CAACG,IAAI,KAAK,EAAE,GAChBH,QAAQ,CAACG,IAAI,GACX,IAAI,GACJH,QAAQ,CAACC,IAAI,GACb,IAAI,IAAI,EAAE,GACZ;wBACR,CAAC,CAAC,CAAE;wBACJmC,YAAY,EAAEA,CAACzC,MAAM,EAAE0C,UAAU,KAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbV,QAAQ,CAACS,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDpE,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,0BAA0B;wBACtCiB,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEtN,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBuN,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC1K,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA4K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,kBAC5B,EAAC,GAAG,eACpB9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,wBACVvK,eAAe,GACX,eAAe,GACf,kBACL,oCAAoC;wBACrC2L,IAAI,EAAC,QAAQ;wBACbmD,GAAG,EAAE,CAAE;wBACP1D,IAAI,EAAE,IAAK;wBACXQ,WAAW,EAAC,MAAM;wBAClBC,KAAK,EAAE/L,UAAW;wBAClBgM,QAAQ,EAAGC,CAAC,IAAKhM,aAAa,CAACgM,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCtK,eAAe,GAAGA,eAAe,GAAG;sBAAE;wBAAAwK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnO,OAAA;kBAAK+N,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,gBACzC9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,eAC5C9N,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACEmP,IAAI,EAAE,UAAW;wBACjBa,IAAI,EAAC,OAAO;wBACZ+B,EAAE,EAAC,OAAO;wBACVQ,OAAO,EAAEvP,KAAK,KAAK,IAAK;wBACxBsM,QAAQ,EAAGC,CAAC,IAAK;0BACftM,QAAQ,CAAC,IAAI,CAAC;wBAChB;sBAAE;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBACE+N,SAAS,EAAC,6CAA6C;wBACvDyE,GAAG,EAAC,OAAO;wBAAA1E,QAAA,EACZ;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,eAC5C9N,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACEmP,IAAI,EAAE,UAAW;wBACjBa,IAAI,EAAC,QAAQ;wBACb+B,EAAE,EAAC,QAAQ;wBACXQ,OAAO,EAAEvP,KAAK,KAAK,KAAM;wBACzBsM,QAAQ,EAAGC,CAAC,IAAK;0BACftM,QAAQ,CAAC,KAAK,CAAC;wBACjB;sBAAE;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBACE+N,SAAS,EAAC,6CAA6C;wBACvDyE,GAAG,EAAC,QAAQ;wBAAA1E,QAAA,EACb;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACEqP,KAAK,EAAE5J,eAAgB;wBACvBgN,IAAI,EAAE,CAAE;wBACRnD,QAAQ,EAAGC,CAAC,IAAK7J,kBAAkB,CAAC6J,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBACpDtB,SAAS,EAAC;sBAAwE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnO,OAAA;gBAAK+N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,eAC1D9N,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI6D,KAAK,GAAG,IAAI;oBAChBvR,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnB6C,gBAAgB,CAAC,EAAE,CAAC;oBACpBI,oBAAoB,CAAC,EAAE,CAAC;oBACxBR,gBAAgB,CAAC,EAAE,CAAC;oBACpBnB,mBAAmB,CAAC,EAAE,CAAC;oBACvBlB,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBACnBM,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBAEtB,IAAIzC,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5CuR,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI1Q,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxCuQ,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI9P,OAAO,KAAK,EAAE,IAAIA,OAAO,CAACyM,KAAK,KAAK,EAAE,EAAE;sBAC1CtM,eAAe,CAAC,yBAAyB,CAAC;sBAC1C2P,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIlQ,IAAI,KAAK,EAAE,EAAE;sBACfG,YAAY,CAAC,yBAAyB,CAAC;sBACvC+P,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIxP,YAAY,KAAK,EAAE,IAAIA,YAAY,CAACmM,KAAK,KAAK,EAAE,EAAE;sBACpDhM,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/CqP,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIpP,UAAU,KAAK,EAAE,EAAE;sBACrBG,kBAAkB,CAAC,yBAAyB,CAAC;sBAC7CiP,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIhP,WAAW,KAAK,EAAE,IAAIA,WAAW,CAAC2L,KAAK,KAAK,EAAE,EAAE;sBAClDxL,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9C6O,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIhO,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C0N,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIzN,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CsN,KAAK,GAAG,KAAK;oBACf,CAAC,MAAM,IACLzN,QAAQ,KAAK,SAAS,IACtBI,YAAY,KAAK,EAAE,EACnB;sBACAG,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/CkN,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACT7G,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLxM,KAAK,CAACsT,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF5E,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf9N,OAAA;gBAAK+N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,GAAC,SACrC,eAAA9N,OAAA;wBAAQ+N,SAAS,EAAC,aAAa;wBAAAD,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBAAK+N,SAAS,EAAC,gBAAgB;wBAAAD,QAAA,gBAC7B9N,OAAA;0BAAK+N,SAAS,EAAC,qDAAqD;0BAAAD,QAAA,gBAClE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,sBACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,sBAAsB,CACvB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,sBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFd,EAAE,EAAC,sBAAsB;4BACzB5C,IAAI,EAAE,UAAW;4BACjBoD,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,sBACF,CAAE;4BACF1D,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,sBAAsB;4BAC1BzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNnO,OAAA;0BAAK+N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,yBACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,yBAAyB,CAC1B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,yBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,yBACF,CAAE;4BACFM,EAAE,EAAC,yBAAyB;4BAC5B5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,yBAAyB;4BAC7BzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNnO,OAAA;0BAAK+N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,6BACF,CAAE;4BACFM,EAAE,EAAC,6BAA6B;4BAChC5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,6BAA6B;4BACjCzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNnO,OAAA;0BAAK+N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,qCACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,qCAAqC,CACtC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,qCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,qCACF,CAAE;4BACFM,EAAE,EAAC,qCAAqC;4BACxC5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,qCAAqC;4BACzCzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNnO,OAAA;0BAAK+N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,kCACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kCAAkC,CACnC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,kCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,kCACF,CAAE;4BACFM,EAAE,EAAC,kCAAkC;4BACrC5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,kCAAkC;4BACtCzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENnO,OAAA;0BAAK+N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,kBACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kBAAkB,CACnB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,kBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,kBACF,CAAE;4BACFM,EAAE,EAAC,kBAAkB;4BACrB5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,kBAAkB;4BACtBzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAENnO,OAAA;0BAAK+N,SAAS,EAAC,sDAAsD;0BAAAD,QAAA,gBACnE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,6BACF,CAAE;4BACFM,EAAE,EAAC,6BAA6B;4BAChC5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,6BAA6B;4BACjCzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eAGNnO,OAAA;0BAAK+N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACtJ,mBAAmB,CAACwL,QAAQ,CAC3B,mBACF,CAAC,EACD;gCACAvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,mBAAmB,CACpB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IACLA,MAAM,KAAK,mBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CACnC,mBACF,CAAE;4BACFM,EAAE,EAAC,mBAAmB;4BACtB5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,mBAAmB;4BACvBzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNnO,OAAA;0BAAK+N,SAAS,EAAC,wDAAwD;0BAAAD,QAAA,gBACrE9N,OAAA;4BACEsP,QAAQ,EAAGC,CAAC,IAAK;8BACf,IAAI,CAACtJ,mBAAmB,CAACwL,QAAQ,CAAC,QAAQ,CAAC,EAAE;gCAC3CvL,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,QAAQ,CACT,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAAC2M,MAAM,CACvBC,MAAM,IAAKA,MAAM,KAAK,QACzB,CACF,CAAC;8BACH;4BACF,CAAE;4BACFN,OAAO,EAAEtM,mBAAmB,CAACwL,QAAQ,CAAC,QAAQ,CAAE;4BAChDM,EAAE,EAAC,QAAQ;4BACX5C,IAAI,EAAE,UAAW;4BACjBpB,SAAS,EAAC;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFnO,OAAA;4BACEwS,GAAG,EAAC,QAAQ;4BACZzE,SAAS,EAAC,8BAA8B;4BAAAD,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAiCNnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC3H,wBAAwB,GACrBA,wBAAwB,GACxB;sBAAE;wBAAA6H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBAEjD9N,OAAA;kBAAK+N,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNnO,OAAA;kBAAK+N,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAC/C7I,QAAQ,KAAK,SAAS,IACvBI,YAAY,KAAK,WAAW,gBAC1BrF,OAAA;oBAAK+N,SAAS,EAAC,kCAAkC;oBAAAD,QAAA,gBAC/C9N,OAAA;sBAAK+N,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,gBAC5C9N,OAAA;wBAAK+N,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAE9C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNnO,OAAA;wBAAA8N,QAAA,eACE9N,OAAA;0BACE+N,SAAS,EAAC,wEAAwE;0BAClFoB,IAAI,EAAC,MAAM;0BACXC,WAAW,EAAC,wBAAwB;0BACpCC,KAAK,EAAE5I,SAAU;0BACjB6I,QAAQ,EAAGC,CAAC,IAAK;4BACf7I,YAAY,CAAC6I,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;4BAC5B;4BACA,IAAIxI,OAAO,IAAIA,OAAO,GAAG0I,CAAC,CAACN,MAAM,CAACI,KAAK,EAAE;8BACvCvI,UAAU,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;4BAC5B;0BACF;wBAAE;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnO,OAAA;sBAAK+N,SAAS,EAAC,+BAA+B;sBAAAD,QAAA,gBAC5C9N,OAAA;wBAAK+N,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAE9C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNnO,OAAA;wBAAA8N,QAAA,eACE9N,OAAA;0BACE+N,SAAS,EAAC,wEAAwE;0BAClFoB,IAAI,EAAC,MAAM;0BACXC,WAAW,EAAC,sBAAsB;0BAClCC,KAAK,EAAExI,OAAQ;0BACfyI,QAAQ,EAAGC,CAAC,IAAKzI,UAAU,CAACyI,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;0BAC5CyD,QAAQ,EAAE,CAACrM,SAAU;0BACrB6L,GAAG,EAAE7L;wBAAU;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENnO,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9BC,KAAK,EAAEhJ,eAAgB;wBACvBiJ,QAAQ,EAAGC,CAAC,IACVjJ,kBAAkB,CAACiJ,CAAC,CAACN,MAAM,CAACI,KAAK;sBAClC;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAE1C9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAEpI,eAAgB;wBACvBqI,QAAQ,EAAGC,CAAC,IAAKrI,kBAAkB,CAACqI,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNnO,OAAA;kBAAK+N,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,EAAC;gBAE1D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAEhI,YAAa;wBACpBiI,QAAQ,EAAGE,MAAM,IAAK;0BAAA,IAAAuD,aAAA;0BACpBzL,eAAe,CAACkI,MAAM,CAAC;0BACvB;0BACA,IAAIwD,eAAe,IAAAD,aAAA,GAAGvD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,KAAK,cAAA0D,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC;0BACAhH,YAAY,CAAC,IAAI,CAAC;0BAElB,MAAMkH,aAAa,GAAG5G,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuD,IAAI,CAClCwC,IAAI,IAAKA,IAAI,CAACL,EAAE,KAAKiB,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAC,qBAAA;4BACjBnP,mBAAmB,EAAAmP,qBAAA,GACjBD,aAAa,CAACE,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;4BACD;4BACAzF,UAAU,CAAC,MAAM;8BACf1B,YAAY,CAAC,KAAK,CAAC;4BACrB,CAAC,EAAE,GAAG,CAAC;0BACT,CAAC,MAAM;4BACLhI,mBAAmB,CAAC,EAAE,CAAC;4BACvBgI,YAAY,CAAC,KAAK,CAAC;0BACrB;wBACF,CAAE;wBACFgC,SAAS,EAAC,SAAS;wBACnBoC,OAAO,EAAE9D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE1B,GAAG,CAAEyH,IAAI,KAAM;0BACjC/C,KAAK,EAAE+C,IAAI,CAACL,EAAE;0BACdhC,KAAK,EAAEqC,IAAI,CAACC,SAAS,IAAI,EAAE;0BAC3B7P,IAAI,EAAE4P,IAAI,CAAC5P,IAAI,IAAI,EAAE;0BACrBI,OAAO,EAAEwP,IAAI,CAACxP,OAAO,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJqP,YAAY,EAAEA,CAACzC,MAAM,EAAE0C,UAAU,KAAK;0BAAA,IAAAkB,aAAA,EAAAC,YAAA,EAAAC,eAAA;0BACpC;0BACA,MAAMC,UAAU,GAAGrB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC;0BAC5C,OACE,EAAAiB,aAAA,GAAA5D,MAAM,CAACO,KAAK,cAAAqD,aAAA,uBAAZA,aAAA,CACIjB,WAAW,CAAC,CAAC,CACdV,QAAQ,CAAC8B,UAAU,CAAC,OAAAF,YAAA,GACvB7D,MAAM,CAAChN,IAAI,cAAA6Q,YAAA,uBAAXA,YAAA,CACIlB,WAAW,CAAC,CAAC,CACdV,QAAQ,CAAC8B,UAAU,CAAC,OAAAD,eAAA,GACvB9D,MAAM,CAAC5M,OAAO,cAAA0Q,eAAA,uBAAdA,eAAA,CACInB,WAAW,CAAC,CAAC,CACdV,QAAQ,CAAC8B,UAAU,CAAC;wBAE3B,CAAE;wBACFnE,WAAW,EAAC,oBAAoB;wBAChCiB,YAAY;wBACZ;wBAAA;wBACAvE,SAAS,EAAEQ,gBAAiB;wBAC5BgE,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEnJ,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvBoJ,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCvG,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,gCAAgC;oBAAAD,QAAA,gBAC7C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,uBACV3J,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCkL,QAAQ,EAAGC,CAAC,IAAK;0BACfpL,kBAAkB,CAACoL,CAAC,CAACN,MAAM,CAACI,KAAK,CAAC;wBACpC,CAAE;wBACFA,KAAK,EAAEnL,eAAgB;wBAAA4J,QAAA,gBAEvB9N,OAAA;0BAAQqP,KAAK,EAAE;wBAAG;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3BrK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6G,GAAG,CAAC,CAAC6I,OAAO,EAAErT,KAAK;0BAAA,IAAAsT,qBAAA;0BAAA,oBACpCzT,OAAA;4BAAQqP,KAAK,EAAEmE,OAAO,CAACzB,EAAG;4BAAAjE,QAAA,IAAA2F,qBAAA,GACvBD,OAAO,CAACE,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAC1BD,OAAO,CAACG,kBAAkB,KAAK,EAAE,GAC9B,KAAK,GAAGH,OAAO,CAACG,kBAAkB,GAClC,EAAE;0BAAA;4BAAA3F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,CACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACTnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrC1J,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,gBACE9N,OAAA;wBACE+N,SAAS,EAAG,uBACVvJ,iBAAiB,GACb,eAAe,GACf,kBACL,oCAAoC;wBACrC2K,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBC,KAAK,EAAE/K,YAAa;wBACpBgL,QAAQ,EAAGC,CAAC,IAAKhL,eAAe,CAACgL,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,eACFnO,OAAA;wBAAK+N,SAAS,EAAC,yBAAyB;wBAAAD,QAAA,EACrCtJ,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAwJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9N,OAAA;oBACE6O,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IAAI6D,KAAK,GAAG,IAAI;sBAChBlL,oBAAoB,CAAC,EAAE,CAAC;sBACxBnD,uBAAuB,CAAC,EAAE,CAAC;sBAC3BI,oBAAoB,CAAC,EAAE,CAAC;sBACxB,IACE4C,YAAY,KAAK,EAAE,IACnBA,YAAY,CAACgI,KAAK,KAAK,EAAE,EACzB;wBACA7H,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDnI,KAAK,CAACsT,KAAK,CAAC,sBAAsB,CAAC;wBACnCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIxO,eAAe,KAAK,EAAE,EAAE;wBAC1BG,uBAAuB,CACrB,4BACF,CAAC;wBACDhF,KAAK,CAACsT,KAAK,CAAC,8BAA8B,CAAC;wBAC3CD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIpO,YAAY,KAAK,EAAE,EAAE;wBACvBG,oBAAoB,CAAC,4BAA4B,CAAC;wBAClDpF,KAAK,CAACsT,KAAK,CAAC,wBAAwB,CAAC;wBACrCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAIA,KAAK,EAAE;wBACT,MAAMkB,MAAM,GAAG,KAAK;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;;wBAEA,IAAI,CAACA,MAAM,EAAE;0BAAA,IAAAC,mBAAA;0BACX;0BACA,IAAIb,eAAe,IAAAa,mBAAA,GAAGxM,YAAY,CAACgI,KAAK,cAAAwE,mBAAA,cAAAA,mBAAA,GAAI,EAAE;0BAC9C,MAAMZ,aAAa,GAAG5G,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuD,IAAI,CAClCwC,IAAI,IACH0B,MAAM,CAAC1B,IAAI,CAACL,EAAE,CAAC,KAAK+B,MAAM,CAACd,eAAe,CAC9C,CAAC;0BACDtF,OAAO,CAACC,GAAG,CAACsF,aAAa,CAAC;0BAE1B,IAAIA,aAAa,EAAE;4BAAA,IAAAc,sBAAA,EAAAC,sBAAA;4BACjB;4BACA,IAAIC,cAAc,GAAG/P,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,EAAE;4BAE1C+O,aAAa,aAAbA,aAAa,wBAAAc,sBAAA,GAAbd,aAAa,CAAEE,QAAQ,cAAAY,sBAAA,uBAAvBA,sBAAA,CAAyB7I,OAAO,CAAEgJ,OAAO,IAAK;8BAC5CxG,OAAO,CAACC,GAAG,CAACuG,OAAO,CAACnC,EAAE,CAAC;4BACzB,CAAC,CAAC;4BAEF,MAAMoC,YAAY,GAChBlB,aAAa,aAAbA,aAAa,wBAAAe,sBAAA,GAAbf,aAAa,CAAEE,QAAQ,cAAAa,sBAAA,uBAAvBA,sBAAA,CAAyBpE,IAAI,CAC1BwC,IAAI,IACH0B,MAAM,CAAC1B,IAAI,CAACL,EAAE,CAAC,KAAK+B,MAAM,CAACG,cAAc,CAC7C,CAAC;4BAEH,IAAIE,YAAY,EAAE;8BAChB;8BACAlQ,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB;gCACEoQ,QAAQ,EAAEnB,aAAa;gCACvBO,OAAO,EAAEW,YAAY;gCACrBE,IAAI,EAAE/P;8BACR,CAAC,CACF,CAAC;8BACFgD,eAAe,CAAC,EAAE,CAAC;8BACnBnD,kBAAkB,CAAC,EAAE,CAAC;8BACtBI,eAAe,CAAC,EAAE,CAAC;8BACnBmJ,OAAO,CAACC,GAAG,CAAC3J,mBAAmB,CAAC;4BAClC,CAAC,MAAM;8BACLwD,oBAAoB,CAClB,kCACF,CAAC;8BACDnI,KAAK,CAACsT,KAAK,CACT,kCACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACLnL,oBAAoB,CAClB,0BACF,CAAC;4BACDnI,KAAK,CAACsT,KAAK,CAAC,0BAA0B,CAAC;0BACzC;wBACF,CAAC,MAAM;0BACLnL,oBAAoB,CAClB,4CACF,CAAC;0BACDnI,KAAK,CAACsT,KAAK,CACT,4CACF,CAAC;wBACH;sBACF;oBACF,CAAE;oBACF5E,SAAS,EAAC,uDAAuD;oBAAAD,QAAA,gBAEjE9N,OAAA;sBACEqO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrB8F,KAAK,EAAC,QAAQ;sBAAAxG,QAAA,eAEd9N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB2O,CAAC,EAAC;sBAAmD;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,EAAM;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTnO,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,gBACpC9N,OAAA;sBAAK+N,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EAAC;oBAE1D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAK+N,SAAS,EAAC,yBAAyB;sBAAAD,QAAA,EACrC9J,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE2G,GAAG,CAAC,CAAC4J,YAAY,EAAEpU,KAAK;wBAAA,IAAAqU,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,kBAAA;wBAAA,oBAC5C9U,OAAA;0BAEE+N,SAAS,EAAC,iCAAiC;0BAAAD,QAAA,gBAE3C9N,OAAA;4BAAK+N,SAAS,EAAC,qBAAqB;4BAAAD,QAAA,eAClC9N,OAAA;8BACE6O,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAMkG,eAAe,GACnB/Q,mBAAmB,CAAC4O,MAAM,CACxB,CAACoC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAK9U,KAC5B,CAAC;gCACH8D,sBAAsB,CAAC8Q,eAAe,CAAC;8BACzC,CAAE;8BAAAjH,QAAA,eAEF9N,OAAA;gCACEqO,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrB8F,KAAK,EAAC,QAAQ;gCAAAxG,QAAA,eAEd9N,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvB2O,CAAC,EAAC;gCAAuE;kCAAAX,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNnO,OAAA;4BAAK+N,SAAS,EAAC,2BAA2B;4BAAAD,QAAA,gBACxC9N,OAAA;8BAAA8N,QAAA,gBACE9N,OAAA;gCAAA8N,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAqG,qBAAA,IAAAC,sBAAA,GACnBF,YAAY,CAACH,QAAQ,cAAAK,sBAAA,uBAArBA,sBAAA,CAAuBpC,SAAS,cAAAmC,qBAAA,cAAAA,qBAAA,GAAI,KAAK;4BAAA;8BAAAxG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNnO,OAAA;8BAAA8N,QAAA,gBACE9N,OAAA;gCAAA8N,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAuG,qBAAA,IAAAC,sBAAA,GAClBJ,YAAY,CAACf,OAAO,cAAAmB,sBAAA,uBAApBA,sBAAA,CAAsBjB,YAAY,cAAAgB,qBAAA,cAAAA,qBAAA,GAAI,IAAI;4BAAA;8BAAA1G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxC,CAAC,eACNnO,OAAA;8BAAA8N,QAAA,gBACE9N,OAAA;gCAAA8N,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAyG,sBAAA,IAAAC,sBAAA,GACrBN,YAAY,CAACf,OAAO,cAAAqB,sBAAA,uBAApBA,sBAAA,CAAsBlB,kBAAkB,cAAAiB,sBAAA,cAAAA,sBAAA,GACvC,KAAK;4BAAA;8BAAA5G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNnO,OAAA;8BAAA8N,QAAA,gBACE9N,OAAA;gCAAA8N,QAAA,EAAG;8BAAK;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,KAAC,GAAA2G,kBAAA,GAACP,YAAY,CAACF,IAAI,cAAAS,kBAAA,cAAAA,kBAAA,GAAI,KAAK;4BAAA;8BAAA9G,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GA9CDhO,KAAK;0BAAA6N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA+CP,CAAC;sBAAA,CACP;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D9N,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnO,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI6D,KAAK,GAAG,IAAI;oBAChBtM,2BAA2B,CAAC,EAAE,CAAC;oBAC/BoB,oBAAoB,CAAC,EAAE,CAAC;;oBAExB;oBACA;oBACA;oBACA;oBACA;oBACA;;oBAEA;oBACA;oBACA;oBACA;oBACA;oBACA;;oBAEA,IAAIkL,KAAK,EAAE;sBACT7G,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLxM,KAAK,CAACsT,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACF5E,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf9N,OAAA;gBAAK+N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAA,GACMoK,0BAA0B,CAAC;oBAAE2D,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG9N,OAAA;oBAAA,GAAWsK,2BAA2B,CAAC;kBAAC;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5CnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB9N,OAAA;sBACEqO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D9N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB2O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnO,OAAA;kBAAOkV,KAAK,EAAE5U,eAAgB;kBAAAwN,QAAA,eAC5B9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnC7D,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEzK,KAAK,kBAC3CH,OAAA;sBACE+N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF9N,OAAA;wBAAK+N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBgG,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,gBAEd9N,OAAA;4BAAM2O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOnO,OAAA;4BAAM2O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBAAK+N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD9N,OAAA;0BAAK+N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAACoF;wBAAI;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNnO,OAAA;0BAAA8N,QAAA,GACG,CAAClD,IAAI,CAACuK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBACE6O,OAAO,EAAEA,CAAA,KAAM;0BACb3E,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAACkI,MAAM,CACd,CAACoC,CAAC,EAAEK,aAAa,KACflV,KAAK,KAAKkV,aACd,CACF,CAAC;wBACH,CAAE;wBACFtH,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrB8F,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,eAEd9N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB2O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAACoF,IAAI;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D9N,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnO,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf9N,OAAA;gBAAK+N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvCC,KAAK,EAAEhH,aAAc;wBACrBiH,QAAQ,EAAGC,CAAC,IAAKjH,gBAAgB,CAACiH,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpCC,KAAK,EAAE5G,UAAW;wBAClB6G,QAAQ,EAAGC,CAAC,IAAK7G,aAAa,CAAC6G,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnO,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/BC,KAAK,EAAExG,MAAO;wBACdyG,QAAQ,EAAGC,CAAC,IAAKzG,SAAS,CAACyG,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAA,GACMsL,yBAAyB,CAAC;oBAAEyC,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG9N,OAAA;oBAAA,GAAWuL,0BAA0B,CAAC;kBAAC;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3CnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB9N,OAAA;sBACEqO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D9N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB2O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnO,OAAA;kBAAOkV,KAAK,EAAE5U,eAAgB;kBAAAwN,QAAA,eAC5B9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnC1C,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEzK,KAAK,kBACnCH,OAAA;sBACE+N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF9N,OAAA;wBAAK+N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBgG,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,gBAEd9N,OAAA;4BAAM2O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOnO,OAAA;4BAAM2O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBAAK+N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD9N,OAAA;0BAAK+N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAACoF;wBAAI;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNnO,OAAA;0BAAA8N,QAAA,GACG,CAAClD,IAAI,CAACuK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBACE6O,OAAO,EAAEA,CAAA,KAAM;0BACbxD,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAACkI,MAAM,CACd,CAACoC,CAAC,EAAEK,aAAa,KACflV,KAAK,KAAKkV,aACd,CACF,CAAC;wBACH,CAAE;wBACFtH,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrB8F,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,eAEd9N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB2O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJvD,IAAI,CAACoF,IAAI;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNnO,OAAA;gBAAK+N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D9N,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnO,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,gBACf9N,OAAA;gBAAK+N,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,gBAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA,CAACR,MAAM;wBACL6P,KAAK,EAAEpG,gBAAiB;wBACxBqG,QAAQ,EAAGE,MAAM,IAAK;0BACpBtG,mBAAmB,CAACsG,MAAM,CAAC;wBAC7B,CAAE;wBACFW,OAAO,EAAEpD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpC,GAAG,CAAEmH,SAAS,KAAM;0BACvCzC,KAAK,EAAEyC,SAAS,CAACC,EAAE;0BACnBhC,KAAK,EAAE+B,SAAS,CAACE,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJC,YAAY,EAAEA,CAACzC,MAAM,EAAE0C,UAAU,KAC/B1C,MAAM,CAACO,KAAK,CACToC,WAAW,CAAC,CAAC,CACbV,QAAQ,CAACS,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDpE,SAAS,EAAC,SAAS;wBACnBqB,WAAW,EAAC,qBAAqB;wBACjCiB,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAEvE,KAAK,MAAM;4BACzB,GAAGuE,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEvH,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBwH,SAAS,EAAE1E,KAAK,CAAC2E,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACFlB,MAAM,EAAGgB,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjQ,OAAO,EAAE,MAAM;4BACfsQ,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA7C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnO,OAAA;oBAAK+N,SAAS,EAAC,+BAA+B;oBAAAD,QAAA,gBAC5C9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACE+N,SAAS,EAAC,wEAAwE;wBAClFoB,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAE5F,YAAa;wBACpB6F,QAAQ,EAAGC,CAAC,IAAK7F,eAAe,CAAC6F,CAAC,CAACN,MAAM,CAACI,KAAK;sBAAE;wBAAArB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD9N,OAAA;kBAAK+N,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eAC1C9N,OAAA;oBAAK+N,SAAS,EAAC,sBAAsB;oBAAAD,QAAA,gBACnC9N,OAAA;sBAAK+N,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNnO,OAAA;sBAAA8N,QAAA,eACE9N,OAAA;wBACEqP,KAAK,EAAExF,aAAc;wBACrByF,QAAQ,EAAGC,CAAC,IAAKzF,gBAAgB,CAACyF,CAAC,CAACN,MAAM,CAACI,KAAK,CAAE;wBAClDtB,SAAS,EAAC,wEAAwE;wBAAAD,QAAA,gBAElF9N,OAAA;0BAAQqP,KAAK,EAAE,EAAG;0BAAAvB,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzCnO,OAAA;0BAAQqP,KAAK,EAAE,SAAU;0BAAAvB,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1CnO,OAAA;0BAAQqP,KAAK,EAAE,UAAW;0BAAAvB,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5CnO,OAAA;0BAAQqP,KAAK,EAAE,QAAS;0BAAAvB,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNnO,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBACjD9N,OAAA;kBAAA,GACM0L,wCAAwC,CAAC;oBAC3CqC,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wFAAwF;kBAAAD,QAAA,gBAElG9N,OAAA;oBAAA,GAAW2L,yCAAyC,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,eACnB9N,OAAA;sBACEqO,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBT,SAAS,EAAC,iDAAiD;sBAAAD,QAAA,eAE3D9N,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvB2O,CAAC,EAAC;sBAA4G;wBAAAX,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNnO,OAAA;kBAAOkV,KAAK,EAAE5U,eAAgB;kBAAAwN,QAAA,eAC5B9N,OAAA;oBAAK+N,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,EACnCtC,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEzK,KAAK,kBACVH,OAAA;sBACE+N,SAAS,EAAC,0EAA0E;sBAAAD,QAAA,gBAGpF9N,OAAA;wBAAK+N,SAAS,EAAC,kEAAkE;wBAAAD,QAAA,eAC/E9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBgG,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,gBAEd9N,OAAA;4BAAM2O,CAAC,EAAC;0BAAqN;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOnO,OAAA;4BAAM2O,CAAC,EAAC;0BAAuI;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBAAK+N,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,gBACjD9N,OAAA;0BAAK+N,SAAS,EAAC,gFAAgF;0BAAAD,QAAA,EAC5FlD,IAAI,CAACoF;wBAAI;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNnO,OAAA;0BAAA8N,QAAA,GACG,CAAClD,IAAI,CAACuK,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAApH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNnO,OAAA;wBACE6O,OAAO,EAAEA,CAAA,KAAM;0BACbpD,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAACkI,MAAM,CACd,CAACoC,CAAC,EAAEK,aAAa,KACflV,KAAK,KAAKkV,aACd,CACJ,CAAC;wBACH,CAAE;wBACFtH,SAAS,EAAC,wDAAwD;wBAAAD,QAAA,eAElE9N,OAAA;0BACEqO,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrB8F,KAAK,EAAC,QAAQ;0BAAAxG,QAAA,eAEd9N,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvB2O,CAAC,EAAC;0BAAsB;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJvD,IAAI,CAACoF,IAAI;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENnO,OAAA;gBAAK+N,SAAS,EAAC,6CAA6C;gBAAAD,QAAA,gBAC1D9N,OAAA;kBACE6O,OAAO,EAAEA,CAAA,KAAMhD,aAAa,CAAC,CAAC,CAAE;kBAChCkC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnO,OAAA;kBACE8S,QAAQ,EAAEpG,cAAe;kBACzBmC,OAAO,EAAE,MAAAA,CAAA,KAAY;oBAAA,IAAAyG,mBAAA;oBACnB,MAAMC,aAAa,GAAGvR,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE2G,GAAG,CAC3CyH,IAAI;sBAAA,IAAAoD,aAAA,EAAAC,cAAA;sBAAA,OAAM;wBACTjC,OAAO,GAAAgC,aAAA,GAAEpD,IAAI,CAACoB,OAAO,cAAAgC,aAAA,uBAAZA,aAAA,CAAczD,EAAE;wBACzBqC,QAAQ,GAAAqB,cAAA,GAAErD,IAAI,CAACgC,QAAQ,cAAAqB,cAAA,uBAAbA,cAAA,CAAe1D,EAAE;wBAC3BsC,IAAI,EAAEjC,IAAI,CAACiC;sBACb,CAAC;oBAAA,CACH,CAAC;oBACD,MAAMtT,QAAQ,CACZxB,UAAU,CAAC;sBACTmW,UAAU,EAAE1U,SAAS,CAAC2U,IAAI,CAAC,CAAC;sBAC5BC,SAAS,EAAExU,QAAQ,CAACuU,IAAI,CAAC,CAAC;sBAC1BtD,SAAS,EAAErR,SAAS,CAAC2U,IAAI,CAAC,CAAC,GAAG,GAAG,GAAGvU,QAAQ,CAACuU,IAAI,CAAC,CAAC;sBACnDE,SAAS,EAAEjU,SAAS;sBACpBkU,aAAa,EAAE9T,KAAK;sBACpB+T,aAAa,EAAEvU,KAAK;sBACpBwU,eAAe,EAAE5T,OAAO;sBACxB6T,YAAY,EAAEzT,IAAI;sBAClB0T,eAAe,EAAEtT,OAAO,CAACyM,KAAK;sBAC9B;sBACA3L,WAAW,EAAEA,WAAW,CAAC2L,KAAK;sBAC9B8G,SAAS,EAAEzR,QAAQ;sBACnB0R,SAAS,EAAEnR,QAAQ;sBACnBoR,cAAc,EACZpR,QAAQ,KAAK,SAAS,GAAGI,YAAY,GAAG,EAAE;sBAC5CiR,gBAAgB,EAAE7Q,eAAe;sBACjC;sBACA8Q,mBAAmB,EAAE1Q,eAAe;sBACpC2Q,WAAW,EAAEvQ,mBAAmB;sBAChCwQ,gBAAgB,EACdpR,YAAY,KAAK,WAAW,GACxB,EAAE,GACFgB,eAAe;sBACrBqQ,UAAU,EACRrR,YAAY,KAAK,WAAW,GAAGoB,SAAS,GAAG,EAAE;sBAC/CkQ,QAAQ,EACNtR,YAAY,KAAK,WAAW,GAAGwB,OAAO,GAAG,EAAE;sBAC7C+P,gBAAgB,EAAE3P,eAAe;sBACjCmN,QAAQ,EAAE/M,YAAY,CAACgI,KAAK;sBAC5B;sBACAwH,cAAc,EAAExO,aAAa;sBAC7ByO,WAAW,EAAErO,UAAU;sBACvBsO,cAAc,EAAElO,MAAM;sBACtBiJ,SAAS,EAAE7I,gBAAgB,CAACoG,KAAK;sBACjC2H,gBAAgB,EAAE3N,eAAe;sBACjC4N,aAAa,EAAExN,YAAY;sBAC3ByN,gBAAgB,EAAErN,aAAa;sBAC/B;sBACAsN,uBAAuB,EAAElN,0BAA0B;sBACnDmN,cAAc,EAAEhM,kBAAkB;sBAClCiM,8BAA8B,EAC5B7L,iCAAiC;sBACnC;sBACAa,SAAS,EAAEkJ,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,EAAE;sBAC9B;sBACA+B,MAAM,EAAEtU,KAAK,GAAG,MAAM,GAAG,OAAO;sBAChCuU,WAAW,EAAEjU,UAAU;sBACvBkU,cAAc,GAAAlC,mBAAA,GAAEpS,YAAY,CAACmM,KAAK,cAAAiG,mBAAA,cAAAA,mBAAA,GAAI;oBACxC,CAAC,CACH,CAAC;kBACH,CAAE;kBACFvH,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAEjEpB,cAAc,GAAG,WAAW,GAAG;gBAAQ;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEPvC,UAAU,KAAK,CAAC,gBACf5L,OAAA;cAAK+N,SAAS,EAAC,EAAE;cAAAD,QAAA,eACf9N,OAAA;gBAAK+N,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,eACjD9N,OAAA;kBAAK+N,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,gBACjE9N,OAAA;oBACEqO,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBT,SAAS,EAAC,oEAAoE;oBAAAD,QAAA,eAE9E9N,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvB2O,CAAC,EAAC;oBAAuB;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,wCAAwC;oBAAAD,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,oDAAoD;oBAAAD,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnO,OAAA;oBAAK+N,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,eAS1D9N,OAAA;sBACEoO,IAAI,EAAC,YAAY;sBACjBL,SAAS,EAAC,wDAAwD;sBAAAD,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvN,EAAA,CAhgFQD,aAAa;EAAA,QACHzB,WAAW,EACXD,WAAW,EACXF,WAAW,EAwHxBU,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA8BGT,WAAW,EAGPA,WAAW,EAGdA,WAAW,EAGPA,WAAW,EAGTA,WAAW;AAAA;AAAAyY,EAAA,GA7N7B9W,aAAa;AAkgFtB,eAAeA,aAAa;AAAC,IAAA8W,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}