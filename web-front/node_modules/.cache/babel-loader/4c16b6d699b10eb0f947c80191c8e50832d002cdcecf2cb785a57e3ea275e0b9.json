{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{confirmResetPasswordReducer,coordinatorsListReducer,createCoordinatorReducer,createNewUserReducer,deleteUserReducer,detailCoordinatorReducer,getProfileUserReducer,historyListCoordinatorReducer,historyListLoggedReducer,logoutSavedUserReducer,resetPasswordReducer,updateCoordinatorReducer,updateLastLoginUserReducer,updatePasswordUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListCoordinatorReducer,caseListInsuranceReducer,caseListLoggedReducer,caseListMapReducer,caseListProviderReducer,caseListReducer,caseHistoryReducer,commentCaseListReducer,createNewCaseReducer,createNewCommentCaseReducer,deleteCaseReducer,deleteCommentCaseReducer,detailCaseReducer,duplicateCaseReducer,updateCaseAssignedReducer,updateCaseReducer,updateCaseStatusReducer}from\"./reducers/caseReducers\";import{addNewProviderReducer,deleteProviderReducer,detailProviderReducer,providerListReducer,updateProviderReducer}from\"./reducers/providerReducers\";import{addNewInsuranceReducer,deleteInsuranceReducer,detailInsuranceReducer,insuranceListReducer,updateInsuranceReducer}from\"./reducers/insurancereducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,caseListMap:caseListMapReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,updateCaseStatus:updateCaseStatusReducer,caseListCoordinator:caseListCoordinatorReducer,updateCaseAssigned:updateCaseAssignedReducer,caseListInsurance:caseListInsuranceReducer,caseListProvider:caseListProviderReducer,caseListLogged:caseListLoggedReducer,duplicateCase:duplicateCaseReducer,caseHistory:caseHistoryReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,addNewProvider:addNewProviderReducer,deleteProvider:deleteProviderReducer,updateProvider:updateProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\ninsuranceList:insuranceListReducer,addNewInsurance:addNewInsuranceReducer,deleteInsurance:deleteInsuranceReducer,detailInsurance:detailInsuranceReducer,updateInsurance:updateInsuranceReducer,//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer,updatePasswordUser:updatePasswordUserReducer,updateLastLoginUser:updateLastLoginUserReducer,historyListLogged:historyListLoggedReducer,historyListCoordinator:historyListCoordinatorReducer,//\ncoordinatorsList:coordinatorsListReducer,createCoordinator:createCoordinatorReducer,detailCoordinator:detailCoordinatorReducer,updateCoordinator:updateCoordinatorReducer,//\ncommentCaseList:commentCaseListReducer,createNewCommentCase:createNewCommentCaseReducer,deleteCommentCase:deleteCommentCaseReducer,//\nlogoutSavedUser:logoutSavedUserReducer,resetPassword:resetPasswordReducer,confirmResetPassword:confirmResetPasswordReducer//\n});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,applyMiddleware(...middleware));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "confirmResetPasswordReducer", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "logoutSavedUserReducer", "resetPasswordReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListMapReducer", "caseListProviderReducer", "caseListReducer", "caseHistoryReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "deleteCommentCaseReducer", "detailCaseReducer", "duplicateCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "updateCaseStatusReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "caseListMap", "detailCase", "createNewCase", "deleteCase", "updateCase", "updateCaseStatus", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "duplicateCase", "caseHistory", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "deleteCommentCase", "logoutSavedUser", "resetPassword", "confirmResetPassword", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  confirmResetPasswordReducer,\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  logoutSavedUserReducer,\n  resetPasswordReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListMapReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  caseHistoryReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  deleteCommentCaseReducer,\n  detailCaseReducer,\n  duplicateCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n  updateCaseStatusReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer,\n\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,2BAA2B,CAC3BC,uBAAuB,CACvBC,wBAAwB,CACxBC,oBAAoB,CACpBC,iBAAiB,CACjBC,wBAAwB,CACxBC,qBAAqB,CACrBC,6BAA6B,CAC7BC,wBAAwB,CACxBC,sBAAsB,CACtBC,oBAAoB,CACpBC,wBAAwB,CACxBC,0BAA0B,CAC1BC,yBAAyB,CACzBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,0BAA0B,CAC1BC,wBAAwB,CACxBC,qBAAqB,CACrBC,kBAAkB,CAClBC,uBAAuB,CACvBC,eAAe,CACfC,kBAAkB,CAClBC,sBAAsB,CACtBC,oBAAoB,CACpBC,2BAA2B,CAC3BC,iBAAiB,CACjBC,wBAAwB,CACxBC,iBAAiB,CACjBC,oBAAoB,CACpBC,yBAAyB,CACzBC,iBAAiB,CACjBC,uBAAuB,KAClB,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,qBAAqB,CACrBC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CACpC,OACEC,sBAAsB,CACtBC,sBAAsB,CACtBC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,KACjB,8BAA8B,CAErC,KAAM,CAAAC,OAAO,CAAGrD,eAAe,CAAC,CAC9BsD,SAAS,CAAEnC,gBAAgB,CAE3B;AACAoC,QAAQ,CAAExB,eAAe,CACzByB,WAAW,CAAE3B,kBAAkB,CAC/B4B,UAAU,CAAEnB,iBAAiB,CAC7BoB,aAAa,CAAExB,oBAAoB,CACnCyB,UAAU,CAAEvB,iBAAiB,CAC7BwB,UAAU,CAAEnB,iBAAiB,CAC7BoB,gBAAgB,CAAEnB,uBAAuB,CACzCoB,mBAAmB,CAAEpC,0BAA0B,CAC/CqC,kBAAkB,CAAEvB,yBAAyB,CAC7CwB,iBAAiB,CAAErC,wBAAwB,CAC3CsC,gBAAgB,CAAEnC,uBAAuB,CACzCoC,cAAc,CAAEtC,qBAAqB,CACrCuC,aAAa,CAAE5B,oBAAoB,CACnC6B,WAAW,CAAEpC,kBAAkB,CAC/B;AACAqC,YAAY,CAAEvB,mBAAmB,CACjCwB,cAAc,CAAEzB,qBAAqB,CACrC0B,cAAc,CAAE5B,qBAAqB,CACrC6B,cAAc,CAAE5B,qBAAqB,CACrC6B,cAAc,CAAE1B,qBAAqB,CACrC;AACA2B,UAAU,CAAErD,iBAAiB,CAC7BsD,eAAe,CAAErD,sBAAsB,CACvCsD,YAAY,CAAEpD,mBAAmB,CACjCqD,YAAY,CAAEpD,mBAAmB,CACjCqD,YAAY,CAAEvD,mBAAmB,CACjC;AACAwD,aAAa,CAAE5B,oBAAoB,CACnC6B,eAAe,CAAEhC,sBAAsB,CACvCiC,eAAe,CAAEhC,sBAAsB,CACvCiC,eAAe,CAAEhC,sBAAsB,CACvCiC,eAAe,CAAE/B,sBAAsB,CAEvC;AACAgC,SAAS,CAAEhE,gBAAgB,CAC3BiE,aAAa,CAAE9E,oBAAoB,CACnC+E,cAAc,CAAE5E,qBAAqB,CACrC6E,iBAAiB,CAAErE,wBAAwB,CAC3CsE,UAAU,CAAEhF,iBAAiB,CAC7BiF,kBAAkB,CAAExE,yBAAyB,CAC7CyE,mBAAmB,CAAE1E,0BAA0B,CAC/C2E,iBAAiB,CAAE/E,wBAAwB,CAC3CgF,sBAAsB,CAAEjF,6BAA6B,CACrD;AACAkF,gBAAgB,CAAExF,uBAAuB,CACzCyF,iBAAiB,CAAExF,wBAAwB,CAC3CyF,iBAAiB,CAAEtF,wBAAwB,CAC3CuF,iBAAiB,CAAEjF,wBAAwB,CAC3C;AACAkF,eAAe,CAAEhE,sBAAsB,CACvCiE,oBAAoB,CAAE/D,2BAA2B,CACjDgE,iBAAiB,CAAE9D,wBAAwB,CAC3C;AACA+D,eAAe,CAAEvF,sBAAsB,CACvCwF,aAAa,CAAEvF,oBAAoB,CACnCwF,oBAAoB,CAAElG,2BAEtB;AACF,CAAC,CAAC,CAEF,KAAM,CAAAmG,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnBtD,SAAS,CAAE,CAAEuD,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAAC5G,KAAK,CAAC,CAE1B,KAAM,CAAA6G,KAAK,CAAGhH,WAAW,CACvBsD,OAAO,CACPuD,YAAY,CACZ3G,eAAe,CAAC,GAAG6G,UAAU,CAC/B,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}