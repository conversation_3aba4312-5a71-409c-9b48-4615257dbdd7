{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersListEditCase}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase,updateCaseStep}from\"../../redux/actions/caseActions\";import LoadingSpinner from\"../../components/LoadingSpinner\";import GoogleComponent from\"react-google-autocomplete\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{insurancesListDashboard}from\"../../redux/actions/insuranceActions\";import{coordinatorsListDashboard}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";// Country to Currency mapping - using exact country names from COUNTRIES constant\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const COUNTRY_CURRENCY_MAP={\"Morocco\":\"MAD\",\"United States\":\"USD\",\"Canada\":\"CAD\",\"United Kingdom\":\"GBP\",\"France\":\"EUR\",\"Germany\":\"EUR\",\"Spain\":\"EUR\",\"Italy\":\"EUR\",\"Netherlands\":\"EUR\",\"Belgium\":\"EUR\",\"Portugal\":\"EUR\",\"Greece\":\"EUR\",\"Austria\":\"EUR\",\"Ireland\":\"EUR\",\"Finland\":\"EUR\",\"Luxembourg\":\"EUR\",\"Estonia\":\"EUR\",\"Slovenia\":\"EUR\",\"Slovakia\":\"EUR\",\"Malta\":\"EUR\",\"Cyprus\":\"EUR\",\"Lithuania\":\"EUR\",\"Latvia\":\"EUR\",\"Japan\":\"JPY\",\"China\":\"CNY\",\"India\":\"INR\",\"Australia\":\"AUD\",\"New Zealand\":\"NZD\",\"South Africa\":\"ZAR\",\"Brazil\":\"BRL\",\"Mexico\":\"MXN\",\"Argentina\":\"ARS\",\"Chile\":\"CLP\",\"Colombia\":\"COP\",\"Peru\":\"PEN\",\"Russia\":\"RUB\",\"Turkey\":\"TRY\",\"Egypt\":\"EGP\",\"Saudi Arabia\":\"SAR\",\"United Arab Emirates\":\"AED\",\"Qatar\":\"QAR\",\"Kuwait\":\"KWD\",\"Bahrain\":\"BHD\",\"Oman\":\"OMR\",\"Jordan\":\"JOD\",\"Lebanon\":\"LBP\",\"Israel\":\"ILS\",\"South Korea\":\"KRW\",\"Thailand\":\"THB\",\"Malaysia\":\"MYR\",\"Singapore\":\"SGD\",\"Indonesia\":\"IDR\",\"Philippines\":\"PHP\",\"Vietnam\":\"VND\",\"Pakistan\":\"PKR\",\"Bangladesh\":\"BDT\",\"Sri Lanka\":\"LKR\",\"Nepal\":\"NPR\",\"Switzerland\":\"CHF\",\"Norway\":\"NOK\",\"Sweden\":\"SEK\",\"Denmark\":\"DKK\",\"Iceland\":\"ISK\",\"Poland\":\"PLN\",\"Czech Republic\":\"CZK\",\"Hungary\":\"HUF\",\"Romania\":\"RON\",\"Bulgaria\":\"BGN\",\"Croatia\":\"HRK\",\"Serbia\":\"RSD\",\"Ukraine\":\"UAH\",\"Belarus\":\"BYN\",\"Algeria\":\"DZD\",\"Tunisia\":\"TND\",\"Libya\":\"LYD\",\"Sudan\":\"SDG\",\"Ethiopia\":\"ETB\",\"Kenya\":\"KES\",\"Uganda\":\"UGX\",\"Tanzania\":\"TZS\",\"Rwanda\":\"RWF\",\"Ghana\":\"GHS\",\"Nigeria\":\"NGN\",\"Senegal\":\"XOF\",\"Ivory Coast\":\"XOF\",\"Mali\":\"XOF\",\"Burkina Faso\":\"XOF\",\"Niger\":\"XOF\",\"Guinea\":\"GNF\",\"Sierra Leone\":\"SLL\",\"Liberia\":\"LRD\",\"Cameroon\":\"XAF\",\"Chad\":\"XAF\",\"Central African Republic\":\"XAF\",\"Democratic Republic of the Congo\":\"CDF\",\"Republic of the Congo\":\"XAF\",\"Gabon\":\"XAF\",\"Angola\":\"AOA\",\"Zambia\":\"ZMK\",\"Zimbabwe\":\"ZWL\",\"Botswana\":\"BWP\",\"Namibia\":\"NAD\",\"Lesotho\":\"LSL\",\"Swaziland\":\"SZL\",\"Mozambique\":\"MZN\",\"Madagascar\":\"MGA\",\"Mauritius\":\"MUR\",\"Seychelles\":\"SCR\",\"Afghanistan\":\"AFN\",\"Albania\":\"ALL\",\"Armenia\":\"AMD\",\"Azerbaijan\":\"AZN\",\"Brunei\":\"BND\",\"Cambodia\":\"KHR\",\"Cape Verde\":\"CVE\",\"Comoros\":\"KMF\",\"Costa Rica\":\"CRC\",\"Cuba\":\"CUP\",\"Dominican Republic\":\"DOP\",\"Ecuador\":\"USD\",\"El Salvador\":\"USD\",\"Eritrea\":\"ERN\",\"Fiji\":\"FJD\",\"Georgia\":\"GEL\",\"Guatemala\":\"GTQ\",\"Guinea-Bissau\":\"XOF\",\"Guyana\":\"GYD\",\"Haiti\":\"HTG\",\"Honduras\":\"HNL\",\"Hong Kong\":\"HKD\",\"Iran\":\"IRR\",\"Iraq\":\"IQD\",\"Jamaica\":\"JMD\",\"Kazakhstan\":\"KZT\",\"Kyrgyzstan\":\"KGS\",\"Laos\":\"LAK\",\"Macau\":\"MOP\",\"Macedonia\":\"MKD\",\"Malawi\":\"MWK\",\"Maldives\":\"MVR\",\"Marshall Islands\":\"USD\",\"Mauritania\":\"MRU\",\"Micronesia\":\"USD\",\"Moldova\":\"MDL\",\"Monaco\":\"EUR\",\"Mongolia\":\"MNT\",\"Montenegro\":\"EUR\",\"Myanmar\":\"MMK\",\"Nicaragua\":\"NIO\",\"North Korea\":\"KPW\",\"Panama\":\"PAB\",\"Papua New Guinea\":\"PGK\",\"Paraguay\":\"PYG\",\"Puerto Rico\":\"USD\",\"Samoa\":\"WST\",\"San Marino\":\"EUR\",\"Sao Tome and Principe\":\"STN\",\"Somalia\":\"SOS\",\"South Sudan\":\"SSP\",\"Suriname\":\"SRD\",\"Syria\":\"SYP\",\"Taiwan\":\"TWD\",\"Tajikistan\":\"TJS\",\"Togo\":\"XOF\",\"Tonga\":\"TOP\",\"Trinidad and Tobago\":\"TTD\",\"Turkmenistan\":\"TMT\",\"Tuvalu\":\"AUD\",\"Uruguay\":\"UYU\",\"Uzbekistan\":\"UZS\",\"Vanuatu\":\"VUV\",\"Venezuela\":\"VES\",\"Western Sahara\":\"MAD\",\"Yemen\":\"YER\"};const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){var _parseInt;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const section=searchParams.get(\"section\")||0;//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorId,setCoordinatorId]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerMultiSelectDelete,setProviderMultiSelectDelete]=useState([]);const[assistanceMultiSelect,setAssistanceMultiSelect]=useState([]);const[assistanceMultiSelectDelete,setAssistanceMultiSelectDelete]=useState([]);const[providerMultiSelectLast,setProviderMultiSelectLast]=useState([]);const[assistanceMultiSelectLast,setAssistanceMultiSelectLast]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseTypeItem,setCaseTypeItem]=useState(\"\");const[caseTypeItemError,setCaseTypeItemError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerDate,setProviderDate]=useState(\"\");const[providerDateError,setProviderDateError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState((_parseInt=parseInt(section))!==null&&_parseInt!==void 0?_parseInt:0);const[isLoading,setIsLoading]=useState(true);const[shouldNavigateToFinalStep,setShouldNavigateToFinalStep]=useState(false);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;// Debug log when providers data changes\nuseEffect(()=>{if(providers&&providers.length>0){console.log(\"Providers data loaded successfully:\",providers.length);}},[providers]);const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;// Update coordinator when coordinators are loaded\nuseEffect(()=>{console.log(\"Coordinator useEffect triggered\");if(coordinators&&coordinators.length>0&&coordinatorId){console.log(\"Trying to find coordinator with ID:\",coordinatorId);// Try to find coordinator by ID (as string to ensure type matching)\nconst foundCoordinator=coordinators.find(item=>String(item.id)===String(coordinatorId));if(foundCoordinator){console.log(\"Found coordinator:\",foundCoordinator.full_name);// Set the coordinator with a slight delay to ensure the UI updates\nsetTimeout(()=>{setCoordinator({value:foundCoordinator.id,label:foundCoordinator.full_name});// Force a re-render by updating the loading state\nsetIsLoading(false);},100);}else{console.log(\"Coordinator not found in the list\");// If coordinator not found, try to find it by name\nconst coordinatorById=coordinators.find(item=>item.id===coordinatorId);if(coordinatorById){console.log(\"Found coordinator by direct ID comparison:\",coordinatorById.full_name);setCoordinator({value:coordinatorById.id,label:coordinatorById.full_name});}}}},[coordinators,coordinatorId]);const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const caseStepUpdate=useSelector(state=>state.updateCaseStep);const{loadingCaseStepUpdate,successCaseStepUpdate,errorCaseStepUpdate}=caseStepUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// Set loading state to true when starting to fetch data\nsetIsLoading(true);// Load all required data at once with optimized actions\ndispatch(coordinatorsListDashboard(\"0\"));dispatch(providersListEditCase(\"0\"));dispatch(insurancesListDashboard(\"0\"));dispatch(detailCase(id));// Set a maximum timeout for the loading indicator (30 seconds) as a fallback\nconst timeoutId=setTimeout(()=>{setIsLoading(false);console.log(\"Maximum loading time reached, hiding loading indicator\");},6000);// Clean up the timeout when the component unmounts\nreturn()=>clearTimeout(timeoutId);}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(successCaseUpdate){if(shouldNavigateToFinalStep){setStepSelect(5);setShouldNavigateToFinalStep(false);// Reset the flag\n}setIsLoading(false);}},[successCaseUpdate,shouldNavigateToFinalStep]);// Handle successful step update\nuseEffect(()=>{if(successCaseStepUpdate){setIsLoading(false);// Refresh case data to get updated information\ndispatch(detailCase(id));}},[successCaseStepUpdate,dispatch,id]);// Set loading state when case update is in progress\nuseEffect(()=>{if(loadingCaseUpdate||loadingCaseStepUpdate){setIsLoading(true);}},[loadingCaseUpdate,loadingCaseStepUpdate]);// Update loading state based on data loading status\nuseEffect(()=>{// Check if essential data is loaded\nif(!loadingProviders&&!loadingCaseInfo&&providers&&providers.length>0&&caseInfo){// Hide loading indicator as soon as we have the essential data\nsetIsLoading(false);}else if(loadingCaseUpdate||loadingCaseStepUpdate){// Show loading during case update\nsetIsLoading(true);}},[loadingProviders,loadingCaseInfo,loadingCaseUpdate,loadingCaseStepUpdate,providers,caseInfo]);useEffect(()=>{// Only proceed if caseInfo is available\nif(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$currency_pr,_caseInfo$price_tatal,_caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$case_status,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$start_date,_caseInfo$end_date,_caseInfo$case_type_i,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_n,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");const patientCountry=(_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}const patientCurrency=(_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){setCurrencyCode({value:foundCurrency.code,label:foundCurrency.name!==\"\"?foundCurrency.name+\" (\"+foundCurrency.code+\") \"||\"\":\"\"});}else{setCurrencyCode(\"\");}setIsPay(caseInfo.is_pay);setPriceTotal((_caseInfo$price_tatal=caseInfo.price_tatal)!==null&&_caseInfo$price_tatal!==void 0?_caseInfo$price_tatal:0);// Store coordinator ID for later use\nif(caseInfo.coordinator_user){var _caseInfo$coordinator,_caseInfo$coordinator2;const initialCoordinator=(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\";console.log(\"Setting coordinator ID from caseInfo:\",initialCoordinator);console.log(\"Coordinator user from caseInfo:\",caseInfo.coordinator_user);// Set coordinator ID with a slight delay to ensure it's properly updated\nsetTimeout(()=>{setCoordinatorId(initialCoordinator);console.log(\"CoordinatorId has been set to:\",initialCoordinator);},50);}setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nconst statuses=(caseInfo===null||caseInfo===void 0?void 0:(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map(status=>status===null||status===void 0?void 0:status.status_coordination))||[];// Default to an empty array if case_status is undefined or not an array\nsetCoordinatStatusList(statuses);//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setStartDate((_caseInfo$start_date=caseInfo.start_date)!==null&&_caseInfo$start_date!==void 0?_caseInfo$start_date:\"\");setEndDate((_caseInfo$end_date=caseInfo.end_date)!==null&&_caseInfo$end_date!==void 0?_caseInfo$end_date:\"\");setCaseTypeItem((_caseInfo$case_type_i=caseInfo.case_type_item)!==null&&_caseInfo$case_type_i!==void 0?_caseInfo$case_type_i:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;var initialProvider=(_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){setProviderName({value:foundProvider.id,label:foundProvider.full_name});}else{setProviderName(\"\");}}if(caseInfo.provider_services){var _caseInfo$provider_se;setProviderMultiSelectLast((_caseInfo$provider_se=caseInfo.provider_services)!==null&&_caseInfo$provider_se!==void 0?_caseInfo$provider_se:[]);}if(caseInfo.assistance_services){var _caseInfo$assistance_;setAssistanceMultiSelectLast((_caseInfo$assistance_=caseInfo.assistance_services)!==null&&_caseInfo$assistance_!==void 0?_caseInfo$assistance_:[]);}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;var initialInsurance=(_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\";var foundInsurance=insurances===null||insurances===void 0?void 0:insurances.find(item=>item.id===initialInsurance);if(foundInsurance){console.log(\"here 2\");setInsuranceCompany({value:foundInsurance.id,label:foundInsurance.assurance_name||\"\"});}else{console.log(\"here 3\");setInsuranceCompany({value:\"\",label:\"\"});}}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInsuranceNumber((_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);// Function to update only the current step\nconst handleUpdateCurrentStep=async()=>{try{let isValid=true;// Step-specific validation based on current step\nif(stepSelect===0){// Step 1: General Information validation\nsetFirstNameError(\"\");setLastNameError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCityError(\"\");setCountryError(\"\");setCaseDateError(\"\");setCaseTypeError(\"\");setCaseDescriptionError(\"\");setPriceTotalError(\"\");setCurrencyCodeError(\"\");setCoordinatorError(\"\");setBirthDateError(\"\");// Required field validations for Step 1\nif(!firstName||firstName.trim()===\"\"){setFirstNameError(\"First name is required.\");isValid=false;}if(!lastName||lastName.trim()===\"\"){setLastNameError(\"Last name is required.\");isValid=false;}if(!phone||phone.trim()===\"\"){setPhoneError(\"Phone number is required.\");isValid=false;}else{// Phone format validation\nconst phoneRegex=/^[\\+]?[1-9][\\d]{0,15}$/;if(!phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g,''))){setPhoneError(\"Please enter a valid phone number +212....\");isValid=false;}}if(email&&email.trim()!==\"\"){const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(email)){setEmailError(\"Please enter a valid email address.\");isValid=false;}}if(!address||address.trim()===\"\"){setAddressError(\"Address is required.\");isValid=false;}if(!city||city.trim()===\"\"){setCityError(\"City is required.\");isValid=false;}if(!country||!country.value||country.value===\"\"){setCountryError(\"Country is required.\");isValid=false;}if(!caseDate||caseDate.trim()===\"\"){setCaseDateError(\"Case date is required.\");isValid=false;}if(!caseType||caseType.trim()===\"\"){setCaseTypeError(\"Case type is required.\");isValid=false;}if(!caseDescription||caseDescription.trim()===\"\"){setCaseDescriptionError(\"Case description is required.\");isValid=false;}if(!coordinator||!coordinator.value||coordinator.value===\"\"){setCoordinatorError(\"Coordinator is required.\");isValid=false;}if(!currencyCode||!currencyCode.value||currencyCode.value===\"\"){setCurrencyCodeError(\"Currency is required.\");isValid=false;}if(!priceTotal||priceTotal===\"\"||priceTotal<0){setPriceTotalError(\"Price total is required and must be greater than 0.\");isValid=false;}// Optional field validations for Step 1\nif(birthDate&&birthDate.trim()!==\"\"){const birthDateObj=new Date(birthDate);const today=new Date();if(birthDateObj>today){setBirthDateError(\"Birth date cannot be in the future.\");isValid=false;}}}else if(stepSelect===1){// Step 2: Coordination Details validation\nsetCoordinatStatusListError(\"\");// Coordination status validation\nif(!coordinatStatusList||coordinatStatusList.length===0){setCoordinatStatusListError(\"At least one coordination status is required.\");isValid=false;}}else if(stepSelect===2){// Step 3: Medical Reports validation\n// No specific required validations for medical reports step\n// Files are optional\n}else if(stepSelect===3){// Step 4: Invoices validation\nsetInvoiceNumberError(\"\");setAmountError(\"\");// Optional field validations for Step 4\nif(invoiceNumber&&invoiceNumber.trim()!==\"\"){if(invoiceNumber.length<3){setInvoiceNumberError(\"Invoice number must be at least 3 characters.\");isValid=false;}}if(amount&&amount!==\"\"&&amount<0){setAmountError(\"Amount cannot be negative.\");isValid=false;}}else if(stepSelect===4){// Step 5: Insurance Authorization validation\nsetInsuranceNumberError(\"\");setPolicyNumberError(\"\");// Optional field validations for Step 5\nif(insuranceNumber&&insuranceNumber.trim()!==\"\"){if(insuranceNumber.length<3){setInsuranceNumberError(\"Insurance number must be at least 3 characters.\");isValid=false;}}if(policyNumber&&policyNumber.trim()!==\"\"){if(policyNumber.length<3){setPolicyNumberError(\"Policy number must be at least 3 characters.\");isValid=false;}}}// If validation fails, show error and return\nif(!isValid){toast.error(\"Please fix the validation errors before updating.\");return;}setIsLoading(true);// Create step-specific data\nconst formData=new FormData();formData.append('step',stepSelect.toString());if(stepSelect===0){var _coordinator$value,_currencyCode$value;// Step 1: General Information\nformData.append('first_name',firstName);formData.append('last_name',lastName);formData.append('full_name',firstName+\" \"+lastName);formData.append('birth_day',birthDate!==null&&birthDate!==void 0?birthDate:\"\");formData.append('patient_phone',phone);formData.append('patient_email',email);formData.append('patient_address',address);formData.append('patient_city',city);formData.append('patient_country',country.value);formData.append('coordinator',(_coordinator$value=coordinator.value)!==null&&_coordinator$value!==void 0?_coordinator$value:\"\");formData.append('case_date',caseDate);formData.append('case_type',caseType);formData.append('case_type_item',caseType===\"Medical\"?caseTypeItem:\"\");formData.append('case_description',caseDescription);formData.append('is_pay',isPay?\"True\":\"False\");formData.append('price_tatal',priceTotal);formData.append('currency_price',(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\");}else if(stepSelect===1){// Step 2: Coordination Details\ncoordinatStatusList.forEach(status=>{formData.append('case_status[]',status);});// Add assistance data\nassistanceMultiSelect.forEach((item,index)=>{formData.append(`assistances[${index}][start_date]`,item.start_date||\"\");formData.append(`assistances[${index}][end_date]`,item.end_date||\"\");formData.append(`assistances[${index}][appointment_date]`,item.appointment_date||\"\");formData.append(`assistances[${index}][service_location]`,item.service_location||\"\");item.provider_services.forEach((providerService,providerIndex)=>{var _providerService$prov,_providerService$prov2;formData.append(`assistances[${index}][provider_services][${providerIndex}][provider]`,((_providerService$prov=providerService.provider)===null||_providerService$prov===void 0?void 0:_providerService$prov.id)||\"\");formData.append(`assistances[${index}][provider_services][${providerIndex}][service]`,((_providerService$prov2=providerService.provider_service)===null||_providerService$prov2===void 0?void 0:_providerService$prov2.id)||\"\");formData.append(`assistances[${index}][provider_services][${providerIndex}][date]`,providerService.provider_date||\"\");});});}else if(stepSelect===2){// Step 3: Medical Reports\nfilesInitialMedicalReports.forEach(file=>{formData.append('initial_medical_reports[]',file);});fileDeleted.forEach(fileId=>{formData.append('files_deleted[]',fileId);});}else if(stepSelect===3){// Step 4: Invoices\nformData.append('invoice_number',invoiceNumber);formData.append('date_issued',dateIssued);formData.append('invoice_amount',amount);filesUploadInvoice.forEach(file=>{formData.append('upload_invoice[]',file);});}else if(stepSelect===4){var _insuranceCompany$val;// Step 5: Insurance Authorization\nformData.append('assurance',(_insuranceCompany$val=insuranceCompany.value)!==null&&_insuranceCompany$val!==void 0?_insuranceCompany$val:\"\");formData.append('assurance_number',insuranceNumber);formData.append('policy_number',policyNumber);formData.append('assurance_status',initialStatus);filesUploadAuthorizationDocuments.forEach(file=>{formData.append('upload_authorization_documents[]',file);});}// Use the new step-specific update action\nawait dispatch(updateCaseStep(id,formData));setIsLoading(false);}catch(error){setIsLoading(false);toast.error(\"Failed to update case step. Please try again.\");}};return/*#__PURE__*/_jsxs(DefaultLayout,{children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-700 font-medium\",children:\"Loading data...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:`flex flex-row mb-3 md:min-h-20 ${stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\"} md:items-start items-center`,children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${firstNameError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${emailError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${phoneError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);// Auto-update currency based on selected country\nif(option&&option.value){// The option.value contains the country title (name)\nconst countryName=option.value;const currencyCode=COUNTRY_CURRENCY_MAP[countryName];if(currencyCode){// Find the currency option in CURRENCYITEMS\nconst currencyOption=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(currency=>currency.code===currencyCode);if(currencyOption){setCurrencyCode({value:currencyOption.code,label:currencyOption.name!==\"\"?currencyOption.name+\" (\"+currencyOption.code+\")\":currencyOption.code});// Show success message\ntoast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);}}}},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"py-2\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:` outline-none border ${cityError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${caseDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:` outline-none border ${caseTypeError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),caseType===\"Medical\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type Item \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseTypeItem,onChange:v=>setCaseTypeItem(v.target.value),className:` outline-none border ${caseTypeItemError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type Item\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Outpatient\",children:\"Outpatient\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Inpatient\",children:\"Inpatient\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeItemError?caseTypeItemError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);console.log(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${priceTotalError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseStepUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseStepUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseTypeItemError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}else if(caseType===\"Medical\"&&caseTypeItem===\"\"){setCaseTypeItemError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordination-fee\")){setCoordinatStatusList([...coordinatStatusList,\"coordination-fee\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordination-fee\"));}},checked:coordinatStatusList.includes(\"coordination-fee\"),id:\"coordination-fee\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordination-fee\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordination Fee\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-payment\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-payment\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-payment\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-payment\"),id:\"coordinated-missing-payment\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-payment\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Payment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Add new Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-2 my-1 p-2 shadow rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col w-full \",children:caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Hospital Starting Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"date\",className:` outline-none border ${startDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\"Hospital Starting Date\",value:startDate,onChange:v=>{setStartDate(v.target.value);// If end date is earlier than new start date, update end date\nif(endDate&&endDate<v.target.value){setEndDate(v.target.value);}}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:startDateError?startDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Hospital Ending Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"date\",className:` outline-none border ${endDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\"Hospital Ending Date\",value:endDate,onChange:v=>setEndDate(v.target.value),disabled:!startDate,min:startDate}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:endDateError?endDateError:\"\"})]})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Appointment Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${appointmentDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:appointmentDateError?appointmentDateError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:` outline-none border ${serviceLocationError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceLocationError?serviceLocationError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Provider Name\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";// Show loading indicator while fetching provider services\nsetIsLoading(true);const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);// Hide loading indicator after services are loaded\nsetTimeout(()=>{setIsLoading(false);},100);}else{setProviderServices([]);setIsLoading(false);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>{var _option$label;return(_option$label=option.label)===null||_option$label===void 0?void 0:_option$label.toLowerCase().includes(inputValue===null||inputValue===void 0?void 0:inputValue.toLowerCase());},placeholder:\"Select Provider...\",isSearchable:true// Add loading indicator\n,isLoading:loadingProviders// Show loading indicator when menu opens\n,onMenuOpen:()=>{console.log(\"Provider dropdown opened\");},styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Provider Service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:`outline-none border ${providerServiceError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Visit Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${providerDateError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\" Visit Date\",value:providerDate,onChange:v=>setProviderDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerDateError?providerDateError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");setProviderDateError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\" Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\" Provider Service is required\");check=false;}if(providerDate===\"\"){setProviderDateError(\"These fields are required.\");toast.error(\" Visit Date is required\");check=false;}if(check){const exists=false;// const exists = providerMultiSelect.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.service?.id) ===\n//       String(providerService)\n// );\nconst existsLast=false;// const existsLast = providerMultiSelectLast.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.provider_service?.id) ===\n//       String(providerService)\n// );\nif(!exists&&!existsLast){var _providerName$value;// find provider\nvar initialProvider=(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService,date:providerDate}]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4,_itemProvider$date;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$date=itemProvider.date)!==null&&_itemProvider$date!==void 0?_itemProvider$date:\"---\"]})]})]},index);})]})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// Validate assistance fields\nlet isValid=true;setStartDateError(\"\");setEndDateError(\"\");setAppointmentDateError(\"\");setServiceLocationError(\"\");setProviderNameError(\"\");// Check if we have the required appointment date information\nif(caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"){// For inpatient, check start and end dates\nif(!startDate){setStartDateError(\"Hospital Starting Date is required\");toast.error(\"Hospital Starting Date is required\");isValid=false;}if(!endDate){setEndDateError(\"Hospital Ending Date is required\");toast.error(\"Hospital Ending Date is required\");isValid=false;}}else{// For outpatient, check appointment date\nif(!appointmentDate){setAppointmentDateError(\"Appointment Date is required\");toast.error(\"Appointment Date is required\");isValid=false;}}// Check service location\n// if (!serviceLocation) {\n//   setServiceLocationError(\n//     \"Service Location is required\"\n//   );\n//   toast.error(\"Service Location is required\");\n//   isValid = false;\n// }\n// Check if at least one provider is added\nif(providerMultiSelect.length===0){setProviderNameError(\"At least one provider must be added\");toast.error(\"At least one provider must be added\");isValid=false;}if(isValid){// Create new assistance object\nconst newAssistance={id:Date.now(),// Generate a temporary ID\nstart_date:startDate||null,end_date:endDate||null,appointment_date:appointmentDate||null,service_location:serviceLocation!==null&&serviceLocation!==void 0?serviceLocation:\"\",provider_services:providerMultiSelect.map(item=>({provider:item.provider,provider_service:item.service,provider_date:item.date}))};// Add to assistanceMultiSelect array\nsetAssistanceMultiSelect(prev=>[...prev,newAssistance]);// Also add to assistanceMultiSelectLast for display\n// setAssistanceMultiSelectLast(prev => [...prev, newAssistance]);\n// Clear all input fields\nsetStartDate(\"\");setEndDate(\"\");setAppointmentDate(\"\");setServiceLocation(\"\");setProviderMultiSelect([]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");toast.success(\"Assistance added successfully\");}},className:\"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-300 flex flex-row items-center justify-center py-2 px-4 rounded-md my-4 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"size-4 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Add New Assistance\"})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 0 0 2.25 2.25h.75m0-3.75h3.75M9 15h3.75M9 12h3.75m3-3h.008v.008h-.008V9Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Assistances\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pl-10\",children:[(assistanceMultiSelectLast===null||assistanceMultiSelectLast===void 0?void 0:assistanceMultiSelectLast.length)>0?assistanceMultiSelectLast.map((itemAssistance,indexAssistance)=>{var _itemAssistance$start,_itemAssistance$end_d,_itemAssistance$appoi,_itemAssistance$servi,_itemAssistance$provi;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-medium text-sm text-gray-800\",children:[\"Appointment #\",indexAssistance+1]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{// Implement delete functionality here\nconst updatedAssistances=assistanceMultiSelectLast.filter((_,index)=>index!==indexAssistance);setAssistanceMultiSelectDelete([...assistanceMultiSelectDelete,itemAssistance.id]);setAssistanceMultiSelectLast(updatedAssistances);},className:\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\",\"aria-label\":\"Delete assistance\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Appointment Info\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-10 space-y-2\",children:[caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Starting Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$start=itemAssistance.start_date)!==null&&_itemAssistance$start!==void 0?_itemAssistance$start:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Ending Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$end_d=itemAssistance.end_date)!==null&&_itemAssistance$end_d!==void 0?_itemAssistance$end_d:\"---\"})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Appointment Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$appoi=itemAssistance.appointment_date)!==null&&_itemAssistance$appoi!==void 0?_itemAssistance$appoi:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$servi=itemAssistance.service_location)!==null&&_itemAssistance$servi!==void 0?_itemAssistance$servi:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#7C3AED\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Providers\"})]}),((_itemAssistance$provi=itemAssistance.provider_services)===null||_itemAssistance$provi===void 0?void 0:_itemAssistance$provi.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 space-y-4\",children:itemAssistance.provider_services.map((itemProvider,idx)=>{var _itemProvider$provide3,_itemProvider$provide4,_itemProvider$provide5,_itemProvider$provide6,_itemProvider$provide7,_itemProvider$provide8,_itemProvider$provide9;return/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-md border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Provider\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide3=(_itemProvider$provide4=itemProvider.provider)===null||_itemProvider$provide4===void 0?void 0:_itemProvider$provide4.full_name)!==null&&_itemProvider$provide3!==void 0?_itemProvider$provide3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Service\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide5=(_itemProvider$provide6=itemProvider.provider_service)===null||_itemProvider$provide6===void 0?void 0:_itemProvider$provide6.service_type)!==null&&_itemProvider$provide5!==void 0?_itemProvider$provide5:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide7=(_itemProvider$provide8=itemProvider.provider_service)===null||_itemProvider$provide8===void 0?void 0:_itemProvider$provide8.service_specialist)!==null&&_itemProvider$provide7!==void 0?_itemProvider$provide7:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide9=itemProvider.provider_date)!==null&&_itemProvider$provide9!==void 0?_itemProvider$provide9:\"---\"})]})]})},idx);})}):/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\",children:\"No providers assigned\"})]})]})]},indexAssistance);}):/*#__PURE__*/_jsx(\"div\",{className:\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\",children:\"No assistances added yet\"}),(assistanceMultiSelect===null||assistanceMultiSelect===void 0?void 0:assistanceMultiSelect.length)>0?assistanceMultiSelect.map((itemAssistance,indexAssistance)=>{var _itemAssistance$start2,_itemAssistance$end_d2,_itemAssistance$appoi2,_itemAssistance$servi2,_itemAssistance$provi2;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-medium text-sm text-gray-800\",children:[\"Appointment #\",indexAssistance+(assistanceMultiSelectLast===null||assistanceMultiSelectLast===void 0?void 0:assistanceMultiSelectLast.length)+1]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=assistanceMultiSelect.filter((_,indexF)=>indexF!==indexAssistance);setAssistanceMultiSelect(updatedServices);// Implement delete functionality here\n// const updatedAssistances = assistanceMultiSelectLast.filter(\n//   (_, index) => index !== indexAssistance\n// );\n// setAssistanceMultiSelectDelete([\n//   ...assistanceMultiSelectDelete,\n//   itemAssistance.id,\n// ]);\n// setAssistanceMultiSelectLast(updatedAssistances);\n},className:\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\",\"aria-label\":\"Delete assistance\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Appointment Info\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-10 space-y-2\",children:[caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Starting Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$start2=itemAssistance.start_date)!==null&&_itemAssistance$start2!==void 0?_itemAssistance$start2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Ending Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$end_d2=itemAssistance.end_date)!==null&&_itemAssistance$end_d2!==void 0?_itemAssistance$end_d2:\"---\"})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Appointment Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$appoi2=itemAssistance.appointment_date)!==null&&_itemAssistance$appoi2!==void 0?_itemAssistance$appoi2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$servi2=itemAssistance.service_location)!==null&&_itemAssistance$servi2!==void 0?_itemAssistance$servi2:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#7C3AED\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Providers\"})]}),((_itemAssistance$provi2=itemAssistance.provider_services)===null||_itemAssistance$provi2===void 0?void 0:_itemAssistance$provi2.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 space-y-4\",children:itemAssistance.provider_services.map((itemProvider,idx)=>{var _itemProvider$provide10,_itemProvider$provide11,_itemProvider$provide12,_itemProvider$provide13,_itemProvider$provide14,_itemProvider$provide15,_itemProvider$provide16;return/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-md border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Provider\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide10=(_itemProvider$provide11=itemProvider.provider)===null||_itemProvider$provide11===void 0?void 0:_itemProvider$provide11.full_name)!==null&&_itemProvider$provide10!==void 0?_itemProvider$provide10:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Service\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide12=(_itemProvider$provide13=itemProvider.provider_service)===null||_itemProvider$provide13===void 0?void 0:_itemProvider$provide13.service_type)!==null&&_itemProvider$provide12!==void 0?_itemProvider$provide12:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide14=(_itemProvider$provide15=itemProvider.provider_service)===null||_itemProvider$provide15===void 0?void 0:_itemProvider$provide15.service_specialist)!==null&&_itemProvider$provide14!==void 0?_itemProvider$provide14:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide16=itemProvider.provider_date)!==null&&_itemProvider$provide16!==void 0?_itemProvider$provide16:\"---\"})]})]})},idx);})}):/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\",children:\"No providers assigned\"})]})]})]},indexAssistance);}):/*#__PURE__*/_jsx(\"div\",{className:\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\",children:\"No new assistances added yet\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseStepUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseStepUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");setCoordinatStatusListError(\"\");if(coordinatStatusList.length===0){toast.error(\"Initial Coordination Status empty or invalid. please try again\");setCoordinatStatusListError(\"Initial Coordination Status is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseStepUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseStepUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseStepUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseStepUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseStepUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseStepUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{var _coordinator$value2,_providerName$value2,_insuranceCompany$val2,_currencyCode$value2;// Show loading indicator while submitting the form\nsetIsLoading(true);setShouldNavigateToFinalStep(true);// Set flag to navigate to final step\n// Map assistance items with their provider services\nconst assistanceItems=assistanceMultiSelect.map(item=>({start_date:item.start_date,end_date:item.end_date,appointment_date:item.appointment_date,service_location:item.service_location,provider_services:item.provider_services.map(providerService=>{var _providerService$prov3,_providerService$prov4;return{provider:(_providerService$prov3=providerService.provider)===null||_providerService$prov3===void 0?void 0:_providerService$prov3.id,service:(_providerService$prov4=providerService.provider_service)===null||_providerService$prov4===void 0?void 0:_providerService$prov4.id,date:providerService.provider_date};})}));const providerItems=providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id,date:item.date};});// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value2=coordinator.value)!==null&&_coordinator$value2!==void 0?_coordinator$value2:\"\",case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:(_providerName$value2=providerName.value)!==null&&_providerName$value2!==void 0?_providerName$value2:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val2=insuranceCompany.value)!==null&&_insuranceCompany$val2!==void 0?_insuranceCompany$val2:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted,providers:providerItems!==null&&providerItems!==void 0?providerItems:[],assistances:assistanceItems!==null&&assistanceItems!==void 0?assistanceItems:[],providers_deleted:providerMultiSelectDelete!==null&&providerMultiSelectDelete!==void 0?providerMultiSelectDelete:[],assistance_deleted:assistanceMultiSelectDelete!==null&&assistanceMultiSelectDelete!==void 0?assistanceMultiSelectDelete:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value2=currencyCode.value)!==null&&_currencyCode$value2!==void 0?_currencyCode$value2:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Save & Complete\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})]});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "DefaultLayout", "addreactionface", "toast", "providersListEditCase", "addNewCase", "detailCase", "updateCase", "updateCaseStep", "LoadingSpinner", "GoogleComponent", "Select", "useDropzone", "insurancesListDashboard", "coordinatorsList<PERSON>ash<PERSON>", "COUNTRIES", "CURRENCYITEMS", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "COUNTRY_CURRENCY_MAP", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_parseInt", "navigate", "location", "dispatch", "id", "searchParams", "section", "get", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinatorId", "setCoordinatorId", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "assistanceMultiSelect", "setAssistanceMultiSelect", "assistanceMultiSelectDelete", "setAssistanceMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "assistanceMultiSelectLast", "setAssistanceMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "parseInt", "isLoading", "setIsLoading", "shouldNavigateToFinalStep", "setShouldNavigateToFinalStep", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "length", "console", "log", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "foundCoordinator", "find", "item", "String", "full_name", "setTimeout", "value", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "caseStepUpdate", "loadingCaseStepUpdate", "successCaseStepUpdate", "errorCaseStepUpdate", "redirect", "timeoutId", "clearTimeout", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$start_date", "_caseInfo$end_date", "_caseInfo$case_type_i", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "option", "className", "children", "icon", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "start_date", "end_date", "case_type_item", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "assistance_services", "_caseInfo$assistance_", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "handleUpdateCurrentStep", "<PERSON><PERSON><PERSON><PERSON>", "trim", "phoneRegex", "test", "replace", "emailRegex", "birthDate<PERSON><PERSON><PERSON>", "today", "error", "formData", "FormData", "append", "toString", "_coordinator$value", "_currencyCode$value", "providerIndex", "_providerService$prov", "_providerService$prov2", "provider_service", "provider_date", "fileId", "_insuranceCompany$val", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "countryName", "currencyOption", "currency", "success", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "min", "checked", "for", "rows", "disabled", "check", "filter", "_option$value", "_foundProvider$servic", "services", "_option$label", "onMenuOpen", "service", "_service$service_type", "service_type", "service_specialist", "exists", "existsLast", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "date", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "newAssistance", "now", "prev", "strokeWidth", "itemAssistance", "indexAssistance", "_itemAssistance$start", "_itemAssistance$end_d", "_itemAssistance$appoi", "_itemAssistance$servi", "_itemAssistance$provi", "updatedAssistances", "idx", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "_itemProvider$provide7", "_itemProvider$provide8", "_itemProvider$provide9", "_itemAssistance$start2", "_itemAssistance$end_d2", "_itemAssistance$appoi2", "_itemAssistance$servi2", "_itemAssistance$provi2", "_itemProvider$provide10", "_itemProvider$provide11", "_itemProvider$provide12", "_itemProvider$provide13", "_itemProvider$provide14", "_itemProvider$provide15", "_itemProvider$provide16", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "_coordinator$value2", "_providerName$value2", "_insuranceCompany$val2", "_currencyCode$value2", "assistanceItems", "_providerService$prov3", "_providerService$prov4", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "assistances", "providers_deleted", "assistance_deleted"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListEditCase } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n  updateCaseStep,\n} from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\n// Country to Currency mapping - using exact country names from COUNTRIES constant\nconst COUNTRY_CURRENCY_MAP = {\n  \"Morocco\": \"MAD\",\n  \"United States\": \"USD\",\n  \"Canada\": \"CAD\",\n  \"United Kingdom\": \"GBP\",\n  \"France\": \"EUR\",\n  \"Germany\": \"EUR\",\n  \"Spain\": \"EUR\",\n  \"Italy\": \"EUR\",\n  \"Netherlands\": \"EUR\",\n  \"Belgium\": \"EUR\",\n  \"Portugal\": \"EUR\",\n  \"Greece\": \"EUR\",\n  \"Austria\": \"EUR\",\n  \"Ireland\": \"EUR\",\n  \"Finland\": \"EUR\",\n  \"Luxembourg\": \"EUR\",\n  \"Estonia\": \"EUR\",\n  \"Slovenia\": \"EUR\",\n  \"Slovakia\": \"EUR\",\n  \"Malta\": \"EUR\",\n  \"Cyprus\": \"EUR\",\n  \"Lithuania\": \"EUR\",\n  \"Latvia\": \"EUR\",\n  \"Japan\": \"JPY\",\n  \"China\": \"CNY\",\n  \"India\": \"INR\",\n  \"Australia\": \"AUD\",\n  \"New Zealand\": \"NZD\",\n  \"South Africa\": \"ZAR\",\n  \"Brazil\": \"BRL\",\n  \"Mexico\": \"MXN\",\n  \"Argentina\": \"ARS\",\n  \"Chile\": \"CLP\",\n  \"Colombia\": \"COP\",\n  \"Peru\": \"PEN\",\n  \"Russia\": \"RUB\",\n  \"Turkey\": \"TRY\",\n  \"Egypt\": \"EGP\",\n  \"Saudi Arabia\": \"SAR\",\n  \"United Arab Emirates\": \"AED\",\n  \"Qatar\": \"QAR\",\n  \"Kuwait\": \"KWD\",\n  \"Bahrain\": \"BHD\",\n  \"Oman\": \"OMR\",\n  \"Jordan\": \"JOD\",\n  \"Lebanon\": \"LBP\",\n  \"Israel\": \"ILS\",\n  \"South Korea\": \"KRW\",\n  \"Thailand\": \"THB\",\n  \"Malaysia\": \"MYR\",\n  \"Singapore\": \"SGD\",\n  \"Indonesia\": \"IDR\",\n  \"Philippines\": \"PHP\",\n  \"Vietnam\": \"VND\",\n  \"Pakistan\": \"PKR\",\n  \"Bangladesh\": \"BDT\",\n  \"Sri Lanka\": \"LKR\",\n  \"Nepal\": \"NPR\",\n  \"Switzerland\": \"CHF\",\n  \"Norway\": \"NOK\",\n  \"Sweden\": \"SEK\",\n  \"Denmark\": \"DKK\",\n  \"Iceland\": \"ISK\",\n  \"Poland\": \"PLN\",\n  \"Czech Republic\": \"CZK\",\n  \"Hungary\": \"HUF\",\n  \"Romania\": \"RON\",\n  \"Bulgaria\": \"BGN\",\n  \"Croatia\": \"HRK\",\n  \"Serbia\": \"RSD\",\n  \"Ukraine\": \"UAH\",\n  \"Belarus\": \"BYN\",\n  \"Algeria\": \"DZD\",\n  \"Tunisia\": \"TND\",\n  \"Libya\": \"LYD\",\n  \"Sudan\": \"SDG\",\n  \"Ethiopia\": \"ETB\",\n  \"Kenya\": \"KES\",\n  \"Uganda\": \"UGX\",\n  \"Tanzania\": \"TZS\",\n  \"Rwanda\": \"RWF\",\n  \"Ghana\": \"GHS\",\n  \"Nigeria\": \"NGN\",\n  \"Senegal\": \"XOF\",\n  \"Ivory Coast\": \"XOF\",\n  \"Mali\": \"XOF\",\n  \"Burkina Faso\": \"XOF\",\n  \"Niger\": \"XOF\",\n  \"Guinea\": \"GNF\",\n  \"Sierra Leone\": \"SLL\",\n  \"Liberia\": \"LRD\",\n  \"Cameroon\": \"XAF\",\n  \"Chad\": \"XAF\",\n  \"Central African Republic\": \"XAF\",\n  \"Democratic Republic of the Congo\": \"CDF\",\n  \"Republic of the Congo\": \"XAF\",\n  \"Gabon\": \"XAF\",\n  \"Angola\": \"AOA\",\n  \"Zambia\": \"ZMK\",\n  \"Zimbabwe\": \"ZWL\",\n  \"Botswana\": \"BWP\",\n  \"Namibia\": \"NAD\",\n  \"Lesotho\": \"LSL\",\n  \"Swaziland\": \"SZL\",\n  \"Mozambique\": \"MZN\",\n  \"Madagascar\": \"MGA\",\n  \"Mauritius\": \"MUR\",\n  \"Seychelles\": \"SCR\",\n  \"Afghanistan\": \"AFN\",\n  \"Albania\": \"ALL\",\n  \"Armenia\": \"AMD\",\n  \"Azerbaijan\": \"AZN\",\n  \"Brunei\": \"BND\",\n  \"Cambodia\": \"KHR\",\n  \"Cape Verde\": \"CVE\",\n  \"Comoros\": \"KMF\",\n  \"Costa Rica\": \"CRC\",\n  \"Cuba\": \"CUP\",\n  \"Dominican Republic\": \"DOP\",\n  \"Ecuador\": \"USD\",\n  \"El Salvador\": \"USD\",\n  \"Eritrea\": \"ERN\",\n  \"Fiji\": \"FJD\",\n  \"Georgia\": \"GEL\",\n  \"Guatemala\": \"GTQ\",\n  \"Guinea-Bissau\": \"XOF\",\n  \"Guyana\": \"GYD\",\n  \"Haiti\": \"HTG\",\n  \"Honduras\": \"HNL\",\n  \"Hong Kong\": \"HKD\",\n  \"Iran\": \"IRR\",\n  \"Iraq\": \"IQD\",\n  \"Jamaica\": \"JMD\",\n  \"Kazakhstan\": \"KZT\",\n  \"Kyrgyzstan\": \"KGS\",\n  \"Laos\": \"LAK\",\n  \"Macau\": \"MOP\",\n  \"Macedonia\": \"MKD\",\n  \"Malawi\": \"MWK\",\n  \"Maldives\": \"MVR\",\n  \"Marshall Islands\": \"USD\",\n  \"Mauritania\": \"MRU\",\n  \"Micronesia\": \"USD\",\n  \"Moldova\": \"MDL\",\n  \"Monaco\": \"EUR\",\n  \"Mongolia\": \"MNT\",\n  \"Montenegro\": \"EUR\",\n  \"Myanmar\": \"MMK\",\n  \"Nicaragua\": \"NIO\",\n  \"North Korea\": \"KPW\",\n  \"Panama\": \"PAB\",\n  \"Papua New Guinea\": \"PGK\",\n  \"Paraguay\": \"PYG\",\n  \"Puerto Rico\": \"USD\",\n  \"Samoa\": \"WST\",\n  \"San Marino\": \"EUR\",\n  \"Sao Tome and Principe\": \"STN\",\n  \"Somalia\": \"SOS\",\n  \"South Sudan\": \"SSP\",\n  \"Suriname\": \"SRD\",\n  \"Syria\": \"SYP\",\n  \"Taiwan\": \"TWD\",\n  \"Tajikistan\": \"TJS\",\n  \"Togo\": \"XOF\",\n  \"Tonga\": \"TOP\",\n  \"Trinidad and Tobago\": \"TTD\",\n  \"Turkmenistan\": \"TMT\",\n  \"Tuvalu\": \"AUD\",\n  \"Uruguay\": \"UYU\",\n  \"Uzbekistan\": \"UZS\",\n  \"Vanuatu\": \"VUV\",\n  \"Venezuela\": \"VES\",\n  \"Western Sahara\": \"MAD\",\n  \"Yemen\": \"YER\"\n};\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [assistanceMultiSelect, setAssistanceMultiSelect] = useState([]);\n  const [assistanceMultiSelectDelete, setAssistanceMultiSelectDelete] =\n    useState([]);\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n  const [assistanceMultiSelectLast, setAssistanceMultiSelectLast] = useState(\n    []\n  );\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [shouldNavigateToFinalStep, setShouldNavigateToFinalStep] = useState(false);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(\n        (item) => String(item.id) === String(coordinatorId)\n      );\n\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(\n          (item) => item.id === coordinatorId\n        );\n        if (coordinatorById) {\n          console.log(\n            \"Found coordinator by direct ID comparison:\",\n            coordinatorById.full_name\n          );\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name,\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const caseStepUpdate = useSelector((state) => state.updateCaseStep);\n  const { loadingCaseStepUpdate, successCaseStepUpdate, errorCaseStepUpdate } = caseStepUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load all required data at once with optimized actions\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListEditCase(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      dispatch(detailCase(id));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 6000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      if (shouldNavigateToFinalStep) {\n        setStepSelect(5);\n        setShouldNavigateToFinalStep(false); // Reset the flag\n      }\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate, shouldNavigateToFinalStep]);\n\n  // Handle successful step update\n  useEffect(() => {\n    if (successCaseStepUpdate) {\n      setIsLoading(false);\n      // Refresh case data to get updated information\n      dispatch(detailCase(id));\n    }\n  }, [successCaseStepUpdate, dispatch, id]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate || loadingCaseStepUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate, loadingCaseStepUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingCaseInfo &&\n      providers &&\n      providers.length > 0 &&\n      caseInfo\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate || loadingCaseStepUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [\n    loadingProviders,\n    loadingCaseInfo,\n    loadingCaseUpdate,\n    loadingCaseStepUpdate,\n    providers,\n    caseInfo,\n  ]);\n\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        const initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        console.log(\n          \"Setting coordinator ID from caseInfo:\",\n          initialCoordinator\n        );\n        console.log(\n          \"Coordinator user from caseInfo:\",\n          caseInfo.coordinator_user\n        );\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setStartDate(caseInfo.start_date ?? \"\");\n      setEndDate(caseInfo.end_date ?? \"\");\n      setCaseTypeItem(caseInfo.case_type_item ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n\n      if (caseInfo.assistance_services) {\n        setAssistanceMultiSelectLast(caseInfo.assistance_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  // Function to update only the current step\n  const handleUpdateCurrentStep = async () => {\n    try {\n      let isValid = true;\n\n      // Step-specific validation based on current step\n      if (stepSelect === 0) {\n        // Step 1: General Information validation\n        setFirstNameError(\"\");\n        setLastNameError(\"\");\n        setPhoneError(\"\");\n        setEmailError(\"\");\n        setAddressError(\"\");\n        setCityError(\"\");\n        setCountryError(\"\");\n        setCaseDateError(\"\");\n        setCaseTypeError(\"\");\n        setCaseDescriptionError(\"\");\n        setPriceTotalError(\"\");\n        setCurrencyCodeError(\"\");\n        setCoordinatorError(\"\");\n        setBirthDateError(\"\");\n\n        // Required field validations for Step 1\n        if (!firstName || firstName.trim() === \"\") {\n          setFirstNameError(\"First name is required.\");\n          isValid = false;\n        }\n\n        if (!lastName || lastName.trim() === \"\") {\n          setLastNameError(\"Last name is required.\");\n          isValid = false;\n        }\n\n        if (!phone || phone.trim() === \"\") {\n          setPhoneError(\"Phone number is required.\");\n          isValid = false;\n        } else {\n          // Phone format validation\n          const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n          if (!phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n            setPhoneError(\"Please enter a valid phone number +212....\");\n            isValid = false;\n          }\n        }\n\n        if (email && email.trim() !== \"\"){\n          const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n          if (!emailRegex.test(email)) {\n            setEmailError(\"Please enter a valid email address.\");\n            isValid = false;\n          }\n        }\n\n        if (!address || address.trim() === \"\") {\n          setAddressError(\"Address is required.\");\n          isValid = false;\n        }\n\n        if (!city || city.trim() === \"\") {\n          setCityError(\"City is required.\");\n          isValid = false;\n        }\n\n        if (!country || !country.value || country.value === \"\") {\n          setCountryError(\"Country is required.\");\n          isValid = false;\n        }\n\n        if (!caseDate || caseDate.trim() === \"\") {\n          setCaseDateError(\"Case date is required.\");\n          isValid = false;\n        }\n\n        if (!caseType || caseType.trim() === \"\") {\n          setCaseTypeError(\"Case type is required.\");\n          isValid = false;\n        }\n\n        if (!caseDescription || caseDescription.trim() === \"\") {\n          setCaseDescriptionError(\"Case description is required.\");\n          isValid = false;\n        }\n\n        if (!coordinator || !coordinator.value || coordinator.value === \"\") {\n          setCoordinatorError(\"Coordinator is required.\");\n          isValid = false;\n        }\n\n        if (!currencyCode || !currencyCode.value || currencyCode.value === \"\") {\n          setCurrencyCodeError(\"Currency is required.\");\n          isValid = false;\n        }\n\n        if (!priceTotal || priceTotal === \"\" || priceTotal < 0) {\n          setPriceTotalError(\"Price total is required and must be greater than 0.\");\n          isValid = false;\n        }\n\n        // Optional field validations for Step 1\n        if (birthDate && birthDate.trim() !== \"\") {\n          const birthDateObj = new Date(birthDate);\n          const today = new Date();\n          if (birthDateObj > today) {\n            setBirthDateError(\"Birth date cannot be in the future.\");\n            isValid = false;\n          }\n        }\n\n      } else if (stepSelect === 1) {\n        // Step 2: Coordination Details validation\n        setCoordinatStatusListError(\"\");\n\n        // Coordination status validation\n        if (!coordinatStatusList || coordinatStatusList.length === 0) {\n          setCoordinatStatusListError(\"At least one coordination status is required.\");\n          isValid = false;\n        }\n\n      } else if (stepSelect === 2) {\n        // Step 3: Medical Reports validation\n        // No specific required validations for medical reports step\n        // Files are optional\n\n      } else if (stepSelect === 3) {\n        // Step 4: Invoices validation\n        setInvoiceNumberError(\"\");\n        setAmountError(\"\");\n\n        // Optional field validations for Step 4\n        if (invoiceNumber && invoiceNumber.trim() !== \"\") {\n          if (invoiceNumber.length < 3) {\n            setInvoiceNumberError(\"Invoice number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n\n        if (amount && amount !== \"\" && amount < 0) {\n          setAmountError(\"Amount cannot be negative.\");\n          isValid = false;\n        }\n\n      } else if (stepSelect === 4) {\n        // Step 5: Insurance Authorization validation\n        setInsuranceNumberError(\"\");\n        setPolicyNumberError(\"\");\n\n        // Optional field validations for Step 5\n        if (insuranceNumber && insuranceNumber.trim() !== \"\") {\n          if (insuranceNumber.length < 3) {\n            setInsuranceNumberError(\"Insurance number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n\n        if (policyNumber && policyNumber.trim() !== \"\") {\n          if (policyNumber.length < 3) {\n            setPolicyNumberError(\"Policy number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n      }\n\n      // If validation fails, show error and return\n      if (!isValid) {\n        toast.error(\"Please fix the validation errors before updating.\");\n        return;\n      }\n\n      setIsLoading(true);\n\n      // Create step-specific data\n      const formData = new FormData();\n      formData.append('step', stepSelect.toString());\n\n      if (stepSelect === 0) {\n        // Step 1: General Information\n        formData.append('first_name', firstName);\n        formData.append('last_name', lastName);\n        formData.append('full_name', firstName + \" \" + lastName);\n        formData.append('birth_day', birthDate ?? \"\");\n        formData.append('patient_phone', phone);\n        formData.append('patient_email', email);\n        formData.append('patient_address', address);\n        formData.append('patient_city', city);\n        formData.append('patient_country', country.value);\n        formData.append('coordinator', coordinator.value ?? \"\");\n        formData.append('case_date', caseDate);\n        formData.append('case_type', caseType);\n        formData.append('case_type_item', caseType === \"Medical\" ? caseTypeItem : \"\");\n        formData.append('case_description', caseDescription);\n        formData.append('is_pay', isPay ? \"True\" : \"False\");\n        formData.append('price_tatal', priceTotal);\n        formData.append('currency_price', currencyCode.value ?? \"\");\n\n      } else if (stepSelect === 1) {\n        // Step 2: Coordination Details\n        coordinatStatusList.forEach(status => {\n          formData.append('case_status[]', status);\n        });\n\n        // Add assistance data\n        assistanceMultiSelect.forEach((item, index) => {\n          formData.append(`assistances[${index}][start_date]`, item.start_date || \"\");\n          formData.append(`assistances[${index}][end_date]`, item.end_date || \"\");\n          formData.append(`assistances[${index}][appointment_date]`, item.appointment_date || \"\");\n          formData.append(`assistances[${index}][service_location]`, item.service_location || \"\");\n\n          item.provider_services.forEach((providerService, providerIndex) => {\n            formData.append(`assistances[${index}][provider_services][${providerIndex}][provider]`, providerService.provider?.id || \"\");\n            formData.append(`assistances[${index}][provider_services][${providerIndex}][service]`, providerService.provider_service?.id || \"\");\n            formData.append(`assistances[${index}][provider_services][${providerIndex}][date]`, providerService.provider_date || \"\");\n          });\n        });\n\n      } else if (stepSelect === 2) {\n        // Step 3: Medical Reports\n        filesInitialMedicalReports.forEach(file => {\n          formData.append('initial_medical_reports[]', file);\n        });\n\n        fileDeleted.forEach(fileId => {\n          formData.append('files_deleted[]', fileId);\n        });\n\n      } else if (stepSelect === 3) {\n        // Step 4: Invoices\n        formData.append('invoice_number', invoiceNumber);\n        formData.append('date_issued', dateIssued);\n        formData.append('invoice_amount', amount);\n\n        filesUploadInvoice.forEach(file => {\n          formData.append('upload_invoice[]', file);\n        });\n\n      } else if (stepSelect === 4) {\n        // Step 5: Insurance Authorization\n        formData.append('assurance', insuranceCompany.value ?? \"\");\n        formData.append('assurance_number', insuranceNumber);\n        formData.append('policy_number', policyNumber);\n        formData.append('assurance_status', initialStatus);\n\n        filesUploadAuthorizationDocuments.forEach(file => {\n          formData.append('upload_authorization_documents[]', file);\n        });\n      }\n\n      // Use the new step-specific update action\n      await dispatch(updateCaseStep(id, formData));\n\n      setIsLoading(false);\n    } catch (error) {\n      setIsLoading(false);\n      toast.error(\"Failed to update case step. Please try again.\");\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n\n                              // Auto-update currency based on selected country\n                              if (option && option.value) {\n                                // The option.value contains the country title (name)\n                                const countryName = option.value;\n                                const currencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                if (currencyCode) {\n                                  // Find the currency option in CURRENCYITEMS\n                                  const currencyOption = CURRENCYITEMS?.find(\n                                    (currency) => currency.code === currencyCode\n                                  );\n\n                                  if (currencyOption) {\n                                    setCurrencyCode({\n                                      value: currencyOption.code,\n                                      label: currencyOption.name !== \"\"\n                                        ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                        : currencyOption.code\n                                    });\n\n                                    // Show success message\n                                    toast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);\n                                  }\n                                }\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                              console.log(option);\n                              \n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseStepUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseStepUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    {/* form add new assistance */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Add new Assistance Details:\n                    </div>\n                    <div className=\"mx-2 my-1 p-2 shadow rounded\">\n                      {/* Appointment Details: */}\n                      <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                        Appointment Details:\n                      </div>\n                      <div className=\"flex md:flex-row flex-col w-full \">\n                        {caseType === \"Medical\" &&\n                        caseTypeItem === \"Inpatient\" ? (\n                          <div className=\"flex md:flex-row flex-col w-full\">\n                            <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                                Hospital Starting Date{\" \"}\n                                <strong className=\"text-danger\">*</strong>\n                              </div>\n                              <div>\n                                <input\n                                  type=\"date\"\n                                  className={` outline-none border ${\n                                    startDateError\n                                      ? \"border-danger\"\n                                      : \"border-[#F1F3FF]\"\n                                  } px-3 py-2 w-full rounded text-sm`}\n                                  placeholder=\"Hospital Starting Date\"\n                                  value={startDate}\n                                  onChange={(v) => {\n                                    setStartDate(v.target.value);\n                                    // If end date is earlier than new start date, update end date\n                                    if (endDate && endDate < v.target.value) {\n                                      setEndDate(v.target.value);\n                                    }\n                                  }}\n                                />\n                                <div className=\" text-[8px] text-danger\">\n                                  {startDateError ? startDateError : \"\"}\n                                </div>\n                              </div>\n                            </div>\n                            <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                                Hospital Ending Date{\" \"}\n                                <strong className=\"text-danger\">*</strong>\n                              </div>\n                              <div>\n                                <input\n                                  type=\"date\"\n                                  className={` outline-none border ${\n                                    endDateError\n                                      ? \"border-danger\"\n                                      : \"border-[#F1F3FF]\"\n                                  } px-3 py-2 w-full rounded text-sm`}\n                                  placeholder=\"Hospital Ending Date\"\n                                  value={endDate}\n                                  onChange={(v) => setEndDate(v.target.value)}\n                                  disabled={!startDate}\n                                  min={startDate}\n                                />\n                                <div className=\" text-[8px] text-danger\">\n                                  {endDateError ? endDateError : \"\"}\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ) : (\n                          <div className=\" w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Appointment Date{\" \"}\n                              <strong className=\"text-danger\">*</strong>\n                            </div>\n                            <div>\n                              <input\n                                className={` outline-none border ${\n                                  appointmentDateError\n                                    ? \"border-danger\"\n                                    : \"border-[#F1F3FF]\"\n                                } px-3 py-2 w-full rounded text-sm`}\n                                type=\"date\"\n                                placeholder=\"Appointment Date\"\n                                value={appointmentDate}\n                                onChange={(v) =>\n                                  setAppointmentDate(v.target.value)\n                                }\n                              />\n                              <div className=\" text-[8px] text-danger\">\n                                {appointmentDateError\n                                  ? appointmentDateError\n                                  : \"\"}\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"flex md:flex-row flex-col  \">\n                        {/*  */}\n                        <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Service Location\n                          </div>\n                          <div>\n                            <input\n                              type=\"text\"\n                              className={` outline-none border ${\n                                serviceLocationError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              } px-3 py-2 w-full rounded text-sm`}\n                              placeholder=\" Service Location\"\n                              value={serviceLocation}\n                              onChange={(v) =>\n                                setServiceLocation(v.target.value)\n                              }\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {serviceLocationError ? serviceLocationError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Provider Information: */}\n                      <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Provider Information:\n                      </div>\n                      <div className=\"flex md:flex-row flex-col  \">\n                        <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Provider Name{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <Select\n                              value={providerName}\n                              onChange={(option) => {\n                                setProviderName(option);\n                                //\n                                var initialProvider = option?.value ?? \"\";\n                                // Show loading indicator while fetching provider services\n                                setIsLoading(true);\n\n                                const foundProvider = providers?.find(\n                                  (item) => item.id === initialProvider\n                                );\n                                if (foundProvider) {\n                                  setProviderServices(\n                                    foundProvider.services ?? []\n                                  );\n                                  // Hide loading indicator after services are loaded\n                                  setTimeout(() => {\n                                    setIsLoading(false);\n                                  }, 100);\n                                } else {\n                                  setProviderServices([]);\n                                  setIsLoading(false);\n                                }\n                              }}\n                              className=\"text-sm\"\n                              options={providers?.map((item) => ({\n                                value: item.id,\n                                label: item.full_name || \"\",\n                              }))}\n                              filterOption={(option, inputValue) =>\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(inputValue?.toLowerCase())\n                              }\n                              placeholder=\"Select Provider...\"\n                              isSearchable\n                              // Add loading indicator\n                              isLoading={loadingProviders}\n                              // Show loading indicator when menu opens\n                              onMenuOpen={() => {\n                                console.log(\"Provider dropdown opened\");\n                              }}\n                              styles={{\n                                control: (base, state) => ({\n                                  ...base,\n                                  background: \"#fff\",\n                                  border: providerNameError\n                                    ? \"1px solid #d34053\"\n                                    : \"1px solid #F1F3FF\",\n                                  boxShadow: state.isFocused ? \"none\" : \"none\",\n                                  \"&:hover\": {\n                                    border: \"1px solid #F1F3FF\",\n                                  },\n                                }),\n                                option: (base) => ({\n                                  ...base,\n                                  display: \"flex\",\n                                  alignItems: \"center\",\n                                }),\n                                singleValue: (base) => ({\n                                  ...base,\n                                  display: \"flex\",\n                                  alignItems: \"center\",\n                                }),\n                              }}\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {providerNameError ? providerNameError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                        {/*  */}\n                        <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Provider Service{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <select\n                              className={`outline-none border ${\n                                providerServiceError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              }  px-3 py-2 w-full rounded text-sm`}\n                              onChange={(v) => {\n                                setProviderService(v.target.value);\n                              }}\n                              value={providerService}\n                            >\n                              <option value={\"\"}></option>\n                              {providerServices?.map((service, index) => (\n                                <option value={service.id}>\n                                  {service.service_type ?? \"\"}\n                                  {service.service_specialist !== \"\"\n                                    ? \" : \" + service.service_specialist\n                                    : \"\"}\n                                </option>\n                              ))}\n                            </select>\n                            <div className=\" text-[8px] text-danger\">\n                              {providerServiceError ? providerServiceError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex md:flex-row flex-col  \">\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Visit Date{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <input\n                              className={`outline-none border ${\n                                providerDateError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              }  px-3 py-2 w-full rounded text-sm`}\n                              type=\"date\"\n                              placeholder=\" Visit Date\"\n                              value={providerDate}\n                              onChange={(v) => setProviderDate(v.target.value)}\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {providerDateError ? providerDateError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {/* add  */}\n                      <div className=\"flex flex-col  \">\n                        <button\n                          onClick={() => {\n                            // providerMultiSelect\n                            var check = true;\n                            setProviderNameError(\"\");\n                            setProviderServiceError(\"\");\n                            setProviderDateError(\"\");\n                            if (\n                              providerName === \"\" ||\n                              providerName.value === \"\"\n                            ) {\n                              setProviderNameError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Provider is required\");\n                              check = false;\n                            }\n                            if (providerService === \"\") {\n                              setProviderServiceError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Provider Service is required\");\n                              check = false;\n                            }\n                            if (providerDate === \"\") {\n                              setProviderDateError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Visit Date is required\");\n                              check = false;\n                            }\n\n                            if (check) {\n                              const exists = false;\n                              // const exists = providerMultiSelect.some(\n                              //   (provider) =>\n                              //     String(provider?.provider?.id) ===\n                              //       String(providerName.value) &&\n                              //     String(provider?.service?.id) ===\n                              //       String(providerService)\n                              // );\n                              const existsLast = false;\n\n                              // const existsLast = providerMultiSelectLast.some(\n                              //   (provider) =>\n                              //     String(provider?.provider?.id) ===\n                              //       String(providerName.value) &&\n                              //     String(provider?.provider_service?.id) ===\n                              //       String(providerService)\n                              // );\n\n                              if (!exists && !existsLast) {\n                                // find provider\n                                var initialProvider = providerName.value ?? \"\";\n                                const foundProvider = providers?.find(\n                                  (item) =>\n                                    String(item.id) === String(initialProvider)\n                                );\n                                \n\n                                if (foundProvider) {\n                                  // found service\n                                  var initialService = providerService ?? \"\";\n\n                                  foundProvider?.services?.forEach(\n                                    (element) => {\n                                      console.log(element.id);\n                                    }\n                                  );\n\n                                  const foundService =\n                                    foundProvider?.services?.find(\n                                      (item) =>\n                                        String(item.id) ===\n                                        String(initialService)\n                                    );\n\n                                  if (foundService) {\n                                    // Add the new item if it doesn't exist\n                                    setProviderMultiSelect([\n                                      ...providerMultiSelect,\n                                      {\n                                        provider: foundProvider,\n                                        service: foundService,\n                                        date: providerDate,\n                                      },\n                                    ]);\n                                    setProviderName(\"\");\n                                    setProviderService(\"\");\n                                    setProviderDate(\"\");\n                                    console.log(providerMultiSelect);\n                                  } else {\n                                    setProviderNameError(\n                                      \"This provider service not exist!\"\n                                    );\n                                    toast.error(\n                                      \"This provider service not exist!\"\n                                    );\n                                  }\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider not exist!\"\n                                  );\n                                  toast.error(\"This provider not exist!\");\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider or service is already added!\"\n                                );\n                                toast.error(\n                                  \"This provider or service is already added!\"\n                                );\n                              }\n                            }\n                          }}\n                          className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                            />\n                          </svg>\n                          <span> Add Provider </span>\n                        </button>\n                        {/* providers list added */}\n                        {providerMultiSelect?.map((itemProvider, index) => (\n                          <div\n                            key={index}\n                            className=\"flex flex-row items-center my-1\"\n                          >\n                            <div className=\"min-w-6 text-center\">\n                              <button\n                                onClick={() => {\n                                  const updatedServices =\n                                    providerMultiSelect.filter(\n                                      (_, indexF) => indexF !== index\n                                    );\n                                  setProviderMultiSelect(updatedServices);\n                                }}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-6\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                            <div className=\"flex-1 mx-1 border-l px-1\">\n                              <div>\n                                <b>Provider:</b>{\" \"}\n                                {itemProvider.provider?.full_name ?? \"---\"}\n                              </div>\n                              <div>\n                                <b>Service:</b>{\" \"}\n                                {itemProvider.service?.service_type ?? \"--\"}\n                              </div>\n                              <div>\n                                <b>Speciality:</b>{\" \"}\n                                {itemProvider.service?.service_specialist ??\n                                  \"---\"}\n                              </div>\n                              <div>\n                                <b>Date:</b> {itemProvider.date ?? \"---\"}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                        {/* end providers list added */}\n                      </div>\n                    </div>\n                    <div>\n                      <button\n                        onClick={() => {\n                          // Validate assistance fields\n                          let isValid = true;\n                          setStartDateError(\"\");\n                          setEndDateError(\"\");\n                          setAppointmentDateError(\"\");\n                          setServiceLocationError(\"\");\n                          setProviderNameError(\"\");\n                          // Check if we have the required appointment date information\n                          if (\n                            caseType === \"Medical\" &&\n                            caseTypeItem === \"Inpatient\"\n                          ) {\n                            // For inpatient, check start and end dates\n                            if (!startDate) {\n                              setStartDateError(\n                                \"Hospital Starting Date is required\"\n                              );\n                              toast.error(\"Hospital Starting Date is required\");\n                              isValid = false;\n                            }\n                            if (!endDate) {\n                              setEndDateError(\n                                \"Hospital Ending Date is required\"\n                              );\n                              toast.error(\"Hospital Ending Date is required\");\n                              isValid = false;\n                            }\n                          } else {\n                            // For outpatient, check appointment date\n                            if (!appointmentDate) {\n                              setAppointmentDateError(\n                                \"Appointment Date is required\"\n                              );\n                              toast.error(\"Appointment Date is required\");\n                              isValid = false;\n                            }\n                          }\n\n                          // Check service location\n                          // if (!serviceLocation) {\n                          //   setServiceLocationError(\n                          //     \"Service Location is required\"\n                          //   );\n                          //   toast.error(\"Service Location is required\");\n                          //   isValid = false;\n                          // }\n\n                          // Check if at least one provider is added\n                          if (providerMultiSelect.length === 0) {\n                            setProviderNameError(\n                              \"At least one provider must be added\"\n                            );\n                            toast.error(\"At least one provider must be added\");\n                            isValid = false;\n                          }\n\n                          if (isValid) {\n                            // Create new assistance object\n                            const newAssistance = {\n                              id: Date.now(), // Generate a temporary ID\n                              start_date: startDate || null,\n                              end_date: endDate || null,\n                              appointment_date: appointmentDate || null,\n                              service_location: serviceLocation??\"\",\n                              provider_services: providerMultiSelect.map(\n                                (item) => ({\n                                  provider: item.provider,\n                                  provider_service: item.service,\n                                  provider_date: item.date,\n                                })\n                              ),\n                            };\n\n                            // Add to assistanceMultiSelect array\n                            setAssistanceMultiSelect((prev) => [\n                              ...prev,\n                              newAssistance,\n                            ]);\n\n                            // Also add to assistanceMultiSelectLast for display\n                            // setAssistanceMultiSelectLast(prev => [...prev, newAssistance]);\n\n                            // Clear all input fields\n                            setStartDate(\"\");\n                            setEndDate(\"\");\n                            setAppointmentDate(\"\");\n                            setServiceLocation(\"\");\n                            setProviderMultiSelect([]);\n                            setProviderName(\"\");\n                            setProviderService(\"\");\n                            setProviderDate(\"\");\n\n                            toast.success(\"Assistance added successfully\");\n                          }\n                        }}\n                        className=\"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-300 flex flex-row items-center justify-center py-2 px-4 rounded-md my-4 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          strokeWidth=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-4 mr-2\"\n                        >\n                          <path\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span>Add New Assistance</span>\n                      </button>\n                    </div>\n                    {/* end form add new assistance */}\n                    <div>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"flex items-center mb-3\">\n                          <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              strokeWidth={1.5}\n                              stroke=\"#3C50E0\"\n                              className=\"w-4 h-4\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 0 0 2.25 2.25h.75m0-3.75h3.75M9 15h3.75M9 12h3.75m3-3h.008v.008h-.008V9Z\"\n                              />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-sm font-semibold text-gray-800\">\n                            Assistances\n                          </h4>\n                        </div>\n\n                        <div className=\"pl-10\">\n                          {/* last data */}\n                          {assistanceMultiSelectLast?.length > 0 ? (\n                            assistanceMultiSelectLast.map(\n                              (itemAssistance, indexAssistance) => (\n                                <div\n                                  key={indexAssistance}\n                                  className=\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\"\n                                >\n                                  {/* Card Header */}\n                                  <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\">\n                                    <h3 className=\"font-medium text-sm text-gray-800\">\n                                      Appointment #{indexAssistance + 1}\n                                    </h3>\n                                    <button\n                                      onClick={() => {\n                                        // Implement delete functionality here\n                                        const updatedAssistances =\n                                          assistanceMultiSelectLast.filter(\n                                            (_, index) =>\n                                              index !== indexAssistance\n                                          );\n                                        setAssistanceMultiSelectDelete([\n                                          ...assistanceMultiSelectDelete,\n                                          itemAssistance.id,\n                                        ]);\n                                        setAssistanceMultiSelectLast(\n                                          updatedAssistances\n                                        );\n                                      }}\n                                      className=\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\"\n                                      aria-label=\"Delete assistance\"\n                                    >\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        strokeWidth=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-5 h-5\"\n                                      >\n                                        <path\n                                          strokeLinecap=\"round\"\n                                          strokeLinejoin=\"round\"\n                                          d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                        />\n                                      </svg>\n                                    </button>\n                                  </div>\n\n                                  {/* Card Content */}\n                                  <div className=\"p-4\">\n                                    {/* Appointment Info Section */}\n                                    <div className=\"mb-4\">\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#3C50E0\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Appointment Info\n                                        </h4>\n                                      </div>\n\n                                      <div className=\"ml-10 space-y-2\">\n                                        {caseType === \"Medical\" &&\n                                        caseTypeItem === \"Inpatient\" ? (\n                                          <>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Starting Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.start_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Ending Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.end_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                          </>\n                                        ) : (\n                                          <div className=\"flex\">\n                                            <span className=\"text-xs text-gray-500 w-40\">\n                                              Appointment Date:\n                                            </span>\n                                            <span className=\"text-xs font-medium\">\n                                              {itemAssistance.appointment_date ??\n                                                \"---\"}\n                                            </span>\n                                          </div>\n                                        )}\n                                        <div className=\"flex\">\n                                          <span className=\"text-xs text-gray-500 w-40\">\n                                            Service Location:\n                                          </span>\n                                          <span className=\"text-xs font-medium\">\n                                            {itemAssistance.service_location ??\n                                              \"---\"}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n\n                                    {/* Providers Section */}\n                                    <div>\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#7C3AED\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Providers\n                                        </h4>\n                                      </div>\n\n                                      {itemAssistance.provider_services\n                                        ?.length > 0 ? (\n                                        <div className=\"ml-10 space-y-4\">\n                                          {itemAssistance.provider_services.map(\n                                            (itemProvider, idx) => (\n                                              <div\n                                                key={idx}\n                                                className=\"p-3 bg-gray-50 rounded-md border border-gray-100\"\n                                              >\n                                                <div className=\"grid grid-cols-2 gap-2\">\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Provider\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider\n                                                        ?.full_name ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Service\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_type ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Speciality\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_specialist ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Date\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider_date ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                </div>\n                                              </div>\n                                            )\n                                          )}\n                                        </div>\n                                      ) : (\n                                        <div className=\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\">\n                                          No providers assigned\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )\n                          ) : (\n                            <div className=\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\">\n                              No assistances added yet\n                            </div>\n                          )}\n\n                          {/* end last data */}\n\n                          {/* new data */}\n                          {assistanceMultiSelect?.length > 0 ? (\n                            assistanceMultiSelect.map(\n                              (itemAssistance, indexAssistance) => (\n                                <div\n                                  key={indexAssistance}\n                                  className=\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\"\n                                >\n                                  {/* Card Header */}\n                                  <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\">\n                                    <h3 className=\"font-medium text-sm text-gray-800\">\n                                      Appointment #\n                                      {indexAssistance +\n                                        assistanceMultiSelectLast?.length +\n                                        1}\n                                    </h3>\n                                    <button\n                                      onClick={() => {\n                                        const updatedServices =\n                                          assistanceMultiSelect.filter(\n                                            (_, indexF) =>\n                                              indexF !== indexAssistance\n                                          );\n                                        setAssistanceMultiSelect(\n                                          updatedServices\n                                        );\n                                        // Implement delete functionality here\n                                        // const updatedAssistances = assistanceMultiSelectLast.filter(\n                                        //   (_, index) => index !== indexAssistance\n                                        // );\n                                        // setAssistanceMultiSelectDelete([\n                                        //   ...assistanceMultiSelectDelete,\n                                        //   itemAssistance.id,\n                                        // ]);\n                                        // setAssistanceMultiSelectLast(updatedAssistances);\n                                      }}\n                                      className=\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\"\n                                      aria-label=\"Delete assistance\"\n                                    >\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        strokeWidth=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-5 h-5\"\n                                      >\n                                        <path\n                                          strokeLinecap=\"round\"\n                                          strokeLinejoin=\"round\"\n                                          d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                        />\n                                      </svg>\n                                    </button>\n                                  </div>\n\n                                  {/* Card Content */}\n                                  <div className=\"p-4\">\n                                    {/* Appointment Info Section */}\n                                    <div className=\"mb-4\">\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#3C50E0\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Appointment Info\n                                        </h4>\n                                      </div>\n\n                                      <div className=\"ml-10 space-y-2\">\n                                        {caseType === \"Medical\" &&\n                                        caseTypeItem === \"Inpatient\" ? (\n                                          <>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Starting Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.start_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Ending Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.end_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                          </>\n                                        ) : (\n                                          <div className=\"flex\">\n                                            <span className=\"text-xs text-gray-500 w-40\">\n                                              Appointment Date:\n                                            </span>\n                                            <span className=\"text-xs font-medium\">\n                                              {itemAssistance.appointment_date ??\n                                                \"---\"}\n                                            </span>\n                                          </div>\n                                        )}\n                                        <div className=\"flex\">\n                                          <span className=\"text-xs text-gray-500 w-40\">\n                                            Service Location:\n                                          </span>\n                                          <span className=\"text-xs font-medium\">\n                                            {itemAssistance.service_location ??\n                                              \"---\"}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n\n                                    {/* Providers Section */}\n                                    <div>\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#7C3AED\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Providers\n                                        </h4>\n                                      </div>\n\n                                      {itemAssistance.provider_services\n                                        ?.length > 0 ? (\n                                        <div className=\"ml-10 space-y-4\">\n                                          {itemAssistance.provider_services.map(\n                                            (itemProvider, idx) => (\n                                              <div\n                                                key={idx}\n                                                className=\"p-3 bg-gray-50 rounded-md border border-gray-100\"\n                                              >\n                                                <div className=\"grid grid-cols-2 gap-2\">\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Provider\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider\n                                                        ?.full_name ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Service\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_type ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Speciality\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_specialist ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Date\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider_date ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                </div>\n                                              </div>\n                                            )\n                                          )}\n                                        </div>\n                                      ) : (\n                                        <div className=\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\">\n                                          No providers assigned\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )\n                          ) : (\n                            <div className=\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\">\n                              No new assistances added yet\n                            </div>\n                          )}\n\n                          {/* end new data */}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseStepUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseStepUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          toast.error(\n                            \"Initial Coordination Status empty or invalid. please try again\"\n                          );\n                          setCoordinatStatusListError(\n                            \"Initial Coordination Status is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseStepUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseStepUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseStepUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseStepUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseStepUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseStepUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // Show loading indicator while submitting the form\n                        setIsLoading(true);\n                        setShouldNavigateToFinalStep(true); // Set flag to navigate to final step\n\n                        // Map assistance items with their provider services\n                        const assistanceItems = assistanceMultiSelect.map(\n                          (item) => ({\n                            start_date: item.start_date,\n                            end_date: item.end_date,\n                            appointment_date: item.appointment_date,\n                            service_location: item.service_location,\n                            provider_services: item.provider_services.map(\n                              (providerService) => ({\n                                provider: providerService.provider?.id,\n                                service: providerService.provider_service?.id,\n                                date: providerService.provider_date,\n                              })\n                            ),\n                          })\n                        );\n\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            assistances: assistanceItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            assistance_deleted:\n                              assistanceMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Save & Complete\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,qBAAqB,KAAQ,qCAAqC,CAC3E,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,CACVC,cAAc,KACT,iCAAiC,CACxC,MAAO,CAAAC,cAAc,KAAM,iCAAiC,CAC5D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAEvD,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,uBAAuB,KAAQ,sCAAsC,CAC9E,OAASC,yBAAyB,KAAQ,iCAAiC,CAC3E,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAE1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,oBAAoB,CAAG,CAC3B,SAAS,CAAE,KAAK,CAChB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,cAAc,CAAE,KAAK,CACrB,sBAAsB,CAAE,KAAK,CAC7B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,MAAM,CAAE,KAAK,CACb,cAAc,CAAE,KAAK,CACrB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,cAAc,CAAE,KAAK,CACrB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,0BAA0B,CAAE,KAAK,CACjC,kCAAkC,CAAE,KAAK,CACzC,uBAAuB,CAAE,KAAK,CAC9B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,oBAAoB,CAAE,KAAK,CAC3B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,MAAM,CAAE,KAAK,CACb,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,kBAAkB,CAAE,KAAK,CACzB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,kBAAkB,CAAE,KAAK,CACzB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,KAAK,CACpB,OAAO,CAAE,KAAK,CACd,YAAY,CAAE,KAAK,CACnB,uBAAuB,CAAE,KAAK,CAC9B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,qBAAqB,CAAE,KAAK,CAC5B,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,gBAAgB,CAAE,KAAK,CACvB,OAAO,CAAE,KACX,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,KAAAC,SAAA,CACxB,KAAM,CAAAC,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAsC,QAAQ,CAAGvC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAwC,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE2C,EAAG,CAAC,CAAGvC,SAAS,CAAC,CAAC,CACxB,KAAM,CAACwC,YAAY,CAAC,CAAGvC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAwC,OAAO,CAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC,EAAI,CAAC,CAEhD;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACoD,QAAQ,CAAEC,WAAW,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACsD,aAAa,CAAEC,gBAAgB,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACwD,KAAK,CAAEC,QAAQ,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC0D,UAAU,CAAEC,aAAa,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC4D,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8D,cAAc,CAAEC,iBAAiB,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgE,KAAK,CAAEC,QAAQ,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkE,UAAU,CAAEC,aAAa,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACoE,OAAO,CAAEC,UAAU,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACsE,YAAY,CAAEC,eAAe,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACwE,IAAI,CAAEC,OAAO,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC0E,SAAS,CAAEC,YAAY,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC4E,OAAO,CAAEC,UAAU,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC8E,YAAY,CAAEC,eAAe,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACgF,WAAW,CAAEC,cAAc,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkF,aAAa,CAAEC,gBAAgB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACoF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACsF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACwF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC0F,yBAAyB,CAAEC,4BAA4B,CAAC,CAAG3F,QAAQ,CACxE,EACF,CAAC,CACD,KAAM,CAAC4F,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CACtE,KAAM,CAAC8F,2BAA2B,CAAEC,8BAA8B,CAAC,CACjE/F,QAAQ,CAAC,EAAE,CAAC,CACd,KAAM,CAACgG,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAC1E,KAAM,CAACkG,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGnG,QAAQ,CACxE,EACF,CAAC,CAED,KAAM,CAACoG,eAAe,CAAEC,kBAAkB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsG,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwG,QAAQ,CAAEC,WAAW,CAAC,CAAGzG,QAAQ,CACtC,GAAI,CAAA0G,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG9G,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC+G,QAAQ,CAAEC,WAAW,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiH,aAAa,CAAEC,gBAAgB,CAAC,CAAGlH,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACmH,YAAY,CAAEC,eAAe,CAAC,CAAGpH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtH,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACuH,eAAe,CAAEC,kBAAkB,CAAC,CAAGxH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACyH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG1H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC2H,KAAK,CAAEC,QAAQ,CAAC,CAAG5H,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAAC6H,YAAY,CAAEC,eAAe,CAAC,CAAG9H,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+H,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACiI,UAAU,CAAEC,aAAa,CAAC,CAAGlI,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACmI,eAAe,CAAEC,kBAAkB,CAAC,CAAGpI,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAACqI,eAAe,CAAEC,kBAAkB,CAAC,CAAGtI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACuI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxI,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACyI,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG1I,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC2I,wBAAwB,CAAEC,2BAA2B,CAAC,CAAG5I,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAAC6I,eAAe,CAAEC,kBAAkB,CAAC,CAAG9I,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+I,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhJ,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACiJ,SAAS,CAAEC,YAAY,CAAC,CAAGlJ,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACmJ,cAAc,CAAEC,iBAAiB,CAAC,CAAGpJ,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACqJ,OAAO,CAAEC,UAAU,CAAC,CAAGtJ,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuJ,YAAY,CAAEC,eAAe,CAAC,CAAGxJ,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACyJ,eAAe,CAAEC,kBAAkB,CAAC,CAAG1J,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC2J,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG5J,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC6J,YAAY,CAAEC,eAAe,CAAC,CAAG9J,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC+J,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhK,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACiK,YAAY,CAAEC,eAAe,CAAC,CAAGlK,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmK,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpK,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACqK,aAAa,CAAEC,gBAAgB,CAAC,CAAGtK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxK,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACyK,aAAa,CAAEC,gBAAgB,CAAC,CAAG1K,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC2K,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5K,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC6K,eAAe,CAAEC,kBAAkB,CAAC,CAAG9K,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC+K,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhL,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACiL,aAAa,CAAEC,gBAAgB,CAAC,CAAGlL,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmL,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpL,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACqL,UAAU,CAAEC,aAAa,CAAC,CAAGtL,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACuL,eAAe,CAAEC,kBAAkB,CAAC,CAAGxL,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACyL,MAAM,CAAEC,SAAS,CAAC,CAAG1L,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAAC2L,WAAW,CAAEC,cAAc,CAAC,CAAG5L,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC6L,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9L,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC+L,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGhM,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACiM,eAAe,CAAEC,kBAAkB,CAAC,CAAGlM,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACmM,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGpM,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACqM,YAAY,CAAEC,eAAe,CAAC,CAAGtM,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuM,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxM,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACyM,aAAa,CAAEC,gBAAgB,CAAC,CAAG1M,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC2M,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5M,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC6M,WAAW,CAAEC,cAAc,CAAC,CAAG9M,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC+M,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGhN,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAACiN,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlN,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJmN,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGpN,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAACqN,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGtN,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJuN,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAGxM,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFjO,SAAS,CAAC,IAAM,CACd,MAAO,IACLsN,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzO,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJuN,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGzN,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFjO,SAAS,CAAC,IAAM,CACd,MAAO,IACLyO,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG7O,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJuN,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAG7N,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFjO,SAAS,CAAC,IAAM,CACd,MAAO,IACL6O,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGjP,QAAQ,EAAAwC,SAAA,CAAC0M,QAAQ,CAACpM,OAAO,CAAC,UAAAN,SAAA,UAAAA,SAAA,CAAI,CAAC,CAAC,CACpE,KAAM,CAAC2M,SAAS,CAAEC,YAAY,CAAC,CAAGpP,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACqP,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGtP,QAAQ,CAAC,KAAK,CAAC,CAEjF,KAAM,CAAAuP,SAAS,CAAGrP,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGxP,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE;AACA3P,SAAS,CAAC,IAAM,CACd,GAAI6P,SAAS,EAAIA,SAAS,CAACG,MAAM,CAAG,CAAC,CAAE,CACrCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEL,SAAS,CAACG,MAAM,CAAC,CACtE,CACF,CAAC,CAAE,CAACH,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAM,cAAc,CAAGhQ,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAACW,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAGrQ,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAAC5O,UAAU,CAAC,CAC3D,KAAM,CAAE4P,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAG1Q,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAACqB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB;AACA7Q,SAAS,CAAC,IAAM,CACdiQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAE9C,GAAIa,YAAY,EAAIA,YAAY,CAACf,MAAM,CAAG,CAAC,EAAI7K,aAAa,CAAE,CAC5D8K,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAE/K,aAAa,CAAC,CAEjE;AACA,KAAM,CAAA+L,gBAAgB,CAAGH,YAAY,CAACI,IAAI,CACvCC,IAAI,EAAKC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GAAKwO,MAAM,CAAClM,aAAa,CACpD,CAAC,CAED,GAAI+L,gBAAgB,CAAE,CACpBjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEgB,gBAAgB,CAACI,SAAS,CAAC,CAC7D;AACAC,UAAU,CAAC,IAAM,CACfrM,cAAc,CAAC,CACbsM,KAAK,CAAEN,gBAAgB,CAACrO,EAAE,CAC1B4O,KAAK,CAAEP,gBAAgB,CAACI,SAC1B,CAAC,CAAC,CACF;AACAjC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLY,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChD;AACA,KAAM,CAAAwB,eAAe,CAAGX,YAAY,CAACI,IAAI,CACtCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAKsC,aACxB,CAAC,CACD,GAAIuM,eAAe,CAAE,CACnBzB,OAAO,CAACC,GAAG,CACT,4CAA4C,CAC5CwB,eAAe,CAACJ,SAClB,CAAC,CACDpM,cAAc,CAAC,CACbsM,KAAK,CAAEE,eAAe,CAAC7O,EAAE,CACzB4O,KAAK,CAAEC,eAAe,CAACJ,SACzB,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAAE,CAACP,YAAY,CAAE5L,aAAa,CAAC,CAAC,CAEjC,KAAM,CAAAwM,UAAU,CAAGxR,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAAC3O,UAAU,CAAC,CAC3D,KAAM,CAAE8Q,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,cAAc,CAAG5R,WAAW,CAAEsP,KAAK,EAAKA,KAAK,CAAC1O,cAAc,CAAC,CACnE,KAAM,CAAEiR,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CAAGH,cAAc,CAE5F,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpBnS,SAAS,CAAC,IAAM,CACd,GAAI,CAAC0P,QAAQ,CAAE,CACbhN,QAAQ,CAACyP,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AACA9C,YAAY,CAAC,IAAI,CAAC,CAElB;AACAzM,QAAQ,CAACvB,yBAAyB,CAAC,GAAG,CAAC,CAAC,CACxCuB,QAAQ,CAACjC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CACpCiC,QAAQ,CAACxB,uBAAuB,CAAC,GAAG,CAAC,CAAC,CACtCwB,QAAQ,CAAC/B,UAAU,CAACgC,EAAE,CAAC,CAAC,CAExB;AACA,KAAM,CAAAuP,SAAS,CAAGb,UAAU,CAAC,IAAM,CACjClC,YAAY,CAAC,KAAK,CAAC,CACnBY,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACvE,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAMmC,YAAY,CAACD,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAAC1P,QAAQ,CAAEgN,QAAQ,CAAE9M,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtC7C,SAAS,CAAC,IAAM,CACd,GAAI8R,iBAAiB,CAAE,CACrB,GAAIxC,yBAAyB,CAAE,CAC7BJ,aAAa,CAAC,CAAC,CAAC,CAChBK,4BAA4B,CAAC,KAAK,CAAC,CAAE;AACvC,CACAF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACyC,iBAAiB,CAAExC,yBAAyB,CAAC,CAAC,CAElD;AACAtP,SAAS,CAAC,IAAM,CACd,GAAIiS,qBAAqB,CAAE,CACzB5C,YAAY,CAAC,KAAK,CAAC,CACnB;AACAzM,QAAQ,CAAC/B,UAAU,CAACgC,EAAE,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACoP,qBAAqB,CAAErP,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEzC;AACA7C,SAAS,CAAC,IAAM,CACd,GAAI4R,iBAAiB,EAAII,qBAAqB,CAAE,CAC9C3C,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CAACuC,iBAAiB,CAAEI,qBAAqB,CAAC,CAAC,CAE9C;AACAhS,SAAS,CAAC,IAAM,CACd;AACA,GACE,CAAC8P,gBAAgB,EACjB,CAACW,eAAe,EAChBZ,SAAS,EACTA,SAAS,CAACG,MAAM,CAAG,CAAC,EACpBY,QAAQ,CACR,CACA;AACAvB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIuC,iBAAiB,EAAII,qBAAqB,CAAE,CACrD;AACA3C,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CACDS,gBAAgB,CAChBW,eAAe,CACfmB,iBAAiB,CACjBI,qBAAqB,CACrBnC,SAAS,CACTe,QAAQ,CACT,CAAC,CAEF5Q,SAAS,CAAC,IAAM,CACd;AACA,GAAI4Q,QAAQ,GAAK0B,SAAS,EAAI1B,QAAQ,GAAK,IAAI,CAAE,KAAA2B,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAI5C,QAAQ,CAAC6C,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpB/Q,YAAY,EAAAwQ,qBAAA,CAAC9C,QAAQ,CAAC6C,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CpQ,WAAW,EAAAqQ,qBAAA,CAAC/C,QAAQ,CAAC6C,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C7P,YAAY,EAAA8P,qBAAA,CAAChD,QAAQ,CAAC6C,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C1P,QAAQ,EAAA2P,qBAAA,CAACjD,QAAQ,CAAC6C,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CnQ,QAAQ,EAAAoQ,sBAAA,CAAClD,QAAQ,CAAC6C,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9CxP,UAAU,EAAAyP,sBAAA,CAACnD,QAAQ,CAAC6C,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAElD,KAAM,CAAAS,cAAc,EAAAR,sBAAA,CAAGpD,QAAQ,CAAC6C,OAAO,CAACgB,eAAe,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7D,KAAM,CAAAU,YAAY,CAAGpT,SAAS,CAAC6P,IAAI,CAChCwD,MAAM,EAAKA,MAAM,CAAC1S,KAAK,GAAKuS,cAC/B,CAAC,CAED,GAAIE,YAAY,CAAE,CAChB5P,UAAU,CAAC,CACT0M,KAAK,CAAEkD,YAAY,CAACzS,KAAK,CACzBwP,KAAK,cACH9P,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCpT,IAAA,SAAMmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEH,YAAY,CAACI,IAAI,CAAO,CAAC,cACjDrT,IAAA,SAAAoT,QAAA,CAAOH,YAAY,CAACzS,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACL6C,UAAU,CAAC,EAAE,CAAC,CAChB,CAEAJ,OAAO,EAAAuP,sBAAA,CAACrD,QAAQ,CAAC6C,OAAO,CAACsB,YAAY,UAAAd,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CAEA,KAAM,CAAAe,eAAe,EAAAzC,qBAAA,CAAG3B,QAAQ,CAACqE,cAAc,UAAA1C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAErD,KAAM,CAAA2C,aAAa,CAAG3T,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACtCwD,MAAM,EAAKA,MAAM,CAACQ,IAAI,GAAKH,eAC9B,CAAC,CAED,GAAIE,aAAa,CAAE,CACjBnN,eAAe,CAAC,CACdyJ,KAAK,CAAE0D,aAAa,CAACC,IAAI,CACzB1D,KAAK,CACHyD,aAAa,CAACE,IAAI,GAAK,EAAE,CACrBF,aAAa,CAACE,IAAI,CAAG,IAAI,CAAGF,aAAa,CAACC,IAAI,CAAG,IAAI,EAAI,EAAE,CAC3D,EACR,CAAC,CAAC,CACJ,CAAC,IAAM,CACLpN,eAAe,CAAC,EAAE,CAAC,CACrB,CAEAF,QAAQ,CAAC+I,QAAQ,CAACyE,MAAM,CAAC,CACzBlN,aAAa,EAAAqK,qBAAA,CAAC5B,QAAQ,CAAC0E,WAAW,UAAA9C,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACxC;AACA,GAAI5B,QAAQ,CAAC2E,gBAAgB,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,kBAAkB,EAAAF,qBAAA,EAAAC,sBAAA,CAAG7E,QAAQ,CAAC2E,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2B5S,EAAE,UAAA2S,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC9DvF,OAAO,CAACC,GAAG,CACT,uCAAuC,CACvCwF,kBACF,CAAC,CACDzF,OAAO,CAACC,GAAG,CACT,iCAAiC,CACjCU,QAAQ,CAAC2E,gBACX,CAAC,CAED;AACAhE,UAAU,CAAC,IAAM,CACfnM,gBAAgB,CAACsQ,kBAAkB,CAAC,CACpCzF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEwF,kBAAkB,CAAC,CACnE,CAAC,CAAE,EAAE,CAAC,CACR,CACAhP,WAAW,EAAA+L,mBAAA,CAAC7B,QAAQ,CAAC+E,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCxL,WAAW,EAAAyL,mBAAA,CAAC9B,QAAQ,CAACgF,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCjL,kBAAkB,EAAAkL,qBAAA,CAAC/B,QAAQ,CAACiF,gBAAgB,UAAAlD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACA,KAAM,CAAAmD,QAAQ,CACZ,CAAAlF,QAAQ,SAARA,QAAQ,kBAAAgC,qBAAA,CAARhC,QAAQ,CAAEmF,WAAW,UAAAnD,qBAAA,iBAArBA,qBAAA,CAAuB5E,GAAG,CAAEgI,MAAM,EAAKA,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,mBAAmB,CAAC,GACnE,EAAE,CAAE;AAENtN,sBAAsB,CAACmN,QAAQ,CAAC,CAEhC;AACAvN,kBAAkB,EAAAsK,qBAAA,CAACjC,QAAQ,CAACqF,mBAAmB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtD9J,kBAAkB,EAAA+J,qBAAA,CAAClC,QAAQ,CAACsF,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD3J,YAAY,EAAA4J,oBAAA,CAACnC,QAAQ,CAACuF,UAAU,UAAApD,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAAC,CACvCxJ,UAAU,EAAAyJ,kBAAA,CAACpC,QAAQ,CAACwF,QAAQ,UAAApD,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAAC,CACnC3L,eAAe,EAAA4L,qBAAA,CAACrC,QAAQ,CAACyF,cAAc,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CtJ,kBAAkB,EAAAuJ,qBAAA,CAACtC,QAAQ,CAAC0F,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAItC,QAAQ,CAAC2F,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB,GAAI,CAAAC,eAAe,EAAAF,qBAAA,EAAAC,kBAAA,CAAG7F,QAAQ,CAAC2F,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmB5T,EAAE,UAAA2T,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjD,KAAM,CAAAG,aAAa,CAAG9G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAK6T,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,CACjB5M,eAAe,CAAC,CACdyH,KAAK,CAAEmF,aAAa,CAAC9T,EAAE,CACvB4O,KAAK,CAAEkF,aAAa,CAACrF,SACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvH,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACA,GAAI6G,QAAQ,CAACgG,iBAAiB,CAAE,KAAAC,qBAAA,CAC9B3Q,0BAA0B,EAAA2Q,qBAAA,CAACjG,QAAQ,CAACgG,iBAAiB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9D,CAEA,GAAIjG,QAAQ,CAACkG,mBAAmB,CAAE,KAAAC,qBAAA,CAChC3Q,4BAA4B,EAAA2Q,qBAAA,CAACnG,QAAQ,CAACkG,mBAAmB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAClE,CACA;AACA9J,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAI2D,QAAQ,CAACoG,eAAe,CAAE,CAC5B/J,6BAA6B,CAAC2D,QAAQ,CAACoG,eAAe,CAAC,CACzD,CACA;AACA7L,gBAAgB,EAAAgI,qBAAA,CAACvC,QAAQ,CAACqG,cAAc,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/C5H,aAAa,EAAA6H,qBAAA,CAACxC,QAAQ,CAACsG,WAAW,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzCzH,SAAS,EAAA0H,qBAAA,CAACzC,QAAQ,CAACuG,cAAc,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvClG,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIyD,QAAQ,CAACwG,eAAe,CAAE,CAC5BjK,qBAAqB,CAACyD,QAAQ,CAACwG,eAAe,CAAC,CACjD,CACA;AACA,GAAIxG,QAAQ,CAACyG,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB,GAAI,CAAAC,gBAAgB,EAAAF,qBAAA,EAAAC,mBAAA,CAAG3G,QAAQ,CAACyG,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoB1U,EAAE,UAAAyU,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAEnD,GAAI,CAAAG,cAAc,CAAGpH,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEc,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAK2U,gBACxB,CAAC,CAED,GAAIC,cAAc,CAAE,CAClBxH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBnE,mBAAmB,CAAC,CAClByF,KAAK,CAAEiG,cAAc,CAAC5U,EAAE,CACxB4O,KAAK,CAAEgG,cAAc,CAACC,cAAc,EAAI,EAC1C,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBnE,mBAAmB,CAAC,CAClByF,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CACF,CACAlF,eAAe,EAAA+G,qBAAA,CAAC1C,QAAQ,CAAC+G,aAAa,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7CnH,kBAAkB,EAAAoH,qBAAA,CAAC3C,QAAQ,CAACgH,gBAAgB,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD5G,gBAAgB,EAAA6G,qBAAA,CAAC5C,QAAQ,CAACiH,gBAAgB,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjDnG,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAIuD,QAAQ,CAACkH,oBAAoB,CAAE,CACjCzK,oCAAoC,CAACuD,QAAQ,CAACkH,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAAClH,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAmH,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CACF,GAAI,CAAAC,OAAO,CAAG,IAAI,CAElB;AACA,GAAI/I,UAAU,GAAK,CAAC,CAAE,CACpB;AACA7L,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBY,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBI,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnB+B,gBAAgB,CAAC,EAAE,CAAC,CACpBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,uBAAuB,CAAC,EAAE,CAAC,CAC3BU,kBAAkB,CAAC,EAAE,CAAC,CACtBJ,oBAAoB,CAAC,EAAE,CAAC,CACxB3C,mBAAmB,CAAC,EAAE,CAAC,CACvBtB,iBAAiB,CAAC,EAAE,CAAC,CAErB;AACA,GAAI,CAACf,SAAS,EAAIA,SAAS,CAACgV,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACzC7U,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C4U,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC3U,QAAQ,EAAIA,QAAQ,CAAC4U,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvCzU,gBAAgB,CAAC,wBAAwB,CAAC,CAC1CwU,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC/T,KAAK,EAAIA,KAAK,CAACgU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjC7T,aAAa,CAAC,2BAA2B,CAAC,CAC1C4T,OAAO,CAAG,KAAK,CACjB,CAAC,IAAM,CACL;AACA,KAAM,CAAAE,UAAU,CAAG,wBAAwB,CAC3C,GAAI,CAACA,UAAU,CAACC,IAAI,CAAClU,KAAK,CAACmU,OAAO,CAAC,aAAa,CAAE,EAAE,CAAC,CAAC,CAAE,CACtDhU,aAAa,CAAC,4CAA4C,CAAC,CAC3D4T,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAIvU,KAAK,EAAIA,KAAK,CAACwU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC/B,KAAM,CAAAI,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACF,IAAI,CAAC1U,KAAK,CAAC,CAAE,CAC3BG,aAAa,CAAC,qCAAqC,CAAC,CACpDoU,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAI,CAAC3T,OAAO,EAAIA,OAAO,CAAC4T,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACrCzT,eAAe,CAAC,sBAAsB,CAAC,CACvCwT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACvT,IAAI,EAAIA,IAAI,CAACwT,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/BrT,YAAY,CAAC,mBAAmB,CAAC,CACjCoT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACnT,OAAO,EAAI,CAACA,OAAO,CAAC2M,KAAK,EAAI3M,OAAO,CAAC2M,KAAK,GAAK,EAAE,CAAE,CACtDxM,eAAe,CAAC,sBAAsB,CAAC,CACvCgT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACvR,QAAQ,EAAIA,QAAQ,CAACwR,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvClR,gBAAgB,CAAC,wBAAwB,CAAC,CAC1CiR,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAChR,QAAQ,EAAIA,QAAQ,CAACiR,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvC9Q,gBAAgB,CAAC,wBAAwB,CAAC,CAC1C6Q,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACxQ,eAAe,EAAIA,eAAe,CAACyQ,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACrDtQ,uBAAuB,CAAC,+BAA+B,CAAC,CACxDqQ,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC/S,WAAW,EAAI,CAACA,WAAW,CAACuM,KAAK,EAAIvM,WAAW,CAACuM,KAAK,GAAK,EAAE,CAAE,CAClElM,mBAAmB,CAAC,0BAA0B,CAAC,CAC/C0S,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAClQ,YAAY,EAAI,CAACA,YAAY,CAAC0J,KAAK,EAAI1J,YAAY,CAAC0J,KAAK,GAAK,EAAE,CAAE,CACrEvJ,oBAAoB,CAAC,uBAAuB,CAAC,CAC7C+P,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC9P,UAAU,EAAIA,UAAU,GAAK,EAAE,EAAIA,UAAU,CAAG,CAAC,CAAE,CACtDG,kBAAkB,CAAC,qDAAqD,CAAC,CACzE2P,OAAO,CAAG,KAAK,CACjB,CAEA;AACA,GAAInU,SAAS,EAAIA,SAAS,CAACoU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACxC,KAAM,CAAAK,YAAY,CAAG,GAAI,CAAA3R,IAAI,CAAC9C,SAAS,CAAC,CACxC,KAAM,CAAA0U,KAAK,CAAG,GAAI,CAAA5R,IAAI,CAAC,CAAC,CACxB,GAAI2R,YAAY,CAAGC,KAAK,CAAE,CACxBvU,iBAAiB,CAAC,qCAAqC,CAAC,CACxDgU,OAAO,CAAG,KAAK,CACjB,CACF,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACApG,2BAA2B,CAAC,EAAE,CAAC,CAE/B;AACA,GAAI,CAACH,mBAAmB,EAAIA,mBAAmB,CAACsH,MAAM,GAAK,CAAC,CAAE,CAC5DnH,2BAA2B,CAAC,+CAA+C,CAAC,CAC5EmP,OAAO,CAAG,KAAK,CACjB,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA;AACA;AAAA,CAED,IAAM,IAAIA,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA5D,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,cAAc,CAAC,EAAE,CAAC,CAElB;AACA,GAAIX,aAAa,EAAIA,aAAa,CAAC+M,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAChD,GAAI/M,aAAa,CAAC8E,MAAM,CAAG,CAAC,CAAE,CAC5B3E,qBAAqB,CAAC,+CAA+C,CAAC,CACtE2M,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAItM,MAAM,EAAIA,MAAM,GAAK,EAAE,EAAIA,MAAM,CAAG,CAAC,CAAE,CACzCG,cAAc,CAAC,4BAA4B,CAAC,CAC5CmM,OAAO,CAAG,KAAK,CACjB,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA5C,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CAExB;AACA,GAAIP,eAAe,EAAIA,eAAe,CAAC+L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpD,GAAI/L,eAAe,CAAC8D,MAAM,CAAG,CAAC,CAAE,CAC9B3D,uBAAuB,CAAC,iDAAiD,CAAC,CAC1E2L,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAI1L,YAAY,EAAIA,YAAY,CAAC2L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9C,GAAI3L,YAAY,CAAC0D,MAAM,CAAG,CAAC,CAAE,CAC3BvD,oBAAoB,CAAC,8CAA8C,CAAC,CACpEuL,OAAO,CAAG,KAAK,CACjB,CACF,CACF,CAEA;AACA,GAAI,CAACA,OAAO,CAAE,CACZtX,KAAK,CAAC8X,KAAK,CAAC,mDAAmD,CAAC,CAChE,OACF,CAEAnJ,YAAY,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAoJ,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAE1J,UAAU,CAAC2J,QAAQ,CAAC,CAAC,CAAC,CAE9C,GAAI3J,UAAU,GAAK,CAAC,CAAE,KAAA4J,kBAAA,CAAAC,mBAAA,CACpB;AACAL,QAAQ,CAACE,MAAM,CAAC,YAAY,CAAE1V,SAAS,CAAC,CACxCwV,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAEtV,QAAQ,CAAC,CACtCoV,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAE1V,SAAS,CAAG,GAAG,CAAGI,QAAQ,CAAC,CACxDoV,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAE9U,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAAC,CAC7C4U,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE1U,KAAK,CAAC,CACvCwU,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAElV,KAAK,CAAC,CACvCgV,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAEtU,OAAO,CAAC,CAC3CoU,QAAQ,CAACE,MAAM,CAAC,cAAc,CAAElU,IAAI,CAAC,CACrCgU,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAE9T,OAAO,CAAC2M,KAAK,CAAC,CACjDiH,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAAE,kBAAA,CAAE5T,WAAW,CAACuM,KAAK,UAAAqH,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAAC,CACvDJ,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAElS,QAAQ,CAAC,CACtCgS,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAE3R,QAAQ,CAAC,CACtCyR,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAE3R,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAAC,CAC7EqR,QAAQ,CAACE,MAAM,CAAC,kBAAkB,CAAEnR,eAAe,CAAC,CACpDiR,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAE/Q,KAAK,CAAG,MAAM,CAAG,OAAO,CAAC,CACnD6Q,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAEzQ,UAAU,CAAC,CAC1CuQ,QAAQ,CAACE,MAAM,CAAC,gBAAgB,EAAAG,mBAAA,CAAEhR,YAAY,CAAC0J,KAAK,UAAAsH,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CAE7D,CAAC,IAAM,IAAI7J,UAAU,GAAK,CAAC,CAAE,CAC3B;AACAvG,mBAAmB,CAAC6F,OAAO,CAACyH,MAAM,EAAI,CACpCyC,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAE3C,MAAM,CAAC,CAC1C,CAAC,CAAC,CAEF;AACAnQ,qBAAqB,CAAC0I,OAAO,CAAC,CAAC6C,IAAI,CAAEpP,KAAK,GAAK,CAC7CyW,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,eAAc,CAAEoP,IAAI,CAAC+E,UAAU,EAAI,EAAE,CAAC,CAC3EsC,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,aAAY,CAAEoP,IAAI,CAACgF,QAAQ,EAAI,EAAE,CAAC,CACvEqC,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,qBAAoB,CAAEoP,IAAI,CAAC8E,gBAAgB,EAAI,EAAE,CAAC,CACvFuC,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,qBAAoB,CAAEoP,IAAI,CAACkF,gBAAgB,EAAI,EAAE,CAAC,CAEvFlF,IAAI,CAACwF,iBAAiB,CAACrI,OAAO,CAAC,CAAClI,eAAe,CAAE0S,aAAa,GAAK,KAAAC,qBAAA,CAAAC,sBAAA,CACjER,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,wBAAuB+W,aAAc,aAAY,CAAE,EAAAC,qBAAA,CAAA3S,eAAe,CAACkQ,QAAQ,UAAAyC,qBAAA,iBAAxBA,qBAAA,CAA0BnW,EAAE,GAAI,EAAE,CAAC,CAC3H4V,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,wBAAuB+W,aAAc,YAAW,CAAE,EAAAE,sBAAA,CAAA5S,eAAe,CAAC6S,gBAAgB,UAAAD,sBAAA,iBAAhCA,sBAAA,CAAkCpW,EAAE,GAAI,EAAE,CAAC,CAClI4V,QAAQ,CAACE,MAAM,CAAE,eAAc3W,KAAM,wBAAuB+W,aAAc,SAAQ,CAAE1S,eAAe,CAAC8S,aAAa,EAAI,EAAE,CAAC,CAC1H,CAAC,CAAC,CACJ,CAAC,CAAC,CAEJ,CAAC,IAAM,IAAIlK,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA3B,0BAA0B,CAACiB,OAAO,CAACN,IAAI,EAAI,CACzCwK,QAAQ,CAACE,MAAM,CAAC,2BAA2B,CAAE1K,IAAI,CAAC,CACpD,CAAC,CAAC,CAEFnB,WAAW,CAACyB,OAAO,CAAC6K,MAAM,EAAI,CAC5BX,QAAQ,CAACE,MAAM,CAAC,iBAAiB,CAAES,MAAM,CAAC,CAC5C,CAAC,CAAC,CAEJ,CAAC,IAAM,IAAInK,UAAU,GAAK,CAAC,CAAE,CAC3B;AACAwJ,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEzN,aAAa,CAAC,CAChDuN,QAAQ,CAACE,MAAM,CAAC,aAAa,CAAErN,UAAU,CAAC,CAC1CmN,QAAQ,CAACE,MAAM,CAAC,gBAAgB,CAAEjN,MAAM,CAAC,CAEzC+C,kBAAkB,CAACF,OAAO,CAACN,IAAI,EAAI,CACjCwK,QAAQ,CAACE,MAAM,CAAC,kBAAkB,CAAE1K,IAAI,CAAC,CAC3C,CAAC,CAAC,CAEJ,CAAC,IAAM,IAAIgB,UAAU,GAAK,CAAC,CAAE,KAAAoK,qBAAA,CAC3B;AACAZ,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAAU,qBAAA,CAAEvN,gBAAgB,CAAC0F,KAAK,UAAA6H,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC1DZ,QAAQ,CAACE,MAAM,CAAC,kBAAkB,CAAEzM,eAAe,CAAC,CACpDuM,QAAQ,CAACE,MAAM,CAAC,eAAe,CAAErM,YAAY,CAAC,CAC9CmM,QAAQ,CAACE,MAAM,CAAC,kBAAkB,CAAEjM,aAAa,CAAC,CAElDmC,iCAAiC,CAACN,OAAO,CAACN,IAAI,EAAI,CAChDwK,QAAQ,CAACE,MAAM,CAAC,kCAAkC,CAAE1K,IAAI,CAAC,CAC3D,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAArL,QAAQ,CAAC7B,cAAc,CAAC8B,EAAE,CAAE4V,QAAQ,CAAC,CAAC,CAE5CpJ,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,MAAOmJ,KAAK,CAAE,CACdnJ,YAAY,CAAC,KAAK,CAAC,CACnB3O,KAAK,CAAC8X,KAAK,CAAC,+CAA+C,CAAC,CAC9D,CACF,CAAC,CAED,mBACE7W,KAAA,CAACnB,aAAa,EAAAqU,QAAA,EAEXzF,SAAS,eACR3N,IAAA,QAAKmT,SAAS,CAAC,+FAA+F,CAAAC,QAAA,cAC5GlT,KAAA,QAAKiT,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3EpT,IAAA,QAAKmT,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvGnT,IAAA,QAAKmT,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC7D,CAAC,CACH,CACN,cAEDlT,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACflT,KAAA,QAAKiT,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtDpT,IAAA,MAAG6X,IAAI,CAAC,YAAY,CAAAzE,QAAA,cAClBlT,KAAA,QAAKiT,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpY,IAAA,SAAMmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJpT,IAAA,SAAAoT,QAAA,cACEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpY,IAAA,QAAKmT,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CpT,IAAA,OAAImT,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,WAEpE,CAAI,CAAC,CACF,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJlT,KAAA,QAAKiT,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxClT,KAAA,QAAKiT,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEpT,IAAA,QAAKmT,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G7S,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEiM,GAAG,CAAC,CAAC8L,IAAI,CAAE9X,KAAK,gBAC1BL,KAAA,QACEoY,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI9K,UAAU,CAAG6K,IAAI,CAAC9X,KAAK,EAAIiN,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAAC4K,IAAI,CAAC9X,KAAK,CAAC,CAC3B,CACF,CAAE,CACF4S,SAAS,CAAG,kCACV3F,UAAU,CAAG6K,IAAI,CAAC9X,KAAK,EAAIiN,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EACL,8BAA8B,CAAA4F,QAAA,EAE9B5F,UAAU,CAAG6K,IAAI,CAAC9X,KAAK,cACtBP,IAAA,QAAKmT,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHpT,IAAA,QACEuY,GAAG,CAAEvZ,eAAgB,CACrBmU,SAAS,CAAC,QAAQ,CAClBqF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJ/K,UAAU,GAAK6K,IAAI,CAAC9X,KAAK,cAC3BP,IAAA,QAAKmT,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExEnT,IAAA,QAAKmT,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAElBpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDlY,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEiF,IAAI,CAAC7X,KAAK,CAAM,CAAC,CACtDgN,UAAU,GAAK6K,IAAI,CAAC9X,KAAK,cACxBP,IAAA,QAAKmT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChDiF,IAAI,CAAC5X,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNP,KAAA,QAAKiT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAEtD5F,UAAU,GAAK,CAAC,cACftN,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfpT,IAAA,QAAKmT,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACVzR,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpCkX,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxB9I,KAAK,CAAEvO,SAAU,CACjBsX,QAAQ,CAAGC,CAAC,EAAKtX,YAAY,CAACsX,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC/C,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC1R,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,UACEmT,SAAS,CAAC,wEAAwE,CAClFyF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvB9I,KAAK,CAAEnO,QAAS,CAChBkX,QAAQ,CAAGC,CAAC,EAAKlX,WAAW,CAACkX,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN7P,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAE9C,CAAK,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACVjR,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpC0W,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3B9I,KAAK,CAAE/N,KAAM,CACb8W,QAAQ,CAAGC,CAAC,EAAK9W,QAAQ,CAAC8W,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC3C,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClR,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENhC,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QACrC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,uBACVzQ,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpCkW,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtB9I,KAAK,CAAEvN,KAAM,CACbsW,QAAQ,CAAGC,CAAC,EAAKtW,QAAQ,CAACsW,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC3C,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC1Q,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzClT,KAAA,QAAKiT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,UACpC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE3M,OAAQ,CACf0V,QAAQ,CAAG5F,MAAM,EAAK,CACpB7P,UAAU,CAAC6P,MAAM,CAAC,CAElB;AACA,GAAIA,MAAM,EAAIA,MAAM,CAACnD,KAAK,CAAE,CAC1B;AACA,KAAM,CAAAiJ,WAAW,CAAG9F,MAAM,CAACnD,KAAK,CAChC,KAAM,CAAA1J,YAAY,CAAGhG,oBAAoB,CAAC2Y,WAAW,CAAC,CAEtD,GAAI3S,YAAY,CAAE,CAChB;AACA,KAAM,CAAA4S,cAAc,CAAGnZ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACvCwJ,QAAQ,EAAKA,QAAQ,CAACxF,IAAI,GAAKrN,YAClC,CAAC,CAED,GAAI4S,cAAc,CAAE,CAClB3S,eAAe,CAAC,CACdyJ,KAAK,CAAEkJ,cAAc,CAACvF,IAAI,CAC1B1D,KAAK,CAAEiJ,cAAc,CAACtF,IAAI,GAAK,EAAE,CAC7BsF,cAAc,CAACtF,IAAI,CAAG,IAAI,CAAGsF,cAAc,CAACvF,IAAI,CAAG,GAAG,CACtDuF,cAAc,CAACvF,IACrB,CAAC,CAAC,CAEF;AACAzU,KAAK,CAACka,OAAO,CAAE,qCAAoCF,cAAc,CAACtF,IAAK,KAAIsF,cAAc,CAACvF,IAAK,GAAE,CAAC,CACpG,CACF,CACF,CACF,CAAE,CACFP,SAAS,CAAC,SAAS,CACnBiG,OAAO,CAAEvZ,SAAS,CAAC0M,GAAG,CAAEnJ,OAAO,GAAM,CACnC2M,KAAK,CAAE3M,OAAO,CAAC5C,KAAK,CACpBwP,KAAK,cACH9P,KAAA,QACEiT,SAAS,CAAG,GACV/P,OAAO,CAAC5C,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EACjC,6BAA6B,CAAA4S,QAAA,eAE9BpT,IAAA,SAAMmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEhQ,OAAO,CAACiQ,IAAI,CAAO,CAAC,cAC5CrT,IAAA,SAAAoT,QAAA,CAAOhQ,OAAO,CAAC5C,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJqY,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEpW,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvBqW,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF7Z,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9P,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACNpD,KAAA,QAAKiT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACR,eAAe,EACdua,MAAM,CAAC,yCAAyC,CAChD5G,SAAS,CAAG,wBACVjQ,SAAS,CAAG,eAAe,CAAG,kBAC/B,mCAAmC,CACpC4V,QAAQ,CAAGC,CAAC,EAAK,CACf9V,OAAO,CAAC8V,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAC,CACzB,CAAE,CACFiK,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BlX,OAAO,EAAAkX,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAErX,IAAK,CACnBsX,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUFva,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClQ,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENhD,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cACvDlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1F,gBAAiB,CACxByO,QAAQ,CAAG5F,MAAM,EAAK,CACpB5I,mBAAmB,CAAC4I,MAAM,CAAC,CAC7B,CAAE,CACFkG,OAAO,CAAExK,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAErC,GAAG,CAAEqJ,SAAS,GAAM,CACvC7F,KAAK,CAAE6F,SAAS,CAACxU,EAAE,CACnB4O,KAAK,CAAE4F,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJuE,YAAY,CAAEA,CAACtH,MAAM,CAAEuH,UAAU,GAC/BvH,MAAM,CAAClD,KAAK,CACT0K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvH,SAAS,CAAC,SAAS,CACnB0F,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnP,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoP,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF7Z,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7I,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNrK,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACVxI,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCiO,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3B9I,KAAK,CAAEtF,eAAgB,CACvBqO,QAAQ,CAAGC,CAAC,EAAKrO,kBAAkB,CAACqO,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CACrD,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzI,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN3K,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAEvM,WAAY,CACnBsV,QAAQ,CAAG5F,MAAM,EAAK,CACpBzP,cAAc,CAACyP,MAAM,CAAC,CACxB,CAAE,CACFC,SAAS,CAAC,SAAS,CACnBiG,OAAO,CAAE9J,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE/C,GAAG,CAAEoD,IAAI,GAAM,CACpCI,KAAK,CAAEJ,IAAI,CAACvO,EAAE,CACd4O,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ2K,YAAY,CAAEA,CAACtH,MAAM,CAAEuH,UAAU,GAC/BvH,MAAM,CAAClD,KAAK,CACT0K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD7B,WAAW,CAAC,uBAAuB,CACnCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE9V,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvB+V,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF7Z,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCxP,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAEH,CAAC,cAEN1D,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACV9N,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CACpCuT,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChC9I,KAAK,CAAE/K,QAAS,CAChB8T,QAAQ,CAAGC,CAAC,EAAK9T,WAAW,CAAC8T,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC9C,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC/N,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACNnF,KAAA,QAAKiT,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACElT,KAAA,WACE6P,KAAK,CAAExK,QAAS,CAChBuT,QAAQ,CAAGC,CAAC,EAAKvT,WAAW,CAACuT,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC7CoD,SAAS,CAAG,wBACV1N,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CAAA2N,QAAA,eAEpCpT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAqD,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvCpT,IAAA,WAAQ+P,KAAK,CAAE,SAAU,CAAAqD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CpT,IAAA,WAAQ+P,KAAK,CAAE,WAAY,CAAAqD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTpT,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3N,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACLF,QAAQ,GAAK,SAAS,eACrBrF,KAAA,QAAKiT,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,YAClC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACjD,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACElT,KAAA,WACE6P,KAAK,CAAEpK,YAAa,CACpBmT,QAAQ,CAAGC,CAAC,EAAKnT,eAAe,CAACmT,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CACjDoD,SAAS,CAAG,wBACVtN,iBAAiB,CACb,eAAe,CACf,kBACL,mCAAmC,CAAAuN,QAAA,eAEpCpT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAqD,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5CpT,IAAA,WAAQ+P,KAAK,CAAE,YAAa,CAAAqD,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChDpT,IAAA,WAAQ+P,KAAK,CAAE,WAAY,CAAAqD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACTpT,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvN,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CACN,cAED3F,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1J,YAAa,CACpByS,QAAQ,CAAG5F,MAAM,EAAK,CACpB5M,eAAe,CAAC4M,MAAM,CAAC,CACvB1E,OAAO,CAACC,GAAG,CAACyE,MAAM,CAAC,CAErB,CAAE,CACFkG,OAAO,CAAEtZ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEyM,GAAG,CAAE2M,QAAQ,GAAM,CACzCnJ,KAAK,CAAEmJ,QAAQ,CAACxF,IAAI,CACpB1D,KAAK,CACHkJ,QAAQ,CAACvF,IAAI,GAAK,EAAE,CAChBuF,QAAQ,CAACvF,IAAI,CACX,IAAI,CACJuF,QAAQ,CAACxF,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJ8G,YAAY,CAAEA,CAACtH,MAAM,CAAEuH,UAAU,GAC/BvH,MAAM,CAAClD,KAAK,CACT0K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvH,SAAS,CAAC,SAAS,CACnB0F,WAAW,CAAC,0BAA0B,CACtCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnT,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBoT,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF7Z,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7M,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNrG,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACVxM,eAAe,CACX,eAAe,CACf,kBACL,oCAAoC,CACrCiS,IAAI,CAAC,QAAQ,CACbgC,GAAG,CAAE,CAAE,CACPvC,IAAI,CAAE,IAAK,CACXQ,WAAW,CAAC,MAAM,CAClB9I,KAAK,CAAEtJ,UAAW,CAClBqS,QAAQ,CAAGC,CAAC,EAAKrS,aAAa,CAACqS,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAChD,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzM,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNzG,KAAA,QAAKiT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCpT,IAAA,QAAKmT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5ClT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACE4Y,IAAI,CAAE,UAAW,CACjBjF,IAAI,CAAC,OAAO,CACZvS,EAAE,CAAC,OAAO,CACVyZ,OAAO,CAAE1U,KAAK,GAAK,IAAK,CACxB2S,QAAQ,CAAGC,CAAC,EAAK,CACf3S,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFpG,IAAA,UACEmT,SAAS,CAAC,6CAA6C,CACvD2H,GAAG,CAAC,OAAO,CAAA1H,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5ClT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACE4Y,IAAI,CAAE,UAAW,CACjBjF,IAAI,CAAC,QAAQ,CACbvS,EAAE,CAAC,QAAQ,CACXyZ,OAAO,CAAE1U,KAAK,GAAK,KAAM,CACzB2S,QAAQ,CAAGC,CAAC,EAAK,CACf3S,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFpG,IAAA,UACEmT,SAAS,CAAC,6CAA6C,CACvD2H,GAAG,CAAC,QAAQ,CAAA1H,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGNpT,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,aACE+P,KAAK,CAAEhK,eAAgB,CACvBgV,IAAI,CAAE,CAAE,CACRjC,QAAQ,CAAGC,CAAC,EAAK/S,kBAAkB,CAAC+S,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CACpDoD,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNjT,KAAA,QAAKiT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEpT,IAAA,WACEsY,OAAO,CAAEhC,uBAAwB,CACjC0E,QAAQ,CAAEzK,qBAAsB,CAChC4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,qBAAqB,CAAG,aAAa,CAAG,QAAQ,CAC3C,CAAC,cACTvQ,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA2C,KAAK,CAAG,IAAI,CAChBtZ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnB2C,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBR,gBAAgB,CAAC,EAAE,CAAC,CACpBzB,mBAAmB,CAAC,EAAE,CAAC,CACvBV,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBiD,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIpF,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CsZ,KAAK,CAAG,KAAK,CACf,CAEA,GAAIzY,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCsY,KAAK,CAAG,KAAK,CACf,CAEA,GAAI7X,OAAO,GAAK,EAAE,EAAIA,OAAO,CAAC2M,KAAK,GAAK,EAAE,CAAE,CAC1CxM,eAAe,CAAC,yBAAyB,CAAC,CAC1C0X,KAAK,CAAG,KAAK,CACf,CAEA,GAAIzX,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuM,KAAK,GAAK,EAAE,CAAE,CAClDlM,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CoX,KAAK,CAAG,KAAK,CACf,CAEA,GAAI1V,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CuV,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACL1V,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,EAAE,CACnB,CACAG,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CmV,KAAK,CAAG,KAAK,CACf,CACA,GAAIjW,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C2V,KAAK,CAAG,KAAK,CACf,CACA,GAAI5U,YAAY,GAAK,EAAE,EAAIA,YAAY,CAAC0J,KAAK,GAAK,EAAE,CAAE,CACpDvJ,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CyU,KAAK,CAAG,KAAK,CACf,CACA,GAAIxU,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7CqU,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTxN,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxO,KAAK,CAAC8X,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF5D,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP5F,UAAU,GAAK,CAAC,cACftN,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfpT,IAAA,QAAKmT,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDpT,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,SACrC,cAAApT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACElT,KAAA,QAAKiT,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BlT,KAAA,QAAKiT,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,sBACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACFnT,EAAE,CAAC,sBAAsB,CACzBwX,IAAI,CAAE,UAAW,CACjBiC,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,sBACF,CAAE,CACFxH,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,sBAAsB,CAC1B3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,yBACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,yBACF,CAAE,CACFvZ,EAAE,CAAC,yBAAyB,CAC5BwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,yBAAyB,CAC7B3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,6BACF,CAAE,CACFvZ,EAAE,CAAC,6BAA6B,CAChCwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,6BAA6B,CACjC3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,qCACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,qCACF,CAAE,CACFvZ,EAAE,CAAC,qCAAqC,CACxCwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,qCAAqC,CACzC3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,kCACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,kCACF,CAAE,CACFvZ,EAAE,CAAC,kCAAkC,CACrCwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,kCAAkC,CACtC3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,kBACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kBAAkB,CACnB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GAAK,kBACf,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,kBACF,CAAE,CACFvZ,EAAE,CAAC,kBAAkB,CACrBwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,kBAAkB,CACtB3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,kBAED,CAAO,CAAC,EACL,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,6BACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,6BACF,CAAE,CACFvZ,EAAE,CAAC,6BAA6B,CAChCwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,6BAA6B,CACjC3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cAGNlT,KAAA,QAAKiT,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAC3B,mBACF,CAAC,CACD,CACAzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CACnC,mBACF,CAAE,CACFvZ,EAAE,CAAC,mBAAmB,CACtBwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,mBAAmB,CACvB3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEpT,IAAA,UACE8Y,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAAC9R,mBAAmB,CAAC0T,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3CzT,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACiU,MAAM,CACvB3G,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACFsG,OAAO,CAAE5T,mBAAmB,CAAC0T,QAAQ,CAAC,QAAQ,CAAE,CAChDvZ,EAAE,CAAC,QAAQ,CACXwX,IAAI,CAAE,UAAW,CACjBzF,SAAS,CAAC,MAAM,CACjB,CAAC,cACFnT,IAAA,UACE8a,GAAG,CAAC,QAAQ,CACZ3H,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCNpT,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCjM,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENnH,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,qBAE1D,CAAK,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAEjDpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,6BAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAE3CpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/C7N,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,QAAKiT,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,wBACtB,CAAC,GAAG,cAC1BpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACE4Y,IAAI,CAAC,MAAM,CACXzF,SAAS,CAAG,wBACVxL,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpCkR,WAAW,CAAC,wBAAwB,CACpC9I,KAAK,CAAEtI,SAAU,CACjBqR,QAAQ,CAAGC,CAAC,EAAK,CACfrR,YAAY,CAACqR,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAC,CAC5B;AACA,GAAIlI,OAAO,EAAIA,OAAO,CAAGkR,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CACvCjI,UAAU,CAACiR,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAC,CAC5B,CACF,CAAE,CACH,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzL,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cACNzH,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACE4Y,IAAI,CAAC,MAAM,CACXzF,SAAS,CAAG,wBACVpL,YAAY,CACR,eAAe,CACf,kBACL,mCAAmC,CACpC8Q,WAAW,CAAC,sBAAsB,CAClC9I,KAAK,CAAElI,OAAQ,CACfiR,QAAQ,CAAGC,CAAC,EAAKjR,UAAU,CAACiR,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC5CiL,QAAQ,CAAE,CAACvT,SAAU,CACrBmT,GAAG,CAAEnT,SAAU,CAChB,CAAC,cACFzH,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrL,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7H,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,wBACV5L,oBAAoB,CAChB,eAAe,CACf,kBACL,mCAAmC,CACpCqR,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9B9I,KAAK,CAAE1I,eAAgB,CACvByR,QAAQ,CAAGC,CAAC,EACVzR,kBAAkB,CAACyR,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAClC,CACF,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7L,oBAAoB,CACjBA,oBAAoB,CACpB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CACN,CACE,CAAC,cAENvH,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAE1ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACE4Y,IAAI,CAAC,MAAM,CACXzF,SAAS,CAAG,wBACVhL,oBAAoB,CAChB,eAAe,CACf,kBACL,mCAAmC,CACpC0Q,WAAW,CAAC,mBAAmB,CAC/B9I,KAAK,CAAE9H,eAAgB,CACvB6Q,QAAQ,CAAGC,CAAC,EACV7Q,kBAAkB,CAAC6Q,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAClC,CACF,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCjL,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNnI,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClT,KAAA,QAAKiT,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1H,YAAa,CACpByQ,QAAQ,CAAG5F,MAAM,EAAK,KAAAiI,aAAA,CACpB7S,eAAe,CAAC4K,MAAM,CAAC,CACvB;AACA,GAAI,CAAA+B,eAAe,EAAAkG,aAAA,CAAGjI,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEnD,KAAK,UAAAoL,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC;AACAvN,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAAsH,aAAa,CAAG9G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAK6T,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAAkG,qBAAA,CACjBrX,mBAAmB,EAAAqX,qBAAA,CACjBlG,aAAa,CAACmG,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACD;AACAtL,UAAU,CAAC,IAAM,CACflC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACL7J,mBAAmB,CAAC,EAAE,CAAC,CACvB6J,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFuF,SAAS,CAAC,SAAS,CACnBiG,OAAO,CAAEhL,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE7B,GAAG,CAAEoD,IAAI,GAAM,CACjCI,KAAK,CAAEJ,IAAI,CAACvO,EAAE,CACd4O,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ2K,YAAY,CAAEA,CAACtH,MAAM,CAAEuH,UAAU,QAAAa,aAAA,QAAAA,aAAA,CAC/BpI,MAAM,CAAClD,KAAK,UAAAsL,aAAA,iBAAZA,aAAA,CACIZ,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC,CAAC,EACvC,CACD7B,WAAW,CAAC,oBAAoB,CAChCQ,YAAY,KACZ;AAAA,CACA1L,SAAS,CAAEU,gBACX;AAAA,CACAkN,UAAU,CAAEA,CAAA,GAAM,CAChB/M,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAE,CACF6K,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnR,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBoR,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF7Z,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7K,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAENrI,KAAA,QAAKiT,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACElT,KAAA,WACEiT,SAAS,CAAG,uBACVrO,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCgU,QAAQ,CAAGC,CAAC,EAAK,CACflU,kBAAkB,CAACkU,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAEnL,eAAgB,CAAAwO,QAAA,eAEvBpT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BjM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyI,GAAG,CAAC,CAACiP,OAAO,CAAEjb,KAAK,QAAAkb,qBAAA,oBACpCvb,KAAA,WAAQ6P,KAAK,CAAEyL,OAAO,CAACpa,EAAG,CAAAgS,QAAA,GAAAqI,qBAAA,CACvBD,OAAO,CAACE,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BD,OAAO,CAACG,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAGH,OAAO,CAACG,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACT3b,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtO,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACN9E,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpClT,KAAA,QAAKiT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,YAClC,CAAC,GAAG,cACdpT,IAAA,WAAQmT,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACNlT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,UACEmT,SAAS,CAAG,uBACVxK,iBAAiB,CACb,eAAe,CACf,kBACL,oCAAoC,CACrCiQ,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,aAAa,CACzB9I,KAAK,CAAEtH,YAAa,CACpBqQ,QAAQ,CAAGC,CAAC,EAAKrQ,eAAe,CAACqQ,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAClD,CAAC,cACF/P,IAAA,QAAKmT,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzK,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENzI,KAAA,QAAKiT,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BlT,KAAA,WACEoY,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAA2C,KAAK,CAAG,IAAI,CAChBzS,oBAAoB,CAAC,EAAE,CAAC,CACxBzD,uBAAuB,CAAC,EAAE,CAAC,CAC3B6D,oBAAoB,CAAC,EAAE,CAAC,CACxB,GACEP,YAAY,GAAK,EAAE,EACnBA,YAAY,CAAC0H,KAAK,GAAK,EAAE,CACzB,CACAvH,oBAAoB,CAClB,4BACF,CAAC,CACDvJ,KAAK,CAAC8X,KAAK,CAAC,uBAAuB,CAAC,CACpCkE,KAAK,CAAG,KAAK,CACf,CACA,GAAIrW,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACD9F,KAAK,CAAC8X,KAAK,CAAC,+BAA+B,CAAC,CAC5CkE,KAAK,CAAG,KAAK,CACf,CACA,GAAIxS,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAClB,4BACF,CAAC,CACD3J,KAAK,CAAC8X,KAAK,CAAC,yBAAyB,CAAC,CACtCkE,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAW,MAAM,CAAG,KAAK,CACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAC,UAAU,CAAG,KAAK,CAExB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI,CAACD,MAAM,EAAI,CAACC,UAAU,CAAE,KAAAC,mBAAA,CAC1B;AACA,GAAI,CAAA7G,eAAe,EAAA6G,mBAAA,CAAGzT,YAAY,CAAC0H,KAAK,UAAA+L,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAC9C,KAAM,CAAA5G,aAAa,CAAG9G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EACHC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GAAKwO,MAAM,CAACqF,eAAe,CAC9C,CAAC,CAGD,GAAIC,aAAa,CAAE,KAAA6G,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAGrX,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CsQ,aAAa,SAAbA,aAAa,kBAAA6G,sBAAA,CAAb7G,aAAa,CAAEmG,QAAQ,UAAAU,sBAAA,iBAAvBA,sBAAA,CAAyBjP,OAAO,CAC7BoP,OAAO,EAAK,CACX1N,OAAO,CAACC,GAAG,CAACyN,OAAO,CAAC9a,EAAE,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAA+a,YAAY,CAChBjH,aAAa,SAAbA,aAAa,kBAAA8G,sBAAA,CAAb9G,aAAa,CAAEmG,QAAQ,UAAAW,sBAAA,iBAAvBA,sBAAA,CAAyBtM,IAAI,CAC1BC,IAAI,EACHC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GACfwO,MAAM,CAACqM,cAAc,CACzB,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACAlY,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACE8Q,QAAQ,CAAEI,aAAa,CACvBsG,OAAO,CAAEW,YAAY,CACrBC,IAAI,CAAE3T,YACR,CAAC,CACF,CAAC,CACFH,eAAe,CAAC,EAAE,CAAC,CACnBzD,kBAAkB,CAAC,EAAE,CAAC,CACtB6D,eAAe,CAAC,EAAE,CAAC,CACnB8F,OAAO,CAACC,GAAG,CAACzK,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLwE,oBAAoB,CAClB,kCACF,CAAC,CACDvJ,KAAK,CAAC8X,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACLvO,oBAAoB,CAClB,0BACF,CAAC,CACDvJ,KAAK,CAAC8X,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACLvO,oBAAoB,CAClB,4CACF,CAAC,CACDvJ,KAAK,CAAC8X,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACF5D,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAEjEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNpY,IAAA,SAAAoT,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,CAERpP,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEuI,GAAG,CAAC,CAAC+P,YAAY,CAAE/b,KAAK,QAAAgc,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,oBAC5C3c,KAAA,QAEEiT,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CpT,IAAA,QAAKmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClCpT,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAwE,eAAe,CACnB9Y,mBAAmB,CAACkX,MAAM,CACxB,CAAC6B,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAKzc,KAC5B,CAAC,CACH0D,sBAAsB,CAAC6Y,eAAe,CAAC,CACzC,CAAE,CAAA1J,QAAA,cAEFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxClT,KAAA,QAAAkT,QAAA,eACEpT,IAAA,MAAAoT,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAAmJ,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAACxH,QAAQ,UAAA0H,sBAAA,iBAArBA,sBAAA,CAAuB3M,SAAS,UAAA0M,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNrc,KAAA,QAAAkT,QAAA,eACEpT,IAAA,MAAAoT,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAAqJ,qBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACd,OAAO,UAAAkB,sBAAA,iBAApBA,sBAAA,CAAsBhB,YAAY,UAAAe,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACNvc,KAAA,QAAAkT,QAAA,eACEpT,IAAA,MAAAoT,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAAuJ,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACd,OAAO,UAAAoB,sBAAA,iBAApBA,sBAAA,CAAsBjB,kBAAkB,UAAAgB,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,cACNzc,KAAA,QAAAkT,QAAA,eACEpT,IAAA,MAAAoT,QAAA,CAAG,OAAK,CAAG,CAAC,IAAC,EAAAyJ,kBAAA,CAACP,YAAY,CAACF,IAAI,UAAAS,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EACrC,CAAC,EACH,CAAC,GA9CDtc,KA+CF,CAAC,EACP,CAAC,EAEC,CAAC,EACH,CAAC,cACNP,IAAA,QAAAoT,QAAA,cACElT,KAAA,WACEoY,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAA/B,OAAO,CAAG,IAAI,CAClB3O,iBAAiB,CAAC,EAAE,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CACnBR,uBAAuB,CAAC,EAAE,CAAC,CAC3BY,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CACxB;AACA,GACEjD,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,WAAW,CAC5B,CACA;AACA,GAAI,CAAC8B,SAAS,CAAE,CACdG,iBAAiB,CACf,oCACF,CAAC,CACD3I,KAAK,CAAC8X,KAAK,CAAC,oCAAoC,CAAC,CACjDR,OAAO,CAAG,KAAK,CACjB,CACA,GAAI,CAAC1O,OAAO,CAAE,CACZG,eAAe,CACb,kCACF,CAAC,CACD/I,KAAK,CAAC8X,KAAK,CAAC,kCAAkC,CAAC,CAC/CR,OAAO,CAAG,KAAK,CACjB,CACF,CAAC,IAAM,CACL;AACA,GAAI,CAAClP,eAAe,CAAE,CACpBG,uBAAuB,CACrB,8BACF,CAAC,CACDvI,KAAK,CAAC8X,KAAK,CAAC,8BAA8B,CAAC,CAC3CR,OAAO,CAAG,KAAK,CACjB,CACF,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAIvS,mBAAmB,CAACuK,MAAM,GAAK,CAAC,CAAE,CACpC/F,oBAAoB,CAClB,qCACF,CAAC,CACDvJ,KAAK,CAAC8X,KAAK,CAAC,qCAAqC,CAAC,CAClDR,OAAO,CAAG,KAAK,CACjB,CAEA,GAAIA,OAAO,CAAE,CACX;AACA,KAAM,CAAA0G,aAAa,CAAG,CACpB7b,EAAE,CAAE8D,IAAI,CAACgY,GAAG,CAAC,CAAC,CAAE;AAChBxI,UAAU,CAAEjN,SAAS,EAAI,IAAI,CAC7BkN,QAAQ,CAAE9M,OAAO,EAAI,IAAI,CACzB4M,gBAAgB,CAAEpN,eAAe,EAAI,IAAI,CACzCwN,gBAAgB,CAAE5M,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAE,EAAE,CACrCkN,iBAAiB,CAAEnR,mBAAmB,CAACuI,GAAG,CACvCoD,IAAI,GAAM,CACTmF,QAAQ,CAAEnF,IAAI,CAACmF,QAAQ,CACvB2C,gBAAgB,CAAE9H,IAAI,CAAC6L,OAAO,CAC9B9D,aAAa,CAAE/H,IAAI,CAACyM,IACtB,CAAC,CACH,CACF,CAAC,CAED;AACA/X,wBAAwB,CAAE8Y,IAAI,EAAK,CACjC,GAAGA,IAAI,CACPF,aAAa,CACd,CAAC,CAEF;AACA;AAEA;AACAvV,YAAY,CAAC,EAAE,CAAC,CAChBI,UAAU,CAAC,EAAE,CAAC,CACdR,kBAAkB,CAAC,EAAE,CAAC,CACtBY,kBAAkB,CAAC,EAAE,CAAC,CACtBjE,sBAAsB,CAAC,EAAE,CAAC,CAC1BqE,eAAe,CAAC,EAAE,CAAC,CACnBzD,kBAAkB,CAAC,EAAE,CAAC,CACtB6D,eAAe,CAAC,EAAE,CAAC,CAEnBzJ,KAAK,CAACka,OAAO,CAAC,+BAA+B,CAAC,CAChD,CACF,CAAE,CACFhG,SAAS,CAAC,uJAAuJ,CAAAC,QAAA,eAEjKpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAC,KAAK,CACjBnF,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,aAAa,CAAAC,QAAA,cAEvBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNpY,IAAA,SAAAoT,QAAA,CAAM,oBAAkB,CAAM,CAAC,EACzB,CAAC,CACN,CAAC,cAENpT,IAAA,QAAAoT,QAAA,cACElT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpClT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAE,GAAI,CACjBnF,MAAM,CAAC,SAAS,CAChB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,0cAA0c,CAC7c,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,OAAImT,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,aAEpD,CAAI,CAAC,EACF,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,OAAO,CAAAC,QAAA,EAEnB,CAAA1O,yBAAyB,SAAzBA,yBAAyB,iBAAzBA,yBAAyB,CAAE6J,MAAM,EAAG,CAAC,CACpC7J,yBAAyB,CAAC6H,GAAG,CAC3B,CAAC8Q,cAAc,CAAEC,eAAe,QAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,oBAC9Bzd,KAAA,QAEEiT,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eAGjIlT,KAAA,QAAKiT,SAAS,CAAC,+GAA+G,CAAAC,QAAA,eAC5HlT,KAAA,OAAIiT,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,eACnC,CAACkK,eAAe,CAAG,CAAC,EAC/B,CAAC,cACLtd,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,KAAM,CAAAsF,kBAAkB,CACtBlZ,yBAAyB,CAACwW,MAAM,CAC9B,CAAC6B,CAAC,CAAExc,KAAK,GACPA,KAAK,GAAK+c,eACd,CAAC,CACH/Y,8BAA8B,CAAC,CAC7B,GAAGD,2BAA2B,CAC9B+Y,cAAc,CAACjc,EAAE,CAClB,CAAC,CACFuD,4BAA4B,CAC1BiZ,kBACF,CAAC,CACH,CAAE,CACFzK,SAAS,CAAC,kGAAkG,CAC5G,aAAW,mBAAmB,CAAAC,QAAA,cAE9BpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAC,KAAK,CACjBnF,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,EACN,CAAC,cAGNlY,KAAA,QAAKiT,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBlT,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAE,GAAI,CACjBnF,MAAM,CAAC,SAAS,CAChB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,OAAImT,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAEpD,CAAI,CAAC,EACF,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7B7N,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,CAAAE,SAAA,EAAAgT,QAAA,eACElT,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAmK,qBAAA,CAClCF,cAAc,CAAC3I,UAAU,UAAA6I,qBAAA,UAAAA,qBAAA,CACxB,KAAK,CACH,CAAC,EACJ,CAAC,cACNrd,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAoK,qBAAA,CAClCH,cAAc,CAAC1I,QAAQ,UAAA6I,qBAAA,UAAAA,qBAAA,CACtB,KAAK,CACH,CAAC,EACJ,CAAC,EACN,CAAC,cAEHtd,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAqK,qBAAA,CAClCJ,cAAc,CAAC5I,gBAAgB,UAAAgJ,qBAAA,UAAAA,qBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CACN,cACDvd,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAsK,qBAAA,CAClCL,cAAc,CAACxI,gBAAgB,UAAA6I,qBAAA,UAAAA,qBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNxd,KAAA,QAAAkT,QAAA,eACElT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAE,GAAI,CACjBnF,MAAM,CAAC,SAAS,CAChB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,OAAImT,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,WAEpD,CAAI,CAAC,EACF,CAAC,CAEL,EAAAuK,qBAAA,CAAAN,cAAc,CAAClI,iBAAiB,UAAAwI,qBAAA,iBAAhCA,qBAAA,CACGpP,MAAM,EAAG,CAAC,cACZvO,IAAA,QAAKmT,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BiK,cAAc,CAAClI,iBAAiB,CAAC5I,GAAG,CACnC,CAAC+P,YAAY,CAAEuB,GAAG,QAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAChBpe,IAAA,QAEEmT,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAE5DlT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClT,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA0K,sBAAA,EAAAC,sBAAA,CAClCzB,YAAY,CAACxH,QAAQ,UAAAiJ,sBAAA,iBAArBA,sBAAA,CACGlO,SAAS,UAAAiO,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClB,CAAC,EACJ,CAAC,cACN5d,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA4K,sBAAA,EAAAC,sBAAA,CAClC3B,YAAY,CACV7E,gBAAgB,UAAAwG,sBAAA,iBADlBA,sBAAA,CAEGvC,YAAY,UAAAsC,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACrB,CAAC,EACJ,CAAC,cACN9d,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA8K,sBAAA,EAAAC,sBAAA,CAClC7B,YAAY,CACV7E,gBAAgB,UAAA0G,sBAAA,iBADlBA,sBAAA,CAEGxC,kBAAkB,UAAAuC,sBAAA,UAAAA,sBAAA,CACpB,KAAK,CACH,CAAC,EACJ,CAAC,cACNhe,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAgL,sBAAA,CAClC9B,YAAY,CAAC5E,aAAa,UAAA0G,sBAAA,UAAAA,sBAAA,CACzB,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EA3CDP,GA4CF,CAAC,EAEV,CAAC,CACE,CAAC,cAEN7d,IAAA,QAAKmT,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,uBAE7E,CAAK,CACN,EACE,CAAC,EACH,CAAC,GAxMDkK,eAyMF,CAAC,EAEV,CAAC,cAEDtd,IAAA,QAAKmT,SAAS,CAAC,mEAAmE,CAAAC,QAAA,CAAC,0BAEnF,CAAK,CACN,CAKA,CAAAhP,qBAAqB,SAArBA,qBAAqB,iBAArBA,qBAAqB,CAAEmK,MAAM,EAAG,CAAC,CAChCnK,qBAAqB,CAACmI,GAAG,CACvB,CAAC8Q,cAAc,CAAEC,eAAe,QAAAe,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC9Bve,KAAA,QAEEiT,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eAGjIlT,KAAA,QAAKiT,SAAS,CAAC,+GAA+G,CAAAC,QAAA,eAC5HlT,KAAA,OAAIiT,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,eAEhD,CAACkK,eAAe,EACd5Y,yBAAyB,SAAzBA,yBAAyB,iBAAzBA,yBAAyB,CAAE6J,MAAM,EACjC,CAAC,EACD,CAAC,cACLvO,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAwE,eAAe,CACnB1Y,qBAAqB,CAAC8W,MAAM,CAC1B,CAAC6B,CAAC,CAAEC,MAAM,GACRA,MAAM,GAAKM,eACf,CAAC,CACHjZ,wBAAwB,CACtByY,eACF,CAAC,CACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,CACF3J,SAAS,CAAC,kGAAkG,CAC5G,aAAW,mBAAmB,CAAAC,QAAA,cAE9BpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAC,KAAK,CACjBnF,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,EACN,CAAC,cAGNlY,KAAA,QAAKiT,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBlT,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBlT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAE,GAAI,CACjBnF,MAAM,CAAC,SAAS,CAChB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,OAAImT,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAEpD,CAAI,CAAC,EACF,CAAC,cAENlT,KAAA,QAAKiT,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7B7N,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,CAAAE,SAAA,EAAAgT,QAAA,eACElT,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAiL,sBAAA,CAClChB,cAAc,CAAC3I,UAAU,UAAA2J,sBAAA,UAAAA,sBAAA,CACxB,KAAK,CACH,CAAC,EACJ,CAAC,cACNne,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAkL,sBAAA,CAClCjB,cAAc,CAAC1I,QAAQ,UAAA2J,sBAAA,UAAAA,sBAAA,CACtB,KAAK,CACH,CAAC,EACJ,CAAC,EACN,CAAC,cAEHpe,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAmL,sBAAA,CAClClB,cAAc,CAAC5I,gBAAgB,UAAA8J,sBAAA,UAAAA,sBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CACN,cACDre,KAAA,QAAKiT,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBpT,IAAA,SAAMmT,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAoL,sBAAA,CAClCnB,cAAc,CAACxI,gBAAgB,UAAA2J,sBAAA,UAAAA,sBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNte,KAAA,QAAAkT,QAAA,eACElT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCpT,IAAA,QAAKmT,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBoF,WAAW,CAAE,GAAI,CACjBnF,MAAM,CAAC,SAAS,CAChB9E,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBpT,IAAA,SACEkY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,OAAImT,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,WAEpD,CAAI,CAAC,EACF,CAAC,CAEL,EAAAqL,sBAAA,CAAApB,cAAc,CAAClI,iBAAiB,UAAAsJ,sBAAA,iBAAhCA,sBAAA,CACGlQ,MAAM,EAAG,CAAC,cACZvO,IAAA,QAAKmT,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BiK,cAAc,CAAClI,iBAAiB,CAAC5I,GAAG,CACnC,CAAC+P,YAAY,CAAEuB,GAAG,QAAAa,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,oBAChBhf,IAAA,QAEEmT,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAE5DlT,KAAA,QAAKiT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrClT,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAsL,uBAAA,EAAAC,uBAAA,CAClCrC,YAAY,CAACxH,QAAQ,UAAA6J,uBAAA,iBAArBA,uBAAA,CACG9O,SAAS,UAAA6O,uBAAA,UAAAA,uBAAA,CAAI,KAAK,CAClB,CAAC,EACJ,CAAC,cACNxe,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAwL,uBAAA,EAAAC,uBAAA,CAClCvC,YAAY,CACV7E,gBAAgB,UAAAoH,uBAAA,iBADlBA,uBAAA,CAEGnD,YAAY,UAAAkD,uBAAA,UAAAA,uBAAA,CAAI,KAAK,CACrB,CAAC,EACJ,CAAC,cACN1e,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA0L,uBAAA,EAAAC,uBAAA,CAClCzC,YAAY,CACV7E,gBAAgB,UAAAsH,uBAAA,iBADlBA,uBAAA,CAEGpD,kBAAkB,UAAAmD,uBAAA,UAAAA,uBAAA,CACpB,KAAK,CACH,CAAC,EACJ,CAAC,cACN5e,KAAA,QAAKiT,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BpT,IAAA,SAAMmT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAExC,CAAM,CAAC,cACPpT,IAAA,SAAMmT,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA4L,uBAAA,CAClC1C,YAAY,CAAC5E,aAAa,UAAAsH,uBAAA,UAAAA,uBAAA,CACzB,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EA3CDnB,GA4CF,CAAC,EAEV,CAAC,CACE,CAAC,cAEN7d,IAAA,QAAKmT,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,uBAE7E,CAAK,CACN,EACE,CAAC,EACH,CAAC,GA/MDkK,eAgNF,CAAC,EAEV,CAAC,cAEDtd,IAAA,QAAKmT,SAAS,CAAC,mEAAmE,CAAAC,QAAA,CAAC,8BAEnF,CAAK,CACN,EAGE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNlT,KAAA,QAAKiT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEpT,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACTpT,IAAA,WACEsY,OAAO,CAAEhC,uBAAwB,CACjC0E,QAAQ,CAAEzK,qBAAsB,CAChC4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,qBAAqB,CAAG,aAAa,CAAG,QAAQ,CAC3C,CAAC,cACTvQ,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA2C,KAAK,CAAG,IAAI,CAChBjU,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,2BAA2B,CAAC,EAAE,CAAC,CAE/B,GAAIH,mBAAmB,CAACsH,MAAM,GAAK,CAAC,CAAE,CACpCtP,KAAK,CAAC8X,KAAK,CACT,gEACF,CAAC,CACD3P,2BAA2B,CACzB,0CACF,CAAC,CACD6T,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTxN,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxO,KAAK,CAAC8X,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF5D,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP5F,UAAU,GAAK,CAAC,cACftN,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfpT,IAAA,QAAKmT,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlT,KAAA,WACM8L,0BAA0B,CAAC,CAAEmH,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFpT,IAAA,aAAWkM,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5ClM,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNpT,IAAA,UAAOif,KAAK,CAAEve,eAAgB,CAAA0S,QAAA,cAC5BlT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnC7H,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvB2P,MAAM,CAAE1O,IAAI,EAAK,CAACnB,WAAW,CAACsP,QAAQ,CAACnO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAAC0S,SAAS,CACZ,CAAC,cACNhf,KAAA,QAAAkT,QAAA,EACG+L,UAAU,CAAC3S,IAAI,CAAC4S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbhN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF+R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ5L,IAAI,CAAC0S,SA0CP,CACN,CAAC,CACHrT,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBAC3CL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAACmH,IAAI,CACP,CAAC,cACNzT,KAAA,QAAAkT,QAAA,EACG,CAAC5G,IAAI,CAAC8S,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbxM,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAAC4O,MAAM,CACd,CAAC6B,CAAC,CAAEwC,aAAa,GACfhf,KAAK,GAAKgf,aACd,CACF,CAAC,CACH,CAAE,CACFpM,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ5L,IAAI,CAACmH,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAENzT,KAAA,QAAKiT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEpT,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACTpT,IAAA,WACEsY,OAAO,CAAEhC,uBAAwB,CACjC0E,QAAQ,CAAEzK,qBAAsB,CAChC4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,qBAAqB,CAAG,aAAa,CAAG,QAAQ,CAC3C,CAAC,cACTvQ,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP5F,UAAU,GAAK,CAAC,cACftN,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfpT,IAAA,QAAKmT,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,UACEmT,SAAS,CAAC,wEAAwE,CAClFyF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvC9I,KAAK,CAAEtG,aAAc,CACrBqP,QAAQ,CAAGC,CAAC,EAAKrP,gBAAgB,CAACqP,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAEN7P,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,UACEmT,SAAS,CAAC,wEAAwE,CAClFyF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpC9I,KAAK,CAAElG,UAAW,CAClBiP,QAAQ,CAAGC,CAAC,EAAKjP,aAAa,CAACiP,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN/P,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,UACEmT,SAAS,CAAC,wEAAwE,CAClFyF,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/B9I,KAAK,CAAE9F,MAAO,CACd6O,QAAQ,CAAGC,CAAC,EAAK7O,SAAS,CAAC6O,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN/P,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlT,KAAA,WACMgN,yBAAyB,CAAC,CAAEiG,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFpT,IAAA,aAAWmN,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CnN,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNpT,IAAA,UAAOif,KAAK,CAAEve,eAAgB,CAAA0S,QAAA,cAC5BlT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnC3H,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACfyP,MAAM,CAAE1O,IAAI,EAAK,CAACnB,WAAW,CAACsP,QAAQ,CAACnO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAAC0S,SAAS,CACZ,CAAC,cACNhf,KAAA,QAAAkT,QAAA,EACG+L,UAAU,CAAC3S,IAAI,CAAC4S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbhN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF+R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ5L,IAAI,CAAC0S,SA0CP,CACN,CAAC,CACHlS,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACnCL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAACmH,IAAI,CACP,CAAC,cACNzT,KAAA,QAAAkT,QAAA,EACG,CAAC5G,IAAI,CAAC8S,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbrL,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAAC4O,MAAM,CACd,CAAC6B,CAAC,CAAEwC,aAAa,GACfhf,KAAK,GAAKgf,aACd,CACF,CAAC,CACH,CAAE,CACFpM,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ5L,IAAI,CAACmH,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGNzT,KAAA,QAAKiT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEpT,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACTpT,IAAA,WACEsY,OAAO,CAAEhC,uBAAwB,CACjC0E,QAAQ,CAAEzK,qBAAsB,CAChC4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,qBAAqB,CAAG,aAAa,CAAG,QAAQ,CAC3C,CAAC,cACTvQ,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP5F,UAAU,GAAK,CAAC,cACftN,KAAA,QAAKiT,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfpT,IAAA,QAAKmT,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDlT,KAAA,QAAKiT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1ClT,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1F,gBAAiB,CACxByO,QAAQ,CAAG5F,MAAM,EAAK,CACpB5I,mBAAmB,CAAC4I,MAAM,CAAC,CAC7B,CAAE,CACFkG,OAAO,CAAExK,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAErC,GAAG,CAAEqJ,SAAS,GAAM,CACvC7F,KAAK,CAAE6F,SAAS,CAACxU,EAAE,CACnB4O,KAAK,CAAE4F,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJuE,YAAY,CAAEA,CAACtH,MAAM,CAAEuH,UAAU,GAC/BvH,MAAM,CAAClD,KAAK,CACT0K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvH,SAAS,CAAC,SAAS,CACnB0F,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAExL,KAAK,IAAM,CACzB,GAAGwL,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEnP,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBoP,SAAS,CAAE3L,KAAK,CAAC4L,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFxG,MAAM,CAAGsG,IAAI,GAAM,CACjB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACP7Y,OAAO,CAAE,MAAM,CACfkZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN3Z,KAAA,QAAKiT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACEpT,IAAA,UACEmT,SAAS,CAAC,wEAAwE,CAClFyF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3B9I,KAAK,CAAElF,YAAa,CACpBiO,QAAQ,CAAGC,CAAC,EAAKjO,eAAe,CAACiO,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN/P,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDpT,IAAA,QAAKmT,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1ClT,KAAA,QAAKiT,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCpT,IAAA,QAAKmT,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNpT,IAAA,QAAAoT,QAAA,cACElT,KAAA,WACE6P,KAAK,CAAE9E,aAAc,CACrB6N,QAAQ,CAAGC,CAAC,EAAK7N,gBAAgB,CAAC6N,CAAC,CAACL,MAAM,CAAC3I,KAAK,CAAE,CAClDoD,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFpT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAqD,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzCpT,IAAA,WAAQ+P,KAAK,CAAE,SAAU,CAAAqD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1CpT,IAAA,WAAQ+P,KAAK,CAAE,UAAW,CAAAqD,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5CpT,IAAA,WAAQ+P,KAAK,CAAE,QAAS,CAAAqD,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENpT,IAAA,QAAKmT,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACNlT,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDlT,KAAA,WACMoN,wCAAwC,CAAC,CAC3C6F,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFpT,IAAA,aAAWuN,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DvN,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNpY,IAAA,QAAKmT,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNpT,IAAA,UAAOif,KAAK,CAAEve,eAAgB,CAAA0S,QAAA,cAC5BlT,KAAA,QAAKiT,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCzH,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9BuP,MAAM,CAAE1O,IAAI,EAAK,CAACnB,WAAW,CAACsP,QAAQ,CAACnO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAAC0S,SAAS,CACZ,CAAC,cACNhf,KAAA,QAAAkT,QAAA,EACG+L,UAAU,CAAC3S,IAAI,CAAC4S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbhN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF+R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ5L,IAAI,CAAC0S,SA0CP,CACN,CAAC,CACH9R,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAEjM,KAAK,gBACVL,KAAA,QACEiT,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFpT,IAAA,QAAKmT,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/ElT,KAAA,QACE4X,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,eAEdpT,IAAA,SAAMoY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpY,IAAA,SAAMoY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlY,KAAA,QAAKiT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDpT,IAAA,QAAKmT,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5F5G,IAAI,CAACmH,IAAI,CACP,CAAC,cACNzT,KAAA,QAAAkT,QAAA,EACG,CAAC5G,IAAI,CAAC8S,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNrf,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM,CACbjL,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAAC4O,MAAM,CACd,CAAC6B,CAAC,CAAEwC,aAAa,GACfhf,KAAK,GAAKgf,aACd,CACJ,CAAC,CACH,CAAE,CACFpM,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoE,KAAK,CAAC,QAAQ,CAAAjJ,QAAA,cAEdpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ5L,IAAI,CAACmH,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAENzT,KAAA,QAAKiT,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEpT,IAAA,WACEsY,OAAO,CAAEA,CAAA,GAAM7K,aAAa,CAAC,CAAC,CAAE,CAChC0F,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACTpT,IAAA,WACEsY,OAAO,CAAEhC,uBAAwB,CACjC0E,QAAQ,CAAEzK,qBAAsB,CAChC4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,qBAAqB,CAAG,aAAa,CAAG,QAAQ,CAC3C,CAAC,cACTvQ,IAAA,WACEgb,QAAQ,CAAE7K,iBAAkB,CAC5BmI,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAAkH,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CACnB;AACA/R,YAAY,CAAC,IAAI,CAAC,CAClBE,4BAA4B,CAAC,IAAI,CAAC,CAAE;AAEpC;AACA,KAAM,CAAA8R,eAAe,CAAGxb,qBAAqB,CAACmI,GAAG,CAC9CoD,IAAI,GAAM,CACT+E,UAAU,CAAE/E,IAAI,CAAC+E,UAAU,CAC3BC,QAAQ,CAAEhF,IAAI,CAACgF,QAAQ,CACvBF,gBAAgB,CAAE9E,IAAI,CAAC8E,gBAAgB,CACvCI,gBAAgB,CAAElF,IAAI,CAACkF,gBAAgB,CACvCM,iBAAiB,CAAExF,IAAI,CAACwF,iBAAiB,CAAC5I,GAAG,CAC1C3H,eAAe,OAAAib,sBAAA,CAAAC,sBAAA,OAAM,CACpBhL,QAAQ,EAAA+K,sBAAA,CAAEjb,eAAe,CAACkQ,QAAQ,UAAA+K,sBAAA,iBAAxBA,sBAAA,CAA0Bze,EAAE,CACtCoa,OAAO,EAAAsE,sBAAA,CAAElb,eAAe,CAAC6S,gBAAgB,UAAAqI,sBAAA,iBAAhCA,sBAAA,CAAkC1e,EAAE,CAC7Cgb,IAAI,CAAExX,eAAe,CAAC8S,aACxB,CAAC,EACH,CACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAqI,aAAa,CAAG/b,mBAAmB,CAACuI,GAAG,CAC1CoD,IAAI,OAAAqQ,aAAA,CAAAC,cAAA,OAAM,CACTzE,OAAO,EAAAwE,aAAA,CAAErQ,IAAI,CAAC6L,OAAO,UAAAwE,aAAA,iBAAZA,aAAA,CAAc5e,EAAE,CACzB0T,QAAQ,EAAAmL,cAAA,CAAEtQ,IAAI,CAACmF,QAAQ,UAAAmL,cAAA,iBAAbA,cAAA,CAAe7e,EAAE,CAC3Bgb,IAAI,CAAEzM,IAAI,CAACyM,IACb,CAAC,EACH,CAAC,CACD;AACA,KAAM,CAAAjb,QAAQ,CACZ9B,UAAU,CAAC+B,EAAE,CAAE,CACbqR,UAAU,CAAEjR,SAAS,CACrBkR,SAAS,CAAE9Q,QAAQ,CACnBiO,SAAS,CAAErO,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC+Q,SAAS,CAAEvQ,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1BwQ,aAAa,CAAEpQ,KAAK,CACpBqQ,aAAa,CAAE7Q,KAAK,CACpB8Q,eAAe,CAAElQ,OAAO,CACxB0Q,YAAY,CAAEtQ,IAAI,CAClBgQ,eAAe,CAAE5P,OAAO,CAAC2M,KAAK,CAC9B;AACAvM,WAAW,EAAAgc,mBAAA,CAAEhc,WAAW,CAACuM,KAAK,UAAAyP,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CACpCtL,SAAS,CAAElP,QAAQ,CACnBmP,SAAS,CAAE5O,QAAQ,CACnBqP,cAAc,CACZrP,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC5CyO,gBAAgB,CAAErO,eAAe,CACjC;AACAyO,mBAAmB,CAAE3N,eAAe,CACpCyN,WAAW,CAAErN,mBAAmB,CAChCwN,gBAAgB,CACd9O,YAAY,GAAK,WAAW,CACxB,EAAE,CACF0B,eAAe,CACrBqN,UAAU,CACR/O,YAAY,GAAK,WAAW,CAAG8B,SAAS,CAAG,EAAE,CAC/CkN,QAAQ,CACNhP,YAAY,GAAK,WAAW,CAAGkC,OAAO,CAAG,EAAE,CAC7CgN,gBAAgB,CAAE5M,eAAe,CACjC6M,QAAQ,EAAA2K,oBAAA,CAAEpX,YAAY,CAAC0H,KAAK,UAAA0P,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAClC;AACAjK,cAAc,CAAE/L,aAAa,CAC7BgM,WAAW,CAAE5L,UAAU,CACvB6L,cAAc,CAAEzL,MAAM,CACtB2L,SAAS,EAAA8J,sBAAA,CAAErV,gBAAgB,CAAC0F,KAAK,UAAA2P,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CACvCvJ,gBAAgB,CAAE1L,eAAe,CACjCyL,aAAa,CAAErL,YAAY,CAC3BuL,gBAAgB,CAAEnL,aAAa,CAC/B;AACAiV,uBAAuB,CAAErU,0BAA0B,CACnDsU,cAAc,CAAEnT,kBAAkB,CAClCoT,8BAA8B,CAC5BhT,iCAAiC,CACnCiT,aAAa,CAAEhV,WAAW,CAC1B+C,SAAS,CAAE2R,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9BO,WAAW,CAAEV,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAClCW,iBAAiB,CAAErc,yBAAyB,SAAzBA,yBAAyB,UAAzBA,yBAAyB,CAAI,EAAE,CAClDsc,kBAAkB,CAChBlc,2BAA2B,SAA3BA,2BAA2B,UAA3BA,2BAA2B,CAAI,EAAE,CACnC;AACAsP,MAAM,CAAEzN,KAAK,CAAG,MAAM,CAAG,OAAO,CAChC0N,WAAW,CAAEpN,UAAU,CACvB+M,cAAc,EAAAmM,oBAAA,CAAEtZ,YAAY,CAAC0J,KAAK,UAAA4P,oBAAA,UAAAA,oBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACFxM,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjEjD,iBAAiB,CAAG,WAAW,CAAG,iBAAiB,CAC9C,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP3C,UAAU,GAAK,CAAC,cACfxN,IAAA,QAAKmT,SAAS,CAAC,EAAE,CAAAC,QAAA,cACfpT,IAAA,QAAKmT,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDlT,KAAA,QAAKiT,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEpT,IAAA,QACE8X,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB9E,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9EpT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoY,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNpY,IAAA,QAAKmT,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNpT,IAAA,QAAKmT,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAS1DpT,IAAA,MACE6X,IAAI,CAAC,YAAY,CACjB1E,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAArS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}