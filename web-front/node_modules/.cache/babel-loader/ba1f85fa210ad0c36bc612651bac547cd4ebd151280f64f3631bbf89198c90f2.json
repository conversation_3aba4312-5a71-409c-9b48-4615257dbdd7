{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{confirmResetPasswordReducer,coordinatorsListReducer,createCoordinatorReducer,createNewUserReducer,deleteUserReducer,detailCoordinatorReducer,getProfileUserReducer,historyListCoordinatorReducer,historyListLoggedReducer,logoutSavedUserReducer,resetPasswordReducer,updateCoordinatorReducer,updateLastLoginUserReducer,updatePasswordUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListCoordinatorReducer,caseListInsuranceReducer,caseListLoggedReducer,caseListMapReducer,caseListProviderReducer,caseListReducer,caseHistoryReducer,commentCaseListReducer,createNewCaseReducer,createNewCommentCaseReducer,deleteCaseReducer,deleteCommentCaseReducer,detailCaseReducer,duplicateCaseReducer,updateCaseAssignedReducer,updateCaseReducer,updateCaseStatusReducer,updateCaseStepReducer}from\"./reducers/caseReducers\";import{addNewProviderReducer,deleteProviderReducer,detailProviderReducer,providerListReducer,updateProviderReducer}from\"./reducers/providerReducers\";import{addNewInsuranceReducer,deleteInsuranceReducer,detailInsuranceReducer,insuranceListReducer,updateInsuranceReducer}from\"./reducers/insurancereducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,caseListMap:caseListMapReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,updateCaseStatus:updateCaseStatusReducer,updateCaseStep:updateCaseStepReducer,caseListCoordinator:caseListCoordinatorReducer,updateCaseAssigned:updateCaseAssignedReducer,caseListInsurance:caseListInsuranceReducer,caseListProvider:caseListProviderReducer,caseListLogged:caseListLoggedReducer,duplicateCase:duplicateCaseReducer,caseHistory:caseHistoryReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,addNewProvider:addNewProviderReducer,deleteProvider:deleteProviderReducer,updateProvider:updateProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\ninsuranceList:insuranceListReducer,addNewInsurance:addNewInsuranceReducer,deleteInsurance:deleteInsuranceReducer,detailInsurance:detailInsuranceReducer,updateInsurance:updateInsuranceReducer,//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer,updatePasswordUser:updatePasswordUserReducer,updateLastLoginUser:updateLastLoginUserReducer,historyListLogged:historyListLoggedReducer,historyListCoordinator:historyListCoordinatorReducer,//\ncoordinatorsList:coordinatorsListReducer,createCoordinator:createCoordinatorReducer,detailCoordinator:detailCoordinatorReducer,updateCoordinator:updateCoordinatorReducer,//\ncommentCaseList:commentCaseListReducer,createNewCommentCase:createNewCommentCaseReducer,deleteCommentCase:deleteCommentCaseReducer,//\nlogoutSavedUser:logoutSavedUserReducer,resetPassword:resetPasswordReducer,confirmResetPassword:confirmResetPasswordReducer//\n});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,applyMiddleware(...middleware));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "confirmResetPasswordReducer", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "logoutSavedUserReducer", "resetPasswordReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListMapReducer", "caseListProviderReducer", "caseListReducer", "caseHistoryReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "deleteCommentCaseReducer", "detailCaseReducer", "duplicateCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "updateCaseStatusReducer", "updateCaseStepReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "caseListMap", "detailCase", "createNewCase", "deleteCase", "updateCase", "updateCaseStatus", "updateCaseStep", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "duplicateCase", "caseHistory", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "deleteCommentCase", "logoutSavedUser", "resetPassword", "confirmResetPassword", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  confirmResetPasswordReducer,\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  logoutSavedUserReducer,\n  resetPasswordReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListMapReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  caseHistoryReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  deleteCommentCaseReducer,\n  detailCaseReducer,\n  duplicateCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n  updateCaseStatusReducer,\n  updateCaseStepReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  updateCaseStep: updateCaseStepReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer,\n\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,2BAA2B,CAC3BC,uBAAuB,CACvBC,wBAAwB,CACxBC,oBAAoB,CACpBC,iBAAiB,CACjBC,wBAAwB,CACxBC,qBAAqB,CACrBC,6BAA6B,CAC7BC,wBAAwB,CACxBC,sBAAsB,CACtBC,oBAAoB,CACpBC,wBAAwB,CACxBC,0BAA0B,CAC1BC,yBAAyB,CACzBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,0BAA0B,CAC1BC,wBAAwB,CACxBC,qBAAqB,CACrBC,kBAAkB,CAClBC,uBAAuB,CACvBC,eAAe,CACfC,kBAAkB,CAClBC,sBAAsB,CACtBC,oBAAoB,CACpBC,2BAA2B,CAC3BC,iBAAiB,CACjBC,wBAAwB,CACxBC,iBAAiB,CACjBC,oBAAoB,CACpBC,yBAAyB,CACzBC,iBAAiB,CACjBC,uBAAuB,CACvBC,qBAAqB,KAChB,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,qBAAqB,CACrBC,qBAAqB,CACrBC,mBAAmB,CACnBC,qBAAqB,KAChB,6BAA6B,CACpC,OACEC,sBAAsB,CACtBC,sBAAsB,CACtBC,sBAAsB,CACtBC,oBAAoB,CACpBC,sBAAsB,KACjB,8BAA8B,CAErC,KAAM,CAAAC,OAAO,CAAGtD,eAAe,CAAC,CAC9BuD,SAAS,CAAEpC,gBAAgB,CAE3B;AACAqC,QAAQ,CAAEzB,eAAe,CACzB0B,WAAW,CAAE5B,kBAAkB,CAC/B6B,UAAU,CAAEpB,iBAAiB,CAC7BqB,aAAa,CAAEzB,oBAAoB,CACnC0B,UAAU,CAAExB,iBAAiB,CAC7ByB,UAAU,CAAEpB,iBAAiB,CAC7BqB,gBAAgB,CAAEpB,uBAAuB,CACzCqB,cAAc,CAAEpB,qBAAqB,CACrCqB,mBAAmB,CAAEtC,0BAA0B,CAC/CuC,kBAAkB,CAAEzB,yBAAyB,CAC7C0B,iBAAiB,CAAEvC,wBAAwB,CAC3CwC,gBAAgB,CAAErC,uBAAuB,CACzCsC,cAAc,CAAExC,qBAAqB,CACrCyC,aAAa,CAAE9B,oBAAoB,CACnC+B,WAAW,CAAEtC,kBAAkB,CAC/B;AACAuC,YAAY,CAAExB,mBAAmB,CACjCyB,cAAc,CAAE1B,qBAAqB,CACrC2B,cAAc,CAAE7B,qBAAqB,CACrC8B,cAAc,CAAE7B,qBAAqB,CACrC8B,cAAc,CAAE3B,qBAAqB,CACrC;AACA4B,UAAU,CAAEvD,iBAAiB,CAC7BwD,eAAe,CAAEvD,sBAAsB,CACvCwD,YAAY,CAAEtD,mBAAmB,CACjCuD,YAAY,CAAEtD,mBAAmB,CACjCuD,YAAY,CAAEzD,mBAAmB,CACjC;AACA0D,aAAa,CAAE7B,oBAAoB,CACnC8B,eAAe,CAAEjC,sBAAsB,CACvCkC,eAAe,CAAEjC,sBAAsB,CACvCkC,eAAe,CAAEjC,sBAAsB,CACvCkC,eAAe,CAAEhC,sBAAsB,CAEvC;AACAiC,SAAS,CAAElE,gBAAgB,CAC3BmE,aAAa,CAAEhF,oBAAoB,CACnCiF,cAAc,CAAE9E,qBAAqB,CACrC+E,iBAAiB,CAAEvE,wBAAwB,CAC3CwE,UAAU,CAAElF,iBAAiB,CAC7BmF,kBAAkB,CAAE1E,yBAAyB,CAC7C2E,mBAAmB,CAAE5E,0BAA0B,CAC/C6E,iBAAiB,CAAEjF,wBAAwB,CAC3CkF,sBAAsB,CAAEnF,6BAA6B,CACrD;AACAoF,gBAAgB,CAAE1F,uBAAuB,CACzC2F,iBAAiB,CAAE1F,wBAAwB,CAC3C2F,iBAAiB,CAAExF,wBAAwB,CAC3CyF,iBAAiB,CAAEnF,wBAAwB,CAC3C;AACAoF,eAAe,CAAElE,sBAAsB,CACvCmE,oBAAoB,CAAEjE,2BAA2B,CACjDkE,iBAAiB,CAAEhE,wBAAwB,CAC3C;AACAiE,eAAe,CAAEzF,sBAAsB,CACvC0F,aAAa,CAAEzF,oBAAoB,CACnC0F,oBAAoB,CAAEpG,2BAEtB;AACF,CAAC,CAAC,CAEF,KAAM,CAAAqG,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnBvD,SAAS,CAAE,CAAEwD,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAAC9G,KAAK,CAAC,CAE1B,KAAM,CAAA+G,KAAK,CAAGlH,WAAW,CACvBuD,OAAO,CACPwD,YAAY,CACZ7G,eAAe,CAAC,GAAG+G,UAAU,CAC/B,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}