{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { addNewCommentCase, deleteCommentCase, detailCase, duplicateCase, getCaseHistory, getListCommentCase, updateAssignedCase, updateCase, updateCaseStatusOnly } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$assurance_n, _caseInfo$created_use, _caseInfo$created_use2, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$pat, _caseInfo$patient2, _caseInfo$patient3, _caseInfo$case_status, _caseInfo$patient$ful2, _caseInfo$patient4, _caseInfo$patient$bir, _caseInfo$patient5, _caseInfo$patient$pat2, _caseInfo$patient6, _caseInfo$patient$pat3, _caseInfo$patient7, _caseInfo$patient$pat4, _caseInfo$patient8, _caseInfo$patient$pat5, _caseInfo$patient9, _caseInfo$patient$pat6, _caseInfo$patient10, _caseInfo$case_type, _caseInfo$currency_pr, _caseInfo$coordinator3, _caseInfo$case_descri, _caseInfo$assistance_, _caseInfo$medical_rep, _caseInfo$medical_rep2, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$upload_invo2, _caseInfo$assurance_s, _caseInfo$assurance$a2, _caseInfo$assurance2, _caseInfo$assurance_n2, _caseInfo$policy_numb, _caseInfo$upload_auth, _caseInfo$upload_auth2;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n  const [isDuplicate, setIsDuplicate] = useState(false);\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // Edit Status Modal\n  const [showEditStatusModal, setShowEditStatusModal] = useState(false);\n  const [selectedStatuses, setSelectedStatuses] = useState([]);\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCommentCase = useSelector(state => state.commentCaseList);\n  const {\n    comments,\n    loadingCommentCase,\n    errorCommentCase,\n    pages\n  } = listCommentCase;\n  const commentCaseDelete = useSelector(state => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete\n  } = commentCaseDelete;\n  const createCommentCase = useSelector(state => state.createNewCommentCase);\n  const {\n    loadingCommentCaseAdd,\n    successCommentCaseAdd,\n    errorCommentCaseAdd\n  } = createCommentCase;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseAssignedUpdate = useSelector(state => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate\n  } = caseAssignedUpdate;\n  const caseDuplicat = useSelector(state => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate\n  } = caseDuplicat;\n  const caseHistoryState = useSelector(state => state.caseHistory);\n  const {\n    loadingHistory,\n    errorHistory,\n    history,\n    page: historyCurrentPage,\n    pages: historyTotalPages\n  } = caseHistoryState;\n  const caseUpdate = useSelector(state => state.updateCase);\n  const {\n    loadingCaseUpdate,\n    successCaseUpdate,\n    errorCaseUpdate\n  } = caseUpdate;\n  const caseStatusUpdate = useSelector(state => state.updateCaseStatus);\n  const {\n    loadingCaseStatusUpdate,\n    successCaseStatusUpdate,\n    errorCaseStatusUpdate\n  } = caseStatusUpdate;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases-list/edit/\" + caseDuplicate);\n      dispatch({\n        type: \"RESET_DUPLICATE_CASE\"\n      });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // Initialize selected statuses when case info is loaded\n  useEffect(() => {\n    if (caseInfo && caseInfo.case_status) {\n      const currentStatuses = caseInfo.case_status.map(status => status.status_coordination);\n      setSelectedStatuses(currentStatuses);\n    }\n  }, [caseInfo]);\n\n  // Handle successful status update\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setShowEditStatusModal(false);\n      dispatch(detailCase(id)); // Refresh case data\n      toast.success(\"Case status updated successfully!\");\n    }\n  }, [successCaseUpdate, dispatch, id]);\n\n  // Handle successful status-only update\n  useEffect(() => {\n    if (successCaseStatusUpdate) {\n      setShowEditStatusModal(false);\n      dispatch(detailCase(id)); // Refresh case data\n    }\n  }, [successCaseStatusUpdate, dispatch, id]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = tabName => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  const caseStatusColor = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = code => {\n    const patientCurrency = code !== null && code !== void 0 ? code : \"\";\n    const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n    if (foundCurrency) {\n      var _foundCurrency$symbol;\n      return (_foundCurrency$symbol = foundCurrency.symbol) !== null && _foundCurrency$symbol !== void 0 ? _foundCurrency$symbol : code;\n    } else {\n      return code;\n    }\n  };\n  const getSectionIndex = selectItem => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  // Status options for the modal\n  const statusOptions = [{\n    value: \"pending-coordination\",\n    label: \"Pending Coordination\",\n    color: \"text-danger\"\n  }, {\n    value: \"coordinated-missing-m-r\",\n    label: \"Coordinated, Missing M.R.\",\n    color: \"text-[#FFA500]\"\n  }, {\n    value: \"coordinated-missing-invoice\",\n    label: \"Coordinated, Missing Invoice\",\n    color: \"text-[#FFA500]\"\n  }, {\n    value: \"waiting-for-insurance-authorization\",\n    label: \"Waiting for Insurance Authorization\",\n    color: \"text-primary\"\n  }, {\n    value: \"coordinated-patient-not-seen-yet\",\n    label: \"Coordinated, Patient not seen yet\",\n    color: \"text-primary\"\n  }, {\n    value: \"coordination-fee\",\n    label: \"Coordination Fee\",\n    color: \"text-primary\"\n  }, {\n    value: \"coordinated-missing-payment\",\n    label: \"Coordinated, Missing Payment\",\n    color: \"text-primary\"\n  }, {\n    value: \"fully-coordinated\",\n    label: \"Fully Coordinated\",\n    color: \"text-[#008000]\"\n  }, {\n    value: \"failed\",\n    label: \"Failed\",\n    color: \"text-[#d34053]\"\n  }];\n\n  // Handle status checkbox change\n  const handleStatusChange = statusValue => {\n    if (selectedStatuses.includes(statusValue)) {\n      setSelectedStatuses(selectedStatuses.filter(status => status !== statusValue));\n    } else {\n      setSelectedStatuses([...selectedStatuses, statusValue]);\n    }\n  };\n\n  // Handle status update submission\n  const handleUpdateStatus = async () => {\n    try {\n      await dispatch(updateCaseStatusOnly(id, selectedStatuses));\n    } catch (error) {\n      toast.error(\"Failed to update case status\");\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3 bg-white shadow-sm px-4 py-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap items-center justify-between mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 459,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#344054] text-xs font-medium\",\n                    children: \"CIA REF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#0388A6] text-sm font-medium\",\n                    children: (_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#667085]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#344054] text-xs font-medium\",\n                    children: \"Created By\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: (_caseInfo$created_use = (_caseInfo$created_use2 = caseInfo.created_user) === null || _caseInfo$created_use2 === void 0 ? void 0 : _caseInfo$created_use2.full_name) !== null && _caseInfo$created_use !== void 0 ? _caseInfo$created_use : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator, _caseInfo$coordinator2;\n                setSelectCoordinator((_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md transition-colors duration-300 text-xs mt-2 sm:mt-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4 mr-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Assign Coordinator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-3 gap-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), caseInfo.is_pay ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"CIA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#667085] text-sm truncate max-w-[120px]\",\n                  children: (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#667085] text-sm truncate max-w-[120px]\",\n                  children: (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Country\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getIconCountry((_caseInfo$patient$pat = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.patient_country) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\"), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-[#667085] text-sm ml-1 truncate max-w-[100px]\",\n                    children: caseStatus((_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.patient_country)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start col-span-2 sm:col-span-3 md:col-span-2 \",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 mt-0.5 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#344054] text-xs font-medium\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowEditStatusModal(true),\n                    className: \"bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md text-xs font-medium flex items-center transition-colors duration-200 shadow-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 mr-1.5\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 591,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 590,\n                      columnNumber: 25\n                    }, this), \"Edit Status\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-1\",\n                  children: (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`,\n                    children: caseStatus(stat.status_coordination)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",\n              href: \"/cases-list/edit/\" + caseInfo.id + \"?section=\" + getSectionIndex(selectPage),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 628,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 mx-2 border-b border-[#F1F5F9]\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap -mb-px\",\n              children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\", \"History\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleTabChange(select),\n                className: `inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${selectPage === select ? \"border-[#0388A6] text-[#0388A6]\" : \"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"}`,\n                children: select\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 719,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Patient Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Full Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] font-medium\",\n                      children: (_caseInfo$patient$ful2 = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Date of Birth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 734,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$bir = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 735,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 733,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Phone\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$pat2 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_phone) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$pat3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.patient_email) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 745,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 749,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] flex items-center\",\n                      children: [getIconCountry((_caseInfo$patient$pat4 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\"), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-1\",\n                        children: [(_caseInfo$patient$pat5 = (_caseInfo$patient9 = caseInfo.patient) === null || _caseInfo$patient9 === void 0 ? void 0 : _caseInfo$patient9.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"---\", \", \", (_caseInfo$patient$pat6 = (_caseInfo$patient10 = caseInfo.patient) === null || _caseInfo$patient10 === void 0 ? void 0 : _caseInfo$patient10.patient_country) !== null && _caseInfo$patient$pat6 !== void 0 ? _caseInfo$patient$pat6 : \"---\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 752,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 750,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 764,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Case Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 767,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Case Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 773,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 29\n                      }, this), caseInfo.case_type === \"Medical\" && caseInfo.case_type_item && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-1 text-[#667085]\",\n                        children: [\"| \", caseInfo.case_type_item]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 777,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Price of Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] font-medium\",\n                      children: parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 784,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Price (EUR)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 791,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Creation Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: formatDate(caseInfo.case_date)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Assigned Coordinator\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 802,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] flex items-center\",\n                      children: (_caseInfo$coordinator3 = caseInfo.coordinator_user) !== null && _caseInfo$coordinator3 !== void 0 && _caseInfo$coordinator3.full_name ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-[#0388A6] font-medium\",\n                            children: caseInfo.coordinator_user.full_name.charAt(0)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 807,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 806,\n                          columnNumber: 33\n                        }, this), caseInfo.coordinator_user.full_name]\n                      }, void 0, true) : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 820,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] whitespace-pre-wrap\",\n                      children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 821,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"m4.5 12.75 6 6 9-13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 836,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Coordination Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-5\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 850,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 849,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 848,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Current Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 854,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: caseInfo.status_coordination ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`,\n                          children: caseStatus(caseInfo.status_coordination)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 857,\n                          columnNumber: 33\n                        }, this) : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 853,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 870,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 869,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 868,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Last Updated\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 874,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: formatDate(caseInfo.updated_at)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 875,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 888,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 887,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Assistances Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5\",\n                  children: ((_caseInfo$assistance_ = caseInfo.assistance_services) === null || _caseInfo$assistance_ === void 0 ? void 0 : _caseInfo$assistance_.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-6\",\n                    children: caseInfo.assistance_services.map((itemAssistance, index) => {\n                      var _caseInfo$assistance_2, _itemAssistance$creat, _itemAssistance$creat2, _itemAssistance$provi;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center mb-2 sm:mb-0\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-4 h-4 text-[#0388A6]\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 906,\n                                    columnNumber: 41\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 905,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 904,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"font-medium text-[#303030]\",\n                                children: [\"Appointment #\", ((_caseInfo$assistance_2 = caseInfo.assistance_services) === null || _caseInfo$assistance_2 === void 0 ? void 0 : _caseInfo$assistance_2.length) - index]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 909,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 903,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-col sm:flex-row sm:items-center\",\n                              children: [itemAssistance.created_user && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-[#F9FAFB] p-1 rounded-full mr-1.5\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-3 h-3 text-[#667085]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 916,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 915,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 914,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: [\"Created by \", ((_itemAssistance$creat = itemAssistance.created_user) === null || _itemAssistance$creat === void 0 ? void 0 : _itemAssistance$creat.full_name) || ((_itemAssistance$creat2 = itemAssistance.created_user) === null || _itemAssistance$creat2 === void 0 ? void 0 : _itemAssistance$creat2.email) || \"User\", itemAssistance.created_at && /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-[#667085] ml-1\",\n                                    children: [\"on \", formatDate(itemAssistance.created_at)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 922,\n                                    columnNumber: 45\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 919,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 913,\n                                columnNumber: 39\n                              }, this), itemAssistance.appointment_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\",\n                                children: formatDate(itemAssistance.appointment_date)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 930,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 911,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 902,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 901,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"px-5 py-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                              children: [itemAssistance.start_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 947,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 946,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 945,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Hospital Starting Date\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 951,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: formatDate(itemAssistance.start_date)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 952,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 950,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 944,\n                                columnNumber: 39\n                              }, this), itemAssistance.end_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 961,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 960,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 959,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Hospital Ending Date\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 965,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: formatDate(itemAssistance.end_date)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 966,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 964,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 958,\n                                columnNumber: 39\n                              }, this), itemAssistance.service_location && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 975,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 976,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 974,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 973,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Service Location\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 980,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: itemAssistance.service_location\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 981,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 979,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 972,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 942,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 941,\n                            columnNumber: 33\n                          }, this), ((_itemAssistance$provi = itemAssistance.provider_services) === null || _itemAssistance$provi === void 0 ? void 0 : _itemAssistance$provi.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center mb-4\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-4 h-4 text-[#0388A6]\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 994,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 993,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 992,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                                className: \"text-sm font-medium text-[#303030]\",\n                                children: \"Providers\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 997,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 991,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-4\",\n                              children: itemAssistance.provider_services.map((providerService, idx) => {\n                                var _providerService$prov, _providerService$prov2;\n                                return /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\",\n                                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#0388A6]\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1006,\n                                          columnNumber: 49\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1005,\n                                        columnNumber: 47\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1004,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"text-sm font-medium text-[#344054]\",\n                                        children: ((_providerService$prov = providerService.provider) === null || _providerService$prov === void 0 ? void 0 : _providerService$prov.full_name) || \"---\"\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1010,\n                                        columnNumber: 47\n                                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\",\n                                        children: providerService.service_type || \"---\"\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1011,\n                                        columnNumber: 47\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1009,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1003,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\",\n                                    children: [providerService.service_specialist && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1019,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1018,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Speciality\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1022,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: providerService.service_specialist\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1023,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1021,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1017,\n                                      columnNumber: 47\n                                    }, this), providerService.provider_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1031,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1030,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Visit Date\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1034,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: formatDate(providerService.provider_date)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1035,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1033,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1029,\n                                      columnNumber: 47\n                                    }, this), ((_providerService$prov2 = providerService.provider) === null || _providerService$prov2 === void 0 ? void 0 : _providerService$prov2.phone) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1043,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1042,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Contact\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1046,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: providerService.provider.phone\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 1047,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 1045,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 1041,\n                                      columnNumber: 47\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1015,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, idx, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1002,\n                                  columnNumber: 41\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1000,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 990,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 939,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 899,\n                        columnNumber: 29\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl p-8 text-center shadow-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-8 h-8 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1065,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1064,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1063,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-[#303030] font-medium mb-2\",\n                      children: \"No Assistances Informations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1068,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"No assistances details have been added to this case yet.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1084,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Medical Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: ((_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: (_caseInfo$medical_rep2 = caseInfo.medical_reports) === null || _caseInfo$medical_rep2 === void 0 ? void 0 : _caseInfo$medical_rep2.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"w-5 h-5 text-[#0388A6]\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1110,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1111,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1104,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1103,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"overflow-hidden\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-[#667085]\",\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1118,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1114,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1102,\n                    columnNumber: 29\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-8 h-8 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1128,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1127,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1126,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-[#344054] font-medium mb-2\",\n                  children: \"No Medical Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#667085] text-sm\",\n                  children: \"No medical reports have been uploaded for this case yet.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1132,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1145,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1158,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1157,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1156,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Invoice Number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1162,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1163,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1161,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1155,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1170,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1169,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1168,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Date Issued\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1174,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: formatDate(caseInfo.date_issued) || \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1175,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1173,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1167,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1182,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1181,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1180,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Amount\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1186,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: [\"$ \", parseFloat(caseInfo.invoice_amount || 0).toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1187,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1185,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1179,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1154,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1196,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1195,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Due Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1200,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1201,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1199,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"m4.5 12.75 6 6 9-13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1208,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1207,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1206,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Invoice Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1212,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1213,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1211,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1192,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1153,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1224,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1223,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1222,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Uploaded Documents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1227,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1221,\n                  columnNumber: 23\n                }, this), ((_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                  children: (_caseInfo$upload_invo2 = caseInfo.upload_invoices) === null || _caseInfo$upload_invo2 === void 0 ? void 0 : _caseInfo$upload_invo2.map((item, idx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: baseURLFile + item.file,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          className: \"w-5 h-5 text-[#0388A6]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1248,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1249,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1242,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1241,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"overflow-hidden\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                          children: item.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1253,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-[#667085]\",\n                          children: [item.file_size, \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1256,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1252,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1240,\n                      columnNumber: 31\n                    }, this)\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1233,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-6 bg-[#F9FAFB] rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-6 h-6 text-[#667085]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1266,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1265,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1264,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: \"No invoice documents have been uploaded yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1269,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1263,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1152,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1140,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1283,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1282,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1281,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1286,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1280,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1279,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"m4.5 12.75 6 6 9-13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1295,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1294,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1293,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Authorization Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1299,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1300,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1298,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1292,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1307,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1306,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Insurance Company\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1311,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance$a2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.assurance_name) !== null && _caseInfo$assurance$a2 !== void 0 ? _caseInfo$assurance$a2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1310,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1304,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1319,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1318,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1317,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1323,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance_n2 = caseInfo.assurance_number) !== null && _caseInfo$assurance_n2 !== void 0 ? _caseInfo$assurance_n2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1324,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1322,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1316,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1331,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1330,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1329,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1335,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1336,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1334,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1291,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1346,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1345,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1344,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Uploaded Documents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1349,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 23\n                }, this), ((_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                  children: (_caseInfo$upload_auth2 = caseInfo.upload_authorization) === null || _caseInfo$upload_auth2 === void 0 ? void 0 : _caseInfo$upload_auth2.map((item, idx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: baseURLFile + item.file,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          className: \"w-5 h-5 text-[#0388A6]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1370,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1371,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1364,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1363,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"overflow-hidden\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                          children: item.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1375,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-[#667085]\",\n                          children: [item.file_size, \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1378,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1374,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1362,\n                      columnNumber: 31\n                    }, this)\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1355,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1353,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-6 bg-[#F9FAFB] rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-6 h-6 text-[#667085]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1388,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1387,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1386,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: \"No authorization documents have been uploaded yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1385,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1342,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1278,\n            columnNumber: 17\n          }, this) : null, selectPage === \"History\" ? /*#__PURE__*/_jsxDEV(CaseHistory, {\n            historyData: {\n              history: history,\n              page: historyCurrentPage,\n              pages: historyTotalPages,\n              count: (history === null || history === void 0 ? void 0 : history.length) || 0\n            },\n            loading: loadingHistory,\n            error: errorHistory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1401,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4 text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1420,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1419,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-[#344054]\",\n              children: \"Add Comment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-[#344054] mb-1.5\",\n                children: \"Comment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: commentInput,\n                  onChange: v => setCommentInput(v.target.value),\n                  placeholder: \"Type your comment here...\",\n                  className: `w-full min-h-[120px] px-3 py-2 bg-white border ${commentInputError ? \"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\" : \"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"} rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 21\n                }, this), commentInputError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1 text-[#D92D20] text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-3.5 h-3.5 mr-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1446,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1445,\n                    columnNumber: 25\n                  }, this), commentInputError]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1444,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-[#344054] mb-1.5\",\n                children: \"Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1456,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ...getRootComments({\n                  className: \"dropzone\"\n                }),\n                className: \"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  ...getInputComments()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-2 rounded-full mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1475,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1467,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1466,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-[#667085] text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-[#0388A6]\",\n                    children: \"Click to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1483,\n                    columnNumber: 23\n                  }, this), \" or drag and drop\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-[#667085] mt-1\",\n                  children: \"PNG, JPG or JPEG (max. 10MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1485,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1426,\n            columnNumber: 15\n          }, this), (filesComments === null || filesComments === void 0 ? void 0 : filesComments.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xs font-medium text-[#344054] mb-2\",\n              children: \"Uploaded Files\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1495,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\",\n              children: filesComments.map((file, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-md p-2 mr-3 flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: file.preview,\n                    className: \"w-10 h-10 object-cover rounded\",\n                    onError: e => {\n                      e.target.onerror = null;\n                      e.target.src = \"/assets/placeholder.png\";\n                    },\n                    alt: \"Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1503,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1502,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-hidden flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-[#344054] truncate\",\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1514,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-[#667085]\",\n                    children: [(file.size / (1024 * 1024)).toFixed(2), \" MB\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1517,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1513,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => idx !== indexToRemove));\n                  },\n                  className: \"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\",\n                  title: \"Remove file\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#D92D20]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M6 18 18 6M6 6l12 12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1538,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1530,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1521,\n                  columnNumber: 25\n                }, this)]\n              }, file.name + idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1496,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1494,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              disabled: loadingCommentCaseAdd,\n              onClick: () => {\n                var check = true;\n                setCommentInputError(\"\");\n                if (commentInput === \"\" && filesComments.length === 0) {\n                  setCommentInputError(\"Please add a comment or upload an image\");\n                  check = false;\n                }\n                if (check) {\n                  dispatch(addNewCommentCase({\n                    content: commentInput,\n                    files_commet: filesComments\n                  }, id));\n                } else {\n                  toast.error(\"Some fields are empty or invalid. Please try again\");\n                }\n              },\n              className: `inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${loadingCommentCaseAdd ? \"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\" : \"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"}`,\n              children: loadingCommentCaseAdd ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1589,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1590,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1588,\n                  columnNumber: 23\n                }, this), \"Saving...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1597,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1596,\n                  columnNumber: 23\n                }, this), \"Add Comment\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1553,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1552,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8\",\n            children: loadingCommentCase ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-center py-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1609,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1608,\n              columnNumber: 19\n            }, this) : errorCommentCase ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-5 h-5 mr-2\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1614,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1613,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: errorCommentCase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1616,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1612,\n              columnNumber: 19\n            }, this) : comments && comments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: comments === null || comments === void 0 ? void 0 : comments.map((comment, idx) => {\n                var _comment$coordinator, _comment$coordinator2, _comment$coordinator3, _comment$coordinator4, _comment$coordinator5, _comment$coordinator6, _comment$coordinator7, _comment$coordinator8, _comment$files;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mr-3 flex-shrink-0\",\n                      children: comment.coordinator ? (_comment$coordinator = comment.coordinator) !== null && _comment$coordinator !== void 0 && _comment$coordinator.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        className: \"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\",\n                        src: baseURLFile + ((_comment$coordinator2 = comment.coordinator) === null || _comment$coordinator2 === void 0 ? void 0 : _comment$coordinator2.photo),\n                        onError: e => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        },\n                        alt: ((_comment$coordinator3 = comment.coordinator) === null || _comment$coordinator3 === void 0 ? void 0 : _comment$coordinator3.full_name) || \"User\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1626,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-medium\",\n                          children: [(_comment$coordinator4 = comment.coordinator) !== null && _comment$coordinator4 !== void 0 && _comment$coordinator4.first_name ? (_comment$coordinator5 = comment.coordinator) === null || _comment$coordinator5 === void 0 ? void 0 : _comment$coordinator5.first_name[0] : \"\", (_comment$coordinator6 = comment.coordinator) !== null && _comment$coordinator6 !== void 0 && _comment$coordinator6.last_name ? (_comment$coordinator7 = comment.coordinator) === null || _comment$coordinator7 === void 0 ? void 0 : _comment$coordinator7.last_name[0] : \"\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1637,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1636,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          strokeWidth: \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-6 h-6 text-[#667085]\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1650,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1649,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1648,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1623,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium text-[#344054]\",\n                          children: ((_comment$coordinator8 = comment.coordinator) === null || _comment$coordinator8 === void 0 ? void 0 : _comment$coordinator8.full_name) || \"System\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1658,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-white p-1 rounded-full mr-1.5\",\n                            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              strokeWidth: \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"w-3.5 h-3.5 text-[#667085]\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1665,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1664,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1663,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: formatDate(comment.created_at)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1668,\n                            columnNumber: 33\n                          }, this), comment.can_delete && /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => {\n                              setSelectComment(comment.id);\n                              setEventType(\"delete\");\n                              setIsDeleteComment(true);\n                            },\n                            className: \"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              strokeWidth: \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"w-3.5 h-3.5 mr-1\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1680,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1679,\n                              columnNumber: 37\n                            }, this), \"Delete\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1671,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1662,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\",\n                        children: comment.content || \"No content\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1688,\n                        columnNumber: 29\n                      }, this), (comment === null || comment === void 0 ? void 0 : (_comment$files = comment.files) === null || _comment$files === void 0 ? void 0 : _comment$files.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\",\n                        children: comment.files.map((file, fileIdx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                          target: \"_blank\",\n                          rel: \"noopener noreferrer\",\n                          href: baseURLFile + file.file,\n                          className: \"block transition-transform hover:scale-[1.03] duration-200\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: baseURLFile + file.file,\n                              className: \"w-full h-full object-cover\",\n                              onError: e => {\n                                e.target.onerror = null;\n                                e.target.src = \"/assets/placeholder.png\";\n                              },\n                              alt: \"Attachment\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1703,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1712,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1702,\n                            columnNumber: 37\n                          }, this)\n                        }, fileIdx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1695,\n                          columnNumber: 35\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1693,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1656,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1622,\n                    columnNumber: 25\n                  }, this)\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1621,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1619,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-10 bg-[#F9FAFB] rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-8 h-8 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1727,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1726,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1725,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-[#344054] font-medium mb-2\",\n                children: \"No Comments Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1730,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#667085] text-sm\",\n                children: \"Be the first to add a comment to this case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1731,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1724,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1606,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: isDeleteComment,\n      message: eventType === \"delete\" ? \"Are you sure you want to delete this Comment?\" : \"Are you sure ?\",\n      title: eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\",\n      icon: \"delete\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: async () => {\n        if (eventType === \"delete\" && selectComment !== \"\") {\n          dispatch(deleteCommentCase(selectComment));\n          setIsDeleteComment(false);\n          setEventType(\"\");\n        } else {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }\n      },\n      onCancel: () => {\n        setIsDeleteComment(false);\n        setEventType(\"\");\n        setSelectComment(\"\");\n      },\n      loadEvent: loadingCommentCaseDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1740,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: openDiag,\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-[#0388A6]\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          strokeWidth: \"1.5\",\n          stroke: \"currentColor\",\n          className: \"w-5 h-5 mr-2\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1775,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1774,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Assign Case Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1777,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1773,\n        columnNumber: 11\n      }, this),\n      message: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full my-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 text-[#0388A6] mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1784,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1783,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"text-[#0388A6] font-medium text-sm\",\n            children: [\"Assigned Coordinator \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1787,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1786,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1782,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1793,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1792,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1791,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            className: `bg-white border ${selectCoordinatorError ? \"border-red-500 focus:ring-red-500 focus:border-red-500\" : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"} text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`,\n            value: selectCoordinator,\n            onChange: v => setSelectCoordinator(v.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a coordinator...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1805,\n              columnNumber: 17\n            }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: item.id,\n              children: item.full_name\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1807,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1796,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m19.5 8.25-7.5 7.5-7.5-7.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1812,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1811,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1810,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1790,\n          columnNumber: 13\n        }, this), selectCoordinatorError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2 text-red-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 mr-1\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1819,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1818,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: selectCoordinatorError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1821,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1817,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1781,\n        columnNumber: 11\n      }, this),\n      icon: \"info\",\n      confirmText: \"Assign Coordinator\",\n      cancelText: \"Cancel\",\n      confirmButtonClass: \"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\",\n      cancelButtonClass: \"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\",\n      onConfirm: async () => {\n        setSelectCoordinatorError(\"\");\n        if (selectCoordinator === \"\") {\n          setSelectCoordinatorError(\"This field is required.\");\n        } else {\n          setIsLoading(true);\n          await dispatch(updateAssignedCase(id, {\n            coordinator: selectCoordinator\n          }));\n          setIsLoading(false);\n        }\n      },\n      onCancel: () => {\n        setSelectCoordinator(\"\");\n        setSelectCoordinatorError(\"\");\n        setOpenDiag(false);\n        setIsLoading(false);\n      },\n      loadEvent: isLoading,\n      loadingText: \"Assigning coordinator...\",\n      loadingIcon: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"animate-spin h-5 w-5 text-white\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          className: \"opacity-25\",\n          cx: \"12\",\n          cy: \"12\",\n          r: \"10\",\n          stroke: \"currentColor\",\n          strokeWidth: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1854,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          className: \"opacity-75\",\n          fill: \"currentColor\",\n          d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1855,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1853,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1770,\n      columnNumber: 7\n    }, this), showEditStatusModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-[#344054]\",\n              children: \"Edit Case Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1867,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowEditStatusModal(false),\n              className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1873,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1872,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1868,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1866,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1865,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-[#667085] mb-4\",\n            children: \"Select the status(es) that apply to this case:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1881,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: statusOptions.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: option.value,\n                checked: selectedStatuses.includes(option.value),\n                onChange: () => handleStatusChange(option.value),\n                className: \"w-4 h-4 text-[#0388A6] bg-gray-100 border-gray-300 rounded focus:ring-[#0388A6] focus:ring-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1888,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: option.value,\n                className: `ml-3 text-sm font-medium cursor-pointer ${option.color}`,\n                children: option.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1895,\n                columnNumber: 21\n              }, this)]\n            }, option.value, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1885,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1880,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 flex justify-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowEditStatusModal(false),\n            className: \"px-4 py-2 text-sm font-medium text-[#344054] bg-gray-100 hover:bg-gray-200 rounded-md transition-colors duration-200\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1908,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleUpdateStatus,\n            disabled: loadingCaseUpdate,\n            className: \"px-4 py-2 text-sm font-medium text-white bg-[#0388A6] hover:bg-[#026e84] rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n            children: loadingCaseUpdate ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1922,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1923,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1921,\n                columnNumber: 21\n              }, this), \"Updating...\"]\n            }, void 0, true) : 'Update Status'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1914,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1907,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1863,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1862,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"NSFPYdTDmP/5kNJl5msVftJMdNw=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getCaseHistory", "getListCommentCase", "updateAssignedCase", "updateCase", "updateCaseStatusOnly", "DefaultLayout", "Loader", "<PERSON><PERSON>", "CaseHistory", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$patient$pat6", "_caseInfo$patient10", "_caseInfo$case_type", "_caseInfo$currency_pr", "_caseInfo$coordinator3", "_caseInfo$case_descri", "_caseInfo$assistance_", "_caseInfo$medical_rep", "_caseInfo$medical_rep2", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$upload_invo2", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n2", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "_caseInfo$upload_auth2", "navigate", "location", "dispatch", "id", "searchParams", "setSearchParams", "page", "get", "tabParam", "historyPageParam", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "showEditStatusModal", "setShowEditStatusModal", "selectedStatuses", "setSelectedStatuses", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "caseHistoryState", "caseHistory", "loadingHistory", "errorHistory", "history", "historyCurrentPage", "historyTotalPages", "caseUpdate", "loadingCaseUpdate", "successCaseUpdate", "errorCaseUpdate", "caseStatusUpdate", "updateCaseStatus", "loadingCaseStatusUpdate", "successCaseStatusUpdate", "errorCaseStatusUpdate", "redirect", "console", "log", "type", "historyPageFromUrl", "case_status", "currentStatuses", "status", "status_coordination", "success", "handleTabChange", "tabName", "newParams", "URLSearchParams", "set", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "statusOptions", "value", "label", "color", "handleStatusChange", "statusValue", "includes", "filter", "handleUpdateStatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "strokeWidth", "assurance_number", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "stat", "index", "class", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "case_type", "case_type_item", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "char<PERSON>t", "case_description", "updated_at", "assistance_services", "length", "itemAssistance", "_caseInfo$assistance_2", "_itemAssistance$creat", "_itemAssistance$creat2", "_itemAssistance$provi", "email", "created_at", "appointment_date", "start_date", "end_date", "service_location", "provider_services", "providerService", "idx", "_providerService$prov", "_providerService$prov2", "provider", "service_type", "service_specialist", "provider_date", "phone", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "historyData", "count", "onChange", "v", "placeholder", "src", "onError", "e", "onerror", "alt", "name", "size", "_", "indexToRemove", "disabled", "check", "content", "files_commet", "cx", "cy", "r", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator7", "_comment$coordinator8", "_comment$files", "coordinator", "photo", "first_name", "last_name", "can_delete", "files", "fileIdx", "isOpen", "confirmText", "cancelText", "onConfirm", "onCancel", "loadEvent", "confirmButtonClass", "cancelButtonClass", "loadingText", "loadingIcon", "checked", "htmlFor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getCaseHistory,\n  getListCommentCase,\n  updateAssignedCase,\n  updateCase,\n  updateCaseStatusOnly,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // Edit Status Modal\n  const [showEditStatusModal, setShowEditStatusModal] = useState(false);\n  const [selectedStatuses, setSelectedStatuses] = useState([]);\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n\n  const caseHistoryState = useSelector((state) => state.caseHistory);\n  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, successCaseUpdate, errorCaseUpdate } = caseUpdate;\n\n  const caseStatusUpdate = useSelector((state) => state.updateCaseStatus);\n  const { loadingCaseStatusUpdate, successCaseStatusUpdate, errorCaseStatusUpdate } = caseStatusUpdate;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases-list/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // Initialize selected statuses when case info is loaded\n  useEffect(() => {\n    if (caseInfo && caseInfo.case_status) {\n      const currentStatuses = caseInfo.case_status.map(status => status.status_coordination);\n      setSelectedStatuses(currentStatuses);\n    }\n  }, [caseInfo]);\n\n  // Handle successful status update\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setShowEditStatusModal(false);\n      dispatch(detailCase(id)); // Refresh case data\n      toast.success(\"Case status updated successfully!\");\n    }\n  }, [successCaseUpdate, dispatch, id]);\n\n  // Handle successful status-only update\n  useEffect(() => {\n    if (successCaseStatusUpdate) {\n      setShowEditStatusModal(false);\n      dispatch(detailCase(id)); // Refresh case data\n    }\n  }, [successCaseStatusUpdate, dispatch, id]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = (tabName) => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  // Status options for the modal\n  const statusOptions = [\n    { value: \"pending-coordination\", label: \"Pending Coordination\", color: \"text-danger\" },\n    { value: \"coordinated-missing-m-r\", label: \"Coordinated, Missing M.R.\", color: \"text-[#FFA500]\" },\n    { value: \"coordinated-missing-invoice\", label: \"Coordinated, Missing Invoice\", color: \"text-[#FFA500]\" },\n    { value: \"waiting-for-insurance-authorization\", label: \"Waiting for Insurance Authorization\", color: \"text-primary\" },\n    { value: \"coordinated-patient-not-seen-yet\", label: \"Coordinated, Patient not seen yet\", color: \"text-primary\" },\n    { value: \"coordination-fee\", label: \"Coordination Fee\", color: \"text-primary\" },\n    { value: \"coordinated-missing-payment\", label: \"Coordinated, Missing Payment\", color: \"text-primary\" },\n    { value: \"fully-coordinated\", label: \"Fully Coordinated\", color: \"text-[#008000]\" },\n    { value: \"failed\", label: \"Failed\", color: \"text-[#d34053]\" },\n  ];\n\n  // Handle status checkbox change\n  const handleStatusChange = (statusValue) => {\n    if (selectedStatuses.includes(statusValue)) {\n      setSelectedStatuses(selectedStatuses.filter(status => status !== statusValue));\n    } else {\n      setSelectedStatuses([...selectedStatuses, statusValue]);\n    }\n  };\n\n  // Handle status update submission\n  const handleUpdateStatus = async () => {\n    try {\n      await dispatch(updateCaseStatusOnly(id, selectedStatuses));\n    } catch (error) {\n      toast.error(\"Failed to update case status\");\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-3 bg-white shadow-sm px-4 py-4 rounded-lg\">\n              {/* Top Row with CIA REF, Created By, and Assign Button */}\n              <div className=\"flex flex-wrap items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-4\">\n                  {/* CIA REF */}\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <div className=\"text-[#344054] text-xs font-medium\">CIA REF</div>\n                      <div className=\"text-[#0388A6] text-sm font-medium\">{caseInfo.assurance_number ?? \"---\"}</div>\n                    </div>\n                  </div>\n\n                  {/* Created By */}\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <div className=\"text-[#344054] text-xs font-medium\">Created By</div>\n                      <div className=\"text-[#667085] text-sm\">{caseInfo.created_user?.full_name ?? \"---\"}</div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Assign Coordinator Button */}\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md transition-colors duration-300 text-xs mt-2 sm:mt-0\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-4 h-4 mr-1\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg>\n                  <span className=\"font-medium\">Assign Coordinator</span>\n                </button>\n              </div>\n\n              {/* Main Info Grid - 3 columns on all screens */}\n              <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-3 gap-y-2\">\n                {/* Payment Status */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Payment</div>\n                    {caseInfo.is_pay ? (\n                      <span className=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* CIA */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">CIA</div>\n                    <div className=\"text-[#667085] text-sm truncate max-w-[120px]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Patient Name */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Patient</div>\n                    <div className=\"text-[#667085] text-sm truncate max-w-[120px]\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Country */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Country</div>\n                    <div className=\"flex items-center\">\n                      {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                      <span className=\"text-[#667085] text-sm ml-1 truncate max-w-[100px]\">{caseStatus(caseInfo.patient?.patient_country)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Status */}\n                <div className=\"flex items-start col-span-2 sm:col-span-3 md:col-span-2 \">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 mt-0.5 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                    </svg>\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <div className=\"text-[#344054] text-xs font-medium\">Status</div>\n                      <button\n                        onClick={() => setShowEditStatusModal(true)}\n                        className=\"bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md text-xs font-medium flex items-center transition-colors duration-200 shadow-sm\"\n                      >\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1.5\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\" />\n                        </svg>\n                        Edit Status\n                      </button>\n                    </div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {caseInfo.case_status?.map((stat, index) => (\n                        <span key={index} className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`}>\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases-list/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases-list/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"mb-6 mx-2 border-b border-[#F1F5F9]\">\n                <div className=\"flex flex-wrap -mb-px\">\n                  {[\n                    \"General Information\",\n                    \"Coordination Details\",\n                    \"Medical Reports\",\n                    \"Invoices\",\n                    \"Insurance Authorization\",\n                    \"History\",\n                  ].map((select, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleTabChange(select)}\n                      className={`inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${\n                        selectPage === select\n                          ? \"border-[#0388A6] text-[#0388A6]\"\n                          : \"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"\n                      }`}\n                    >\n                      {select}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  {/* Patient Details Section */}\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Patient Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Full Name</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Date of Birth</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.birth_day ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Phone</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_phone ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Email</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_email ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Location</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                            <span className=\"ml-1\">{caseInfo.patient?.patient_city ?? \"---\"}, {caseInfo.patient?.patient_country ?? \"---\"}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Case Details Section */}\n                    <div className=\"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Case Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Case Type</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            <span className=\"font-medium\">{caseInfo.case_type ?? \"---\"}</span>\n                            {caseInfo.case_type === \"Medical\" && caseInfo.case_type_item &&\n                              <span className=\"ml-1 text-[#667085]\">| {caseInfo.case_type_item}</span>\n                            }\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price of Service</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">\n                            {parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price (EUR)</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            {parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Creation Date</div>\n                          <div className=\"text-sm text-[#344054]\">{formatDate(caseInfo.case_date)}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Assigned Coordinator</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {caseInfo.coordinator_user?.full_name ? (\n                              <>\n                                <div className=\"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\">\n                                  <span className=\"text-xs text-[#0388A6] font-medium\">\n                                    {caseInfo.coordinator_user.full_name.charAt(0)}\n                                  </span>\n                                </div>\n                                {caseInfo.coordinator_user.full_name}\n                              </>\n                            ) : (\n                              \"---\"\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Description</div>\n                          <div className=\"text-sm text-[#344054] whitespace-pre-wrap\">\n                            {caseInfo.case_description ?? \"---\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                      <div className=\"flex items-center mb-1\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Coordination Status</h3>\n                      </div>\n                    </div>\n\n                    <div className=\"p-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Current Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">\n                              {caseInfo.status_coordination ? (\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`}>\n                                  {caseStatus(caseInfo.status_coordination)}\n                                </span>\n                              ) : (\n                                \"---\"\n                              )}\n                            </span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Last Updated</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.updated_at)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Assistances Information</h3>\n                        </div>\n                      </div>\n                      <div className=\"p-5\">\n\n                      {caseInfo.assistance_services?.length > 0 ? (\n                        <div className=\"space-y-6\">\n                          {caseInfo.assistance_services.map((itemAssistance, index) => (\n                            <div key={index} className=\"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\">\n                              {/* Assistance Header */}\n                              <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\">\n                                <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center\">\n                                  <div className=\"flex items-center mb-2 sm:mb-0\">\n                                    <div className=\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\">\n                                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                      </svg>\n                                    </div>\n                                    <h4 className=\"font-medium text-[#303030]\">Appointment #{caseInfo.assistance_services?.length-index}</h4>\n                                  </div>\n                                  <div className=\"flex flex-col sm:flex-row sm:items-center\">\n                                    {itemAssistance.created_user && (\n                                      <div className=\"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\">\n                                        <div className=\"bg-[#F9FAFB] p-1 rounded-full mr-1.5\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3 h-3 text-[#667085]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                          </svg>\n                                        </div>\n                                        <span>\n                                          Created by {itemAssistance.created_user?.full_name || itemAssistance.created_user?.email || \"User\"}\n                                          {itemAssistance.created_at && (\n                                            <span className=\"text-[#667085] ml-1\">\n                                              on {formatDate(itemAssistance.created_at)}\n                                            </span>\n                                          )}\n                                        </span>\n                                      </div>\n                                    )}\n                                    {itemAssistance.appointment_date && (\n                                      <div className=\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\">\n                                        {formatDate(itemAssistance.appointment_date)}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Assistance Content */}\n                              <div className=\"px-5 py-4\">\n                                {/* Assistance Details */}\n                                <div className=\"mb-6\">\n                                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                                    {itemAssistance.start_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Starting Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.start_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.end_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Ending Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.end_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.service_location && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Service Location</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{itemAssistance.service_location}</span>\n                                        </div>\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Provider Services */}\n                                {itemAssistance.provider_services?.length > 0 && (\n                                  <div>\n                                    <div className=\"flex items-center mb-4\">\n                                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n                                        </svg>\n                                      </div>\n                                      <h5 className=\"text-sm font-medium text-[#303030]\">Providers</h5>\n                                    </div>\n\n                                    <div className=\"space-y-4\">\n                                      {itemAssistance.provider_services.map((providerService, idx) => (\n                                        <div key={idx} className=\"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\">\n                                          <div className=\"flex items-center mb-3\">\n                                            <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\">\n                                              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                              </svg>\n                                            </div>\n                                            <div>\n                                              <span className=\"text-sm font-medium text-[#344054]\">{providerService.provider?.full_name || \"---\"}</span>\n                                              <span className=\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\">{providerService.service_type || \"---\"}</span>\n                                            </div>\n                                          </div>\n\n                                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\">\n                                            {providerService.service_specialist && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Speciality</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.service_specialist}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider_date && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Visit Date</span>\n                                                  <span className=\"text-sm text-[#344054]\">{formatDate(providerService.provider_date)}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider?.phone && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Contact</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.provider.phone}</span>\n                                                </div>\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"bg-white rounded-xl p-8 text-center shadow-sm\">\n                          <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-[#303030] font-medium mb-2\">No Assistances Informations</h4>\n                          <p className=\"text-gray-500 text-sm\">No assistances details have been added to this case yet.</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  </div>\n                  </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Medical Reports</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    {caseInfo.medical_reports?.length > 0 ? (\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        {caseInfo.medical_reports?.map((item, index) => (\n                          <a\n                            key={index}\n                            href={baseURLFile + item.file}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                          >\n                            <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                              <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  className=\"w-5 h-5 text-[#0388A6]\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"overflow-hidden\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                  {item.file_name}\n                                </div>\n                                <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                              </div>\n                            </div>\n                          </a>\n                        ))}\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-[#344054] font-medium mb-2\">No Medical Reports</h4>\n                        <p className=\"text-[#667085] text-sm\">No medical reports have been uploaded for this case yet.</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Invoice Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Number</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.invoice_number ?? \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Date Issued</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.date_issued) || \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Amount</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">$ {parseFloat(caseInfo.invoice_amount || 0).toFixed(2)}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Due Date</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_invoices?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_invoices?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No invoice documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Insurance Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Authorization Status</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_status ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Insurance Company</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">CIA Reference</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Policy Number</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.policy_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_authorization?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_authorization?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No authorization documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/* \"History\" */}\n              {selectPage === \"History\" ? (\n                <CaseHistory\n                  historyData={{\n                    history: history,\n                    page: historyCurrentPage,\n                    pages: historyTotalPages,\n                    count: history?.length || 0\n                  }}\n                  loading={loadingHistory}\n                  error={errorHistory}\n                />\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-sm font-medium text-[#344054]\">Add Comment</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Comment Input */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Comment\n                  </label>\n                  <div className=\"relative\">\n                    <textarea\n                      value={commentInput}\n                      onChange={(v) => setCommentInput(v.target.value)}\n                      placeholder=\"Type your comment here...\"\n                      className={`w-full min-h-[120px] px-3 py-2 bg-white border ${\n                        commentInputError\n                          ? \"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\"\n                          : \"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"\n                      } rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`}\n                    ></textarea>\n                    {commentInputError && (\n                      <div className=\"flex items-center mt-1 text-[#D92D20] text-xs\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                        </svg>\n                        {commentInputError}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Image Upload */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Images\n                  </label>\n                  <div\n                    {...getRootComments({\n                      className: \"dropzone\",\n                    })}\n                    className=\"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\"\n                  >\n                    <input {...getInputComments()} />\n                    <div className=\"bg-[#E6F4F7] p-2 rounded-full mb-2\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        strokeWidth=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"w-5 h-5 text-[#0388A6]\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        />\n                      </svg>\n                    </div>\n                    <p className=\"text-sm text-[#667085] text-center\">\n                      <span className=\"font-medium text-[#0388A6]\">Click to upload</span> or drag and drop\n                    </p>\n                    <p className=\"text-xs text-[#667085] mt-1\">\n                      PNG, JPG or JPEG (max. 10MB)\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Uploaded Files Preview */}\n              {filesComments?.length > 0 && (\n                <div className=\"mt-6\">\n                  <h4 className=\"text-xs font-medium text-[#344054] mb-2\">Uploaded Files</h4>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\">\n                    {filesComments.map((file, idx) => (\n                      <div\n                        className=\"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\"\n                        key={file.name + idx}\n                      >\n                        <div className=\"bg-white rounded-md p-2 mr-3 flex-shrink-0\">\n                          <img\n                            src={file.preview}\n                            className=\"w-10 h-10 object-cover rounded\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                            alt=\"Preview\"\n                          />\n                        </div>\n                        <div className=\"overflow-hidden flex-1\">\n                          <div className=\"text-sm font-medium text-[#344054] truncate\">\n                            {file.name}\n                          </div>\n                          <div className=\"text-xs text-[#667085]\">\n                            {(file.size / (1024 * 1024)).toFixed(2)} MB\n                          </div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter((_, indexToRemove) => idx !== indexToRemove)\n                            );\n                          }}\n                          className=\"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\"\n                          title=\"Remove file\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            strokeWidth=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"w-4 h-4 text-[#D92D20]\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Submit Button */}\n              <div className=\"mt-6\">\n                <button\n                  disabled={loadingCommentCaseAdd}\n                  onClick={() => {\n                    var check = true;\n                    setCommentInputError(\"\");\n\n                    if (commentInput === \"\" && filesComments.length === 0) {\n                      setCommentInputError(\"Please add a comment or upload an image\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      dispatch(\n                        addNewCommentCase(\n                          {\n                            content: commentInput,\n                            files_commet: filesComments,\n                          },\n                          id\n                        )\n                      );\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. Please try again\"\n                      );\n                    }\n                  }}\n                  className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${\n                    loadingCommentCaseAdd\n                      ? \"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\"\n                      : \"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"\n                  }`}\n                >\n                  {loadingCommentCaseAdd ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Saving...\n                    </>\n                  ) : (\n                    <>\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-2\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                      Add Comment\n                    </>\n                  )}\n                </button>\n              </div>\n\n              {/* Comments List */}\n              <div className=\"mt-8\">\n                {loadingCommentCase ? (\n                  <div className=\"flex justify-center items-center py-8\">\n                    <div className=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"></div>\n                  </div>\n                ) : errorCommentCase ? (\n                  <div className=\"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                    </svg>\n                    <span>{errorCommentCase}</span>\n                  </div>\n                ) : comments && comments.length > 0 ? (\n                  <div className=\"space-y-6\">\n                    {comments?.map((comment, idx) => (\n                      <div key={idx} className=\"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\">\n                        <div className=\"flex items-start\">\n                          <div className=\"mr-3 flex-shrink-0\">\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                  alt={comment.coordinator?.full_name || \"User\"}\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\">\n                                  <span className=\"text-lg font-medium\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </span>\n                                </div>\n                              )\n                            ) : (\n                              <div className=\"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                </svg>\n                              </div>\n                            )}\n                          </div>\n\n                          <div className=\"flex-1\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-2\">\n                              <div className=\"font-medium text-[#344054]\">\n                                {comment.coordinator?.full_name || \"System\"}\n                              </div>\n\n                              <div className=\"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\">\n                                <div className=\"bg-white p-1 rounded-full mr-1.5\">\n                                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 text-[#667085]\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                                  </svg>\n                                </div>\n                                <span>{formatDate(comment.created_at)}</span>\n\n                                {comment.can_delete && (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\"\n                                  >\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\" />\n                                    </svg>\n                                    Delete\n                                  </button>\n                                )}\n                              </div>\n                            </div>\n\n                            <div className=\"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\">\n                              {comment.content || \"No content\"}\n                            </div>\n\n                            {comment?.files?.length > 0 && (\n                              <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\">\n                                {comment.files.map((file, fileIdx) => (\n                                  <a\n                                    key={fileIdx}\n                                    target=\"_blank\"\n                                    rel=\"noopener noreferrer\"\n                                    href={baseURLFile + file.file}\n                                    className=\"block transition-transform hover:scale-[1.03] duration-200\"\n                                  >\n                                    <div className=\"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\">\n                                      <img\n                                        src={baseURLFile + file.file}\n                                        className=\"w-full h-full object-cover\"\n                                        onError={(e) => {\n                                          e.target.onerror = null;\n                                          e.target.src = \"/assets/placeholder.png\";\n                                        }}\n                                        alt=\"Attachment\"\n                                      />\n                                      <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"></div>\n                                    </div>\n                                  </a>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-10 bg-[#F9FAFB] rounded-lg\">\n                    <div className=\"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#667085]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-[#344054] font-medium mb-2\">No Comments Yet</h4>\n                    <p className=\"text-[#667085] text-sm\">Be the first to add a comment to this case</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        title={eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\"}\n        icon=\"delete\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      <ConfirmationModal\n        isOpen={openDiag}\n        title={\n          <div className=\"flex items-center text-[#0388A6]\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n            </svg>\n            <span>Assign Case Coordinator</span>\n          </div>\n        }\n        message={\n          <div className=\"w-full my-4\">\n            <div className=\"flex items-center mb-2\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6] mr-2\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n              </svg>\n              <label className=\"text-[#0388A6] font-medium text-sm\">\n                Assigned Coordinator <span className=\"text-red-500\">*</span>\n              </label>\n            </div>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                </svg>\n              </div>\n              <select\n                className={`bg-white border ${\n                  selectCoordinatorError\n                    ? \"border-red-500 focus:ring-red-500 focus:border-red-500\"\n                    : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"\n                } text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}\n                value={selectCoordinator}\n                onChange={(v) => setSelectCoordinator(v.target.value)}\n              >\n                <option value={\"\"}>Select a coordinator...</option>\n                {coordinators?.map((item) => (\n                  <option key={item.id} value={item.id}>{item.full_name}</option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m19.5 8.25-7.5 7.5-7.5-7.5\" />\n                </svg>\n              </div>\n            </div>\n            {selectCoordinatorError && (\n              <div className=\"flex items-center mt-2 text-red-500\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                </svg>\n                <span className=\"text-xs\">{selectCoordinatorError}</span>\n              </div>\n            )}\n          </div>\n        }\n        icon=\"info\"\n        confirmText=\"Assign Coordinator\"\n        cancelText=\"Cancel\"\n        confirmButtonClass=\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\"\n        cancelButtonClass=\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\"\n        onConfirm={async () => {\n          setSelectCoordinatorError(\"\");\n\n          if (selectCoordinator === \"\") {\n            setSelectCoordinatorError(\"This field is required.\");\n          } else {\n            setIsLoading(true);\n            await dispatch(\n              updateAssignedCase(id, { coordinator: selectCoordinator })\n            );\n            setIsLoading(false);\n          }\n        }}\n        onCancel={() => {\n          setSelectCoordinator(\"\");\n          setSelectCoordinatorError(\"\");\n          setOpenDiag(false);\n          setIsLoading(false);\n        }}\n        loadEvent={isLoading}\n        loadingText=\"Assigning coordinator...\"\n        loadingIcon={\n          <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        }\n      />\n\n      {/* Edit Status Modal */}\n      {showEditStatusModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-xl shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto\">\n            {/* Modal Header */}\n            <div className=\"px-6 py-4 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold text-[#344054]\">Edit Case Status</h3>\n                <button\n                  onClick={() => setShowEditStatusModal(false)}\n                  className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* Modal Body */}\n            <div className=\"px-6 py-4\">\n              <p className=\"text-sm text-[#667085] mb-4\">\n                Select the status(es) that apply to this case:\n              </p>\n\n              <div className=\"space-y-3\">\n                {statusOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id={option.value}\n                      checked={selectedStatuses.includes(option.value)}\n                      onChange={() => handleStatusChange(option.value)}\n                      className=\"w-4 h-4 text-[#0388A6] bg-gray-100 border-gray-300 rounded focus:ring-[#0388A6] focus:ring-2\"\n                    />\n                    <label\n                      htmlFor={option.value}\n                      className={`ml-3 text-sm font-medium cursor-pointer ${option.color}`}\n                    >\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Modal Footer */}\n            <div className=\"px-6 py-4 border-t border-gray-200 flex justify-end space-x-3\">\n              <button\n                onClick={() => setShowEditStatusModal(false)}\n                className=\"px-4 py-2 text-sm font-medium text-[#344054] bg-gray-100 hover:bg-gray-200 rounded-md transition-colors duration-200\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleUpdateStatus}\n                disabled={loadingCaseUpdate}\n                className=\"px-4 py-2 text-sm font-medium text-white bg-[#0388A6] hover:bg-[#026e84] rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n              >\n                {loadingCaseUpdate ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Updating...\n                  </>\n                ) : (\n                  'Update Status'\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,kBAAkB,EAClBC,UAAU,EACVC,oBAAoB,QACf,iCAAiC;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAEvE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAMC,QAAQ,GAAG7E,WAAW,CAAC,CAAC;EAC9B,MAAM8E,QAAQ,GAAG/E,WAAW,CAAC,CAAC;EAC9B,MAAMgF,QAAQ,GAAGlF,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEmF;EAAG,CAAC,GAAG/E,SAAS,CAAC,CAAC;EACxB,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGhF,eAAe,CAAC,CAAC;EACzD,MAAMiF,IAAI,GAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB;EACjE,MAAME,gBAAgB,GAAGL,YAAY,CAACG,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG;EAE/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACiG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACmG,UAAU,EAAEC,aAAa,CAAC,GAAGpG,QAAQ,CAACyF,QAAQ,CAAC;EACtD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACyG,WAAW,EAAEC,cAAc,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM,CAAC2G,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6G,aAAa,EAAEC,gBAAgB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+G,SAAS,EAAEC,YAAY,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACiH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlH,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA;EACA,MAAM,CAACqH,aAAa,EAAEC,gBAAgB,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEuH,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtEnG,WAAW,CAAC;IACVoG,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJjI,SAAS,CAAC,MAAM;IACd,OAAO,MACLsH,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGtI,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAG3I,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAAChI,UAAU,CAAC;EAC3D,MAAM;IAAEqI,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,eAAe,GAAGhJ,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACU,eAAe,CAAC;EACrE,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAC7DL,eAAe;EAEjB,MAAMM,iBAAiB,GAAGtJ,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACjI,iBAAiB,CAAC;EACzE,MAAM;IACJiJ,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC,GAAGH,iBAAiB;EAErB,MAAMI,iBAAiB,GAAG1J,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACoB,oBAAoB,CAAC;EAC5E,MAAM;IAAEC,qBAAqB;IAAEC,qBAAqB;IAAEC;EAAoB,CAAC,GACzEJ,iBAAiB;EAEnB,MAAMK,gBAAgB,GAAG/J,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACyB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,kBAAkB,GAAGpK,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAAC8B,kBAAkB,CAAC;EAC3E,MAAM;IACJC,yBAAyB;IACzBC,uBAAuB;IACvBC;EACF,CAAC,GAAGJ,kBAAkB;EAEtB,MAAMK,YAAY,GAAGzK,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAAC/H,aAAa,CAAC;EAChE,MAAM;IACJkK,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,YAAY;EAEhB,MAAMK,gBAAgB,GAAG9K,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACwC,WAAW,CAAC;EAClE,MAAM;IAAEC,cAAc;IAAEC,YAAY;IAAEC,OAAO;IAAE7F,IAAI,EAAE8F,kBAAkB;IAAE9B,KAAK,EAAE+B;EAAkB,CAAC,GAAGN,gBAAgB;EAEtH,MAAMO,UAAU,GAAGrL,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAAC3H,UAAU,CAAC;EAC3D,MAAM;IAAE0K,iBAAiB;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,gBAAgB,GAAGzL,WAAW,CAAEuI,KAAK,IAAKA,KAAK,CAACmD,gBAAgB,CAAC;EACvE,MAAM;IAAEC,uBAAuB;IAAEC,uBAAuB;IAAEC;EAAsB,CAAC,GAAGJ,gBAAgB;;EAEpG;EACA;EACA,MAAMK,QAAQ,GAAG,GAAG;EACpBjM,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2I,QAAQ,EAAE;MACbzD,QAAQ,CAAC+G,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAACxD,QAAQ,CAAC;MAErBvD,QAAQ,CAAC1E,UAAU,CAAC2E,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACvE,kBAAkB,CAAC,GAAG,EAAEwE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACwD,QAAQ,EAAEyD,QAAQ,EAAEvD,QAAQ,EAAEC,EAAE,EAAEG,IAAI,CAAC,CAAC;EAE5CxF,SAAS,CAAC,MAAM;IACd,IAAIgK,qBAAqB,EAAE;MACzBzD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBc,gBAAgB,CAAC,EAAE,CAAC;MACpBnC,QAAQ,CAACvE,kBAAkB,CAAC,GAAG,EAAEwE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAAC2E,qBAAqB,CAAC,CAAC;EAE3BhK,SAAS,CAAC,MAAM;IACd,IAAI2J,wBAAwB,EAAE;MAC5BpD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBc,gBAAgB,CAAC,EAAE,CAAC;MACpBnC,QAAQ,CAACvE,kBAAkB,CAAC,GAAG,EAAEwE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACsE,wBAAwB,CAAC,CAAC;EAE9B3J,SAAS,CAAC,MAAM;IACd,IAAI+K,oBAAoB,IAAIC,aAAa,EAAE;MACzC9F,QAAQ,CAAC,mBAAmB,GAAG8F,aAAa,CAAC;MAC7C5F,QAAQ,CAAC;QAAEgH,IAAI,EAAE;MAAuB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACrB,oBAAoB,EAAEC,aAAa,CAAC,CAAC;;EAEzC;EACAhL,SAAS,CAAC,MAAM;IACd,OAAO,MAAM2G,cAAc,CAAC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN3G,SAAS,CAAC,MAAM;IACd,IAAI2K,yBAAyB,EAAE;MAC7B1E,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BJ,WAAW,CAAC,KAAK,CAAC;MAClBX,QAAQ,CAAC1E,UAAU,CAAC2E,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACvE,kBAAkB,CAAC,GAAG,EAAEwE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACiJ,yBAAyB,CAAC,CAAC;;EAE/B;EACA3K,SAAS,CAAC,MAAM;IACd,IAAIoG,UAAU,KAAK,SAAS,IAAIf,EAAE,EAAE;MAClC;MACA,MAAMgH,kBAAkB,GAAG/G,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;MAC1DL,QAAQ,CAACxE,cAAc,CAACyE,EAAE,EAAEgH,kBAAkB,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACjG,UAAU,EAAEf,EAAE,EAAED,QAAQ,EAAEE,YAAY,CAAC,CAAC;;EAE5C;EACAtF,SAAS,CAAC,MAAM;IACd,IAAIkJ,QAAQ,IAAIA,QAAQ,CAACoD,WAAW,EAAE;MACpC,MAAMC,eAAe,GAAGrD,QAAQ,CAACoD,WAAW,CAACtE,GAAG,CAACwE,MAAM,IAAIA,MAAM,CAACC,mBAAmB,CAAC;MACtFpF,mBAAmB,CAACkF,eAAe,CAAC;IACtC;EACF,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;;EAEd;EACAlJ,SAAS,CAAC,MAAM;IACd,IAAI0L,iBAAiB,EAAE;MACrBvE,sBAAsB,CAAC,KAAK,CAAC;MAC7B/B,QAAQ,CAAC1E,UAAU,CAAC2E,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B5D,KAAK,CAACiL,OAAO,CAAC,mCAAmC,CAAC;IACpD;EACF,CAAC,EAAE,CAAChB,iBAAiB,EAAEtG,QAAQ,EAAEC,EAAE,CAAC,CAAC;;EAErC;EACArF,SAAS,CAAC,MAAM;IACd,IAAI+L,uBAAuB,EAAE;MAC3B5E,sBAAsB,CAAC,KAAK,CAAC;MAC7B/B,QAAQ,CAAC1E,UAAU,CAAC2E,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAAC0G,uBAAuB,EAAE3G,QAAQ,EAAEC,EAAE,CAAC,CAAC;;EAE3C;EACA;;EAEA;EACA,MAAMsH,eAAe,GAAIC,OAAO,IAAK;IACnCvG,aAAa,CAACuG,OAAO,CAAC;;IAEtB;IACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACxH,YAAY,CAAC;IACnDuH,SAAS,CAACE,GAAG,CAAC,KAAK,EAAEH,OAAO,CAAC;IAC7BrH,eAAe,CAACsH,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,UAAU,IAAK;IACtC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,yBAAyB;QAC5B,OAAO,gBAAgB;MACzB,KAAK,6BAA6B;QAChC,OAAO,gBAAgB;MACzB,KAAK,qCAAqC;QACxC,OAAO,cAAc;MACvB,KAAK,kCAAkC;QACrC,OAAO,cAAc;MACvB,KAAK,mBAAmB;QACtB,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGvM,SAAS,CAACwM,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,eAAe,GAAGD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;IAElC,MAAME,aAAa,GAAG9M,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuM,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACI,IAAI,KAAKC,eAC9B,CAAC;IAED,IAAIC,aAAa,EAAE;MAAA,IAAAC,qBAAA;MACjB,QAAAA,qBAAA,GAAOD,aAAa,CAACE,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAIH,IAAI;IACrC,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC;EAED,MAAMK,eAAe,GAAIC,UAAU,IAAK;IACtC,IAAIA,UAAU,KAAK,qBAAqB,EAAE;MACxC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,sBAAsB,EAAE;MAChD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MAC3C,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MACpC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,yBAAyB,EAAE;MACnD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,SAAS,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAc,CAAC,EACtF;IAAEF,KAAK,EAAE,yBAAyB;IAAEC,KAAK,EAAE,2BAA2B;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACjG;IAAEF,KAAK,EAAE,6BAA6B;IAAEC,KAAK,EAAE,8BAA8B;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACxG;IAAEF,KAAK,EAAE,qCAAqC;IAAEC,KAAK,EAAE,qCAAqC;IAAEC,KAAK,EAAE;EAAe,CAAC,EACrH;IAAEF,KAAK,EAAE,kCAAkC;IAAEC,KAAK,EAAE,mCAAmC;IAAEC,KAAK,EAAE;EAAe,CAAC,EAChH;IAAEF,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC/E;IAAEF,KAAK,EAAE,6BAA6B;IAAEC,KAAK,EAAE,8BAA8B;IAAEC,KAAK,EAAE;EAAe,CAAC,EACtG;IAAEF,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAiB,CAAC,EACnF;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAiB,CAAC,CAC9D;;EAED;EACA,MAAMC,kBAAkB,GAAIC,WAAW,IAAK;IAC1C,IAAI3H,gBAAgB,CAAC4H,QAAQ,CAACD,WAAW,CAAC,EAAE;MAC1C1H,mBAAmB,CAACD,gBAAgB,CAAC6H,MAAM,CAACzC,MAAM,IAAIA,MAAM,KAAKuC,WAAW,CAAC,CAAC;IAChF,CAAC,MAAM;MACL1H,mBAAmB,CAAC,CAAC,GAAGD,gBAAgB,EAAE2H,WAAW,CAAC,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAM9J,QAAQ,CAACpE,oBAAoB,CAACqE,EAAE,EAAE+B,gBAAgB,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdpH,KAAK,CAACoH,KAAK,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,oBACE/G,OAAA,CAACb,aAAa;IAAAkO,QAAA,gBACZrN,OAAA;MAAKsN,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACfrN,OAAA;QAAKsN,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDrN,OAAA;UAAGuN,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBrN,OAAA;YAAKsN,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DrN,OAAA;cACEwN,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBrN,OAAA;gBACE4N,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlO,OAAA;cAAMsN,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJlO,OAAA;UAAAqN,QAAA,eACErN,OAAA;YACEwN,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBrN,OAAA;cACE4N,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlO,OAAA;UAAGuN,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnBrN,OAAA;YAAKsN,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJlO,OAAA;UAAAqN,QAAA,eACErN,OAAA;YACEwN,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBrN,OAAA;cACE4N,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPlO,OAAA;UAAKsN,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGLjH,eAAe,gBACdjH,OAAA,CAACZ,MAAM;QAAA2O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRhH,aAAa,gBACflH,OAAA,CAACX,KAAK;QAACiL,IAAI,EAAE,OAAQ;QAAC6D,OAAO,EAAEjH;MAAc;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9C9G,QAAQ,gBACVpH,OAAA;QAAAqN,QAAA,gBAEErN,OAAA;UAAKsN,SAAS,EAAC,8CAA8C;UAAAD,QAAA,gBAE3DrN,OAAA;YAAKsN,SAAS,EAAC,kDAAkD;YAAAD,QAAA,gBAC/DrN,OAAA;cAAKsN,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAE1CrN,OAAA;gBAAKsN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChCrN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA8M;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAAqN,QAAA,gBACErN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjElO,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,GAAA3M,qBAAA,GAAE0G,QAAQ,CAACiH,gBAAgB,cAAA3N,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAqN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlO,OAAA;gBAAKsN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChCrN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA2J;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAAqN,QAAA,gBACErN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpElO,OAAA;oBAAKsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,GAAA1M,qBAAA,IAAAC,sBAAA,GAAEwG,QAAQ,CAACkH,YAAY,cAAA1N,sBAAA,uBAArBA,sBAAA,CAAuB2N,SAAS,cAAA5N,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAAoN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cACEwO,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAC,qBAAA,EAAAC,sBAAA;gBACbvK,oBAAoB,EAAAsK,qBAAA,IAAAC,sBAAA,GAACtH,QAAQ,CAACuH,gBAAgB,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BnL,EAAE,cAAAkL,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;gBACzDpK,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACFuJ,SAAS,EAAC,yIAAyI;cAAAD,QAAA,gBAEnJrN,OAAA;gBACEwN,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnBU,WAAW,EAAC,KAAK;gBACjBT,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAExBrN,OAAA;kBACE4N,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2X;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9X;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNlO,OAAA;gBAAMsN,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlO,OAAA;YAAKsN,SAAS,EAAC,gEAAgE;YAAAD,QAAA,gBAE7ErN,OAAA;cAAKsN,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCrN,OAAA;gBAAKsN,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/DrN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkf;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACviB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAAqN,QAAA,gBACErN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAChE9G,QAAQ,CAACwH,MAAM,gBACd5O,OAAA;kBAAMsN,SAAS,EAAC,qGAAqG;kBAAAD,QAAA,EAAC;gBAEtH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEPlO,OAAA;kBAAMsN,SAAS,EAAC,qGAAqG;kBAAAD,QAAA,EAAC;gBAEtH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKsN,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCrN,OAAA;gBAAKsN,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/DrN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAoI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAAqN,QAAA,gBACErN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DlO,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,GAAAxM,qBAAA,IAAAC,mBAAA,GAAEsG,QAAQ,CAACyH,SAAS,cAAA/N,mBAAA,uBAAlBA,mBAAA,CAAoBgO,cAAc,cAAAjO,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAkN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKsN,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCrN,OAAA;gBAAKsN,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/DrN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9M;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAAqN,QAAA,gBACErN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjElO,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,GAAAtM,qBAAA,IAAAC,iBAAA,GAAEoG,QAAQ,CAAC2H,OAAO,cAAA/N,iBAAA,uBAAhBA,iBAAA,CAAkBuN,SAAS,cAAAxN,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAgN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKsN,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChCrN,OAAA;gBAAKsN,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/DrN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/FlO,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAAqN,QAAA,gBACErN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjElO,OAAA;kBAAKsN,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,GAC/BxB,cAAc,EAAA5K,qBAAA,IAAAC,kBAAA,GAACkG,QAAQ,CAAC2H,OAAO,cAAA7N,kBAAA,uBAAhBA,kBAAA,CAAkB8N,eAAe,cAAA/N,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,eACxDjB,OAAA;oBAAMsN,SAAS,EAAC,oDAAoD;oBAAAD,QAAA,EAAE3B,UAAU,EAAAvK,kBAAA,GAACiG,QAAQ,CAAC2H,OAAO,cAAA5N,kBAAA,uBAAhBA,kBAAA,CAAkB6N,eAAe;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAKsN,SAAS,EAAC,0DAA0D;cAAAD,QAAA,gBACvErN,OAAA;gBAAKsN,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,eACtErN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAKsN,SAAS,EAAC,QAAQ;gBAAAD,QAAA,gBACrBrN,OAAA;kBAAKsN,SAAS,EAAC,wCAAwC;kBAAAD,QAAA,gBACrDrN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChElO,OAAA;oBACEwO,OAAO,EAAEA,CAAA,KAAMnJ,sBAAsB,CAAC,IAAI,CAAE;oBAC5CiI,SAAS,EAAC,kJAAkJ;oBAAAD,QAAA,gBAE5JrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,eACxIrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAkQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvT,CAAC,eAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNlO,OAAA;kBAAKsN,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,GAAAjM,qBAAA,GAClCgG,QAAQ,CAACoD,WAAW,cAAApJ,qBAAA,uBAApBA,qBAAA,CAAsB8E,GAAG,CAAC,CAAC+I,IAAI,EAAEC,KAAK,kBACrClP,OAAA;oBAAkBsN,SAAS,EAAG,2EAA0E1B,eAAe,CAACqD,IAAI,CAACtE,mBAAmB,CAAE,EAAE;oBAAA0C,QAAA,EACjJ3B,UAAU,CAACuD,IAAI,CAACtE,mBAAmB;kBAAC,GAD5BuE,KAAK;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlO,OAAA;UAAKsN,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvDrN,OAAA;YAAKsN,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzCrN,OAAA;cACEsN,SAAS,EAAC,mGAAmG;cAC7GC,IAAI,EACF,mBAAmB,GACnBnG,QAAQ,CAAC7D,EAAE,GACX,WAAW,GACXmJ,eAAe,CAACpI,UAAU,CAC3B;cAAA+I,QAAA,gBAEDrN,OAAA;gBAAAqN,QAAA,eACErN,OAAA;kBACEwN,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBwB,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,eAEdrN,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvB8N,CAAC,EAAC;kBAA2gB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9gB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPlO,OAAA;gBAAMsN,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDD,CAAC,eACNlO,OAAA;YAAKsN,SAAS,EAAC,qCAAqC;YAAAD,QAAA,eAClDrN,OAAA;cAAKsN,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EACnC,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,EACzB,SAAS,CACV,CAACnH,GAAG,CAAC,CAACkJ,MAAM,EAAEF,KAAK,kBAClBlP,OAAA;gBAEEwO,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAACuE,MAAM,CAAE;gBACvC9B,SAAS,EAAG,oGACVhJ,UAAU,KAAK8K,MAAM,GACjB,iCAAiC,GACjC,+EACL,EAAE;gBAAA/B,QAAA,EAEF+B;cAAM,GARFF,KAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL5J,UAAU,KAAK,qBAAqB,gBACnCtE,OAAA;YAAKsN,SAAS,EAAC,yDAAyD;YAAAD,QAAA,eAEtErN,OAAA;cAAKsN,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxCrN,OAAA;gBAAKsN,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9BrN,OAAA;kBAAKsN,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjErN,OAAA;oBAAKsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCrN,OAAA;sBAAKsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAyJ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9M;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAIsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAe;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5BrN,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5DlO,OAAA;sBAAKsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAhM,sBAAA,IAAAC,kBAAA,GAAE8F,QAAQ,CAAC2H,OAAO,cAAAzN,kBAAA,uBAAhBA,kBAAA,CAAkBiN,SAAS,cAAAlN,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAA0M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChElO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAA9L,qBAAA,IAAAC,kBAAA,GAAE4F,QAAQ,CAAC2H,OAAO,cAAAvN,kBAAA,uBAAhBA,kBAAA,CAAkB6N,SAAS,cAAA9N,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAwM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAK;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDlO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAA5L,sBAAA,IAAAC,kBAAA,GAAE0F,QAAQ,CAAC2H,OAAO,cAAArN,kBAAA,uBAAhBA,kBAAA,CAAkB4N,aAAa,cAAA7N,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAAsM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAK;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxDlO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAA1L,sBAAA,IAAAC,kBAAA,GAAEwF,QAAQ,CAAC2H,OAAO,cAAAnN,kBAAA,uBAAhBA,kBAAA,CAAkB2N,aAAa,cAAA5N,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAAoM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3DlO,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,GACtDxB,cAAc,EAAAhK,sBAAA,IAAAC,kBAAA,GAACsF,QAAQ,CAAC2H,OAAO,cAAAjN,kBAAA,uBAAhBA,kBAAA,CAAkBkN,eAAe,cAAAnN,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC,eACxD7B,OAAA;wBAAMsN,SAAS,EAAC,MAAM;wBAAAD,QAAA,IAAAtL,sBAAA,IAAAC,kBAAA,GAAEoF,QAAQ,CAAC2H,OAAO,cAAA/M,kBAAA,uBAAhBA,kBAAA,CAAkBwN,YAAY,cAAAzN,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAC,IAAE,GAAAE,sBAAA,IAAAC,mBAAA,GAACkF,QAAQ,CAAC2H,OAAO,cAAA7M,mBAAA,uBAAhBA,mBAAA,CAAkB8M,eAAe,cAAA/M,sBAAA,cAAAA,sBAAA,GAAI,KAAK;sBAAA;wBAAA8L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlO,OAAA;gBAAKsN,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,gBAClFrN,OAAA;kBAAKsN,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjErN,OAAA;oBAAKsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCrN,OAAA;sBAAKsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAA8M;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAIsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAY;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5BrN,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5DlO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,gBACrCrN,OAAA;wBAAMsN,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAAlL,mBAAA,GAAEiF,QAAQ,CAACqI,SAAS,cAAAtN,mBAAA,cAAAA,mBAAA,GAAI;sBAAK;wBAAA4L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACjE9G,QAAQ,CAACqI,SAAS,KAAK,SAAS,IAAIrI,QAAQ,CAACsI,cAAc,iBAC1D1P,OAAA;wBAAMsN,SAAS,EAAC,qBAAqB;wBAAAD,QAAA,GAAC,IAAE,EAACjG,QAAQ,CAACsI,cAAc;sBAAA;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnElO,OAAA;sBAAKsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAChDsC,UAAU,CAACvI,QAAQ,CAACwI,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGzD,eAAe,EAAAhK,qBAAA,GAACgF,QAAQ,CAAC0I,cAAc,cAAA1N,qBAAA,cAAAA,qBAAA,GAAI,EAAE;oBAAC;sBAAA2L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9DlO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EACpCsC,UAAU,CAACvI,QAAQ,CAAC2I,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGzD,eAAe,CAAC,KAAK;oBAAC;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChElO,OAAA;sBAAKsN,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EAAEnC,UAAU,CAAC9D,QAAQ,CAAC4I,SAAS;oBAAC;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAoB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvElO,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EACtD,CAAAhL,sBAAA,GAAA+E,QAAQ,CAACuH,gBAAgB,cAAAtM,sBAAA,eAAzBA,sBAAA,CAA2BkM,SAAS,gBACnCvO,OAAA,CAAAE,SAAA;wBAAAmN,QAAA,gBACErN,OAAA;0BAAKsN,SAAS,EAAC,2EAA2E;0BAAAD,QAAA,eACxFrN,OAAA;4BAAMsN,SAAS,EAAC,oCAAoC;4BAAAD,QAAA,EACjDjG,QAAQ,CAACuH,gBAAgB,CAACJ,SAAS,CAAC0B,MAAM,CAAC,CAAC;0BAAC;4BAAAlC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,EACL9G,QAAQ,CAACuH,gBAAgB,CAACJ,SAAS;sBAAA,eACpC,CAAC,GAEH;oBACD;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5BrN,OAAA;sBAAKsN,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9DlO,OAAA;sBAAKsN,SAAS,EAAC,4CAA4C;sBAAAD,QAAA,GAAA/K,qBAAA,GACxD8E,QAAQ,CAAC8I,gBAAgB,cAAA5N,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAyL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP5J,UAAU,KAAK,sBAAsB,gBACpCtE,OAAA;YAAAqN,QAAA,gBACErN,OAAA;cAAKsN,SAAS,EAAC,yDAAyD;cAAAD,QAAA,gBACtErN,OAAA;gBAAKsN,SAAS,EAAC,oDAAoD;gBAAAD,QAAA,eACjErN,OAAA;kBAAKsN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCrN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAIsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAmB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlO,OAAA;gBAAKsN,SAAS,EAAC,KAAK;gBAAAD,QAAA,eAClBrN,OAAA;kBAAKsN,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpDrN,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAgE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpElO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EACjDjG,QAAQ,CAACuD,mBAAmB,gBAC3B3K,OAAA;0BAAMsN,SAAS,EAAG,2EAA0E1B,eAAe,CAACxE,QAAQ,CAACuD,mBAAmB,CAAE,EAAE;0BAAA0C,QAAA,EACzI3B,UAAU,CAACtE,QAAQ,CAACuD,mBAAmB;wBAAC;0BAAAoD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,GAEP;sBACD;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAkD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAY;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClElO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAEnC,UAAU,CAAC9D,QAAQ,CAAC+I,UAAU;sBAAC;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlO,OAAA;cAAKsN,SAAS,EAAC,yDAAyD;cAAAD,QAAA,eACtErN,OAAA;gBAAKsN,SAAS,EAAC,QAAQ;gBAAAD,QAAA,gBACrBrN,OAAA;kBAAKsN,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjErN,OAAA;oBAAKsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrCrN,OAAA;sBAAKsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAslB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3oB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAIsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAuB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAKsN,SAAS,EAAC,KAAK;kBAAAD,QAAA,EAEnB,EAAA9K,qBAAA,GAAA6E,QAAQ,CAACgJ,mBAAmB,cAAA7N,qBAAA,uBAA5BA,qBAAA,CAA8B8N,MAAM,IAAG,CAAC,gBACvCrQ,OAAA;oBAAKsN,SAAS,EAAC,WAAW;oBAAAD,QAAA,EACvBjG,QAAQ,CAACgJ,mBAAmB,CAAClK,GAAG,CAAC,CAACoK,cAAc,EAAEpB,KAAK;sBAAA,IAAAqB,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;sBAAA,oBACtD1Q,OAAA;wBAAiBsN,SAAS,EAAC,oFAAoF;wBAAAD,QAAA,gBAE7GrN,OAAA;0BAAKsN,SAAS,EAAC,oDAAoD;0BAAAD,QAAA,eACjErN,OAAA;4BAAKsN,SAAS,EAAC,8DAA8D;4BAAAD,QAAA,gBAC3ErN,OAAA;8BAAKsN,SAAS,EAAC,gCAAgC;8BAAAD,QAAA,gBAC7CrN,OAAA;gCAAKsN,SAAS,EAAC,kDAAkD;gCAAAD,QAAA,eAC/DrN,OAAA;kCAAKwN,KAAK,EAAC,4BAA4B;kCAACC,IAAI,EAAC,MAAM;kCAACC,OAAO,EAAC,WAAW;kCAACU,WAAW,EAAC,KAAK;kCAACT,MAAM,EAAC,cAAc;kCAACL,SAAS,EAAC,wBAAwB;kCAAAD,QAAA,eAChJrN,OAAA;oCAAM4N,aAAa,EAAC,OAAO;oCAACC,cAAc,EAAC,OAAO;oCAACC,CAAC,EAAC;kCAAmO;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxR;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,eACNlO,OAAA;gCAAIsN,SAAS,EAAC,4BAA4B;gCAAAD,QAAA,GAAC,eAAa,EAAC,EAAAkD,sBAAA,GAAAnJ,QAAQ,CAACgJ,mBAAmB,cAAAG,sBAAA,uBAA5BA,sBAAA,CAA8BF,MAAM,IAACnB,KAAK;8BAAA;gCAAAnB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtG,CAAC,eACNlO,OAAA;8BAAKsN,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,GACvDiD,cAAc,CAAChC,YAAY,iBAC1BtO,OAAA;gCAAKsN,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,gBAC5ErN,OAAA;kCAAKsN,SAAS,EAAC,sCAAsC;kCAAAD,QAAA,eACnDrN,OAAA;oCAAKwN,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJrN,OAAA;sCAAM4N,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAyJ;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC9M;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACNlO,OAAA;kCAAAqN,QAAA,GAAM,aACO,EAAC,EAAAmD,qBAAA,GAAAF,cAAc,CAAChC,YAAY,cAAAkC,qBAAA,uBAA3BA,qBAAA,CAA6BjC,SAAS,OAAAkC,sBAAA,GAAIH,cAAc,CAAChC,YAAY,cAAAmC,sBAAA,uBAA3BA,sBAAA,CAA6BE,KAAK,KAAI,MAAM,EACjGL,cAAc,CAACM,UAAU,iBACxB5Q,OAAA;oCAAMsN,SAAS,EAAC,qBAAqB;oCAAAD,QAAA,GAAC,KACjC,EAACnC,UAAU,CAACoF,cAAc,CAACM,UAAU,CAAC;kCAAA;oCAAA7C,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrC,CACP;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACG,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CACN,EACAoC,cAAc,CAACO,gBAAgB,iBAC9B7Q,OAAA;gCAAKsN,SAAS,EAAC,gFAAgF;gCAAAD,QAAA,EAC5FnC,UAAU,CAACoF,cAAc,CAACO,gBAAgB;8BAAC;gCAAA9C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACzC,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGNlO,OAAA;0BAAKsN,SAAS,EAAC,WAAW;0BAAAD,QAAA,gBAExBrN,OAAA;4BAAKsN,SAAS,EAAC,MAAM;4BAAAD,QAAA,eACnBrN,OAAA;8BAAKsN,SAAS,EAAC,uCAAuC;8BAAAD,QAAA,GACnDiD,cAAc,CAACQ,UAAU,iBACxB9Q,OAAA;gCAAKsN,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5DrN,OAAA;kCAAKsN,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvDrN,OAAA;oCAAKwN,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJrN,OAAA;sCAAM4N,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAmG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxJ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACNlO,OAAA;kCAAAqN,QAAA,gBACErN,OAAA;oCAAMsN,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAsB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC5ElO,OAAA;oCAAMsN,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAEnC,UAAU,CAACoF,cAAc,CAACQ,UAAU;kCAAC;oCAAA/C,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChG,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN,EAEAoC,cAAc,CAACS,QAAQ,iBACtB/Q,OAAA;gCAAKsN,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5DrN,OAAA;kCAAKsN,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvDrN,OAAA;oCAAKwN,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJrN,OAAA;sCAAM4N,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAmG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxJ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACNlO,OAAA;kCAAAqN,QAAA,gBACErN,OAAA;oCAAMsN,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAoB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC1ElO,OAAA;oCAAMsN,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAEnC,UAAU,CAACoF,cAAc,CAACS,QAAQ;kCAAC;oCAAAhD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC9F,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN,EAEAoC,cAAc,CAACU,gBAAgB,iBAC9BhR,OAAA;gCAAKsN,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5DrN,OAAA;kCAAKsN,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvDrN,OAAA;oCAAKwN,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,gBAChJrN,OAAA;sCAAM4N,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAuC;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,eAC/FlO,OAAA;sCAAM4N,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAgF;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrI;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACNlO,OAAA;kCAAAqN,QAAA,gBACErN,OAAA;oCAAMsN,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAgB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eACtElO,OAAA;oCAAMsN,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAEiD,cAAc,CAACU;kCAAgB;oCAAAjD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1F,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EAGL,EAAAwC,qBAAA,GAAAJ,cAAc,CAACW,iBAAiB,cAAAP,qBAAA,uBAAhCA,qBAAA,CAAkCL,MAAM,IAAG,CAAC,iBAC3CrQ,OAAA;4BAAAqN,QAAA,gBACErN,OAAA;8BAAKsN,SAAS,EAAC,wBAAwB;8BAAAD,QAAA,gBACrCrN,OAAA;gCAAKsN,SAAS,EAAC,oCAAoC;gCAAAD,QAAA,eACjDrN,OAAA;kCAAKwN,KAAK,EAAC,4BAA4B;kCAACC,IAAI,EAAC,MAAM;kCAACC,OAAO,EAAC,WAAW;kCAACU,WAAW,EAAC,KAAK;kCAACT,MAAM,EAAC,cAAc;kCAACL,SAAS,EAAC,wBAAwB;kCAAAD,QAAA,eAChJrN,OAAA;oCAAM4N,aAAa,EAAC,OAAO;oCAACC,cAAc,EAAC,OAAO;oCAACC,CAAC,EAAC;kCAA2X;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChb;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,eACNlO,OAAA;gCAAIsN,SAAS,EAAC,oCAAoC;gCAAAD,QAAA,EAAC;8BAAS;gCAAAU,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9D,CAAC,eAENlO,OAAA;8BAAKsN,SAAS,EAAC,WAAW;8BAAAD,QAAA,EACvBiD,cAAc,CAACW,iBAAiB,CAAC/K,GAAG,CAAC,CAACgL,eAAe,EAAEC,GAAG;gCAAA,IAAAC,qBAAA,EAAAC,sBAAA;gCAAA,oBACzDrR,OAAA;kCAAesN,SAAS,EAAC,4EAA4E;kCAAAD,QAAA,gBACnGrN,OAAA;oCAAKsN,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,gBACrCrN,OAAA;sCAAKsN,SAAS,EAAC,+EAA+E;sCAAAD,QAAA,eAC5FrN,OAAA;wCAAKwN,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,wBAAwB;wCAAAD,QAAA,eAChJrN,OAAA;0CAAM4N,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAyJ;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAC9M;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CAAC,eACNlO,OAAA;sCAAAqN,QAAA,gBACErN,OAAA;wCAAMsN,SAAS,EAAC,oCAAoC;wCAAAD,QAAA,EAAE,EAAA+D,qBAAA,GAAAF,eAAe,CAACI,QAAQ,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0B7C,SAAS,KAAI;sCAAK;wCAAAR,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC,eAC1GlO,OAAA;wCAAMsN,SAAS,EAAC,mEAAmE;wCAAAD,QAAA,EAAE6D,eAAe,CAACK,YAAY,IAAI;sCAAK;wCAAAxD,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC/H,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CAAC,eAENlO,OAAA;oCAAKsN,SAAS,EAAC,6CAA6C;oCAAAD,QAAA,GACzD6D,eAAe,CAACM,kBAAkB,iBACjCxR,OAAA;sCAAKsN,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChCrN,OAAA;wCAAKwN,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJrN,OAAA;0CAAM4N,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAkd;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACvgB,CAAC,eACNlO,OAAA;wCAAAqN,QAAA,gBACErN,OAAA;0CAAMsN,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAU;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAChElO,OAAA;0CAAMsN,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAE6D,eAAe,CAACM;wCAAkB;0CAAAzD,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACjF,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN,EAEAgD,eAAe,CAACO,aAAa,iBAC5BzR,OAAA;sCAAKsN,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChCrN,OAAA;wCAAKwN,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJrN,OAAA;0CAAM4N,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAmO;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxR,CAAC,eACNlO,OAAA;wCAAAqN,QAAA,gBACErN,OAAA;0CAAMsN,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAU;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAChElO,OAAA;0CAAMsN,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAEnC,UAAU,CAACgG,eAAe,CAACO,aAAa;wCAAC;0CAAA1D,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxF,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN,EAEA,EAAAmD,sBAAA,GAAAH,eAAe,CAACI,QAAQ,cAAAD,sBAAA,uBAAxBA,sBAAA,CAA0BK,KAAK,kBAC9B1R,OAAA;sCAAKsN,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChCrN,OAAA;wCAAKwN,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJrN,OAAA;0CAAM4N,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAmW;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxZ,CAAC,eACNlO,OAAA;wCAAAqN,QAAA,gBACErN,OAAA;0CAAMsN,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAO;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAC7DlO,OAAA;0CAAMsN,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAE6D,eAAe,CAACI,QAAQ,CAACI;wCAAK;0CAAA3D,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAC7E,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACE,CAAC;gCAAA,GAjDEiD,GAAG;kCAAApD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAkDR,CAAC;8BAAA,CACP;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA,GA9JEgB,KAAK;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+JV,CAAC;oBAAA,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAENlO,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,mFAAmF;sBAAAD,QAAA,eAChGrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAslB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3oB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAIsN,SAAS,EAAC,iCAAiC;sBAAAD,QAAA,EAAC;oBAA2B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChFlO,OAAA;sBAAGsN,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAC;oBAAwD;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,GACN,IAAI,EAEP5J,UAAU,KAAK,iBAAiB,gBAC/BtE,OAAA;YAAKsN,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtErN,OAAA;cAAKsN,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjErN,OAAA;gBAAKsN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrCrN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAmQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAIsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAe;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlO,OAAA;cAAKsN,SAAS,EAAC,KAAK;cAAAD,QAAA,EACjB,EAAA7K,qBAAA,GAAA4E,QAAQ,CAACuK,eAAe,cAAAnP,qBAAA,uBAAxBA,qBAAA,CAA0B6N,MAAM,IAAG,CAAC,gBACnCrQ,OAAA;gBAAKsN,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,GAAA5K,sBAAA,GACnD2E,QAAQ,CAACuK,eAAe,cAAAlP,sBAAA,uBAAxBA,sBAAA,CAA0ByD,GAAG,CAAC,CAAC0L,IAAI,EAAE1C,KAAK,kBACzClP,OAAA;kBAEEuN,IAAI,EAAEhO,WAAW,GAAGqS,IAAI,CAACzL,IAAK;kBAC9B0L,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzBxE,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,eAEtErN,OAAA;oBAAKsN,SAAS,EAAC,8FAA8F;oBAAAD,QAAA,gBAC3GrN,OAAA;sBAAKsN,SAAS,EAAC,0DAA0D;sBAAAD,QAAA,eACvErN,OAAA;wBACEwN,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,gBAElCrN,OAAA;0BAAM8N,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChOlO,OAAA;0BAAM8N,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAKsN,SAAS,EAAC,iBAAiB;sBAAAD,QAAA,gBAC9BrN,OAAA;wBAAKsN,SAAS,EAAC,oFAAoF;wBAAAD,QAAA,EAChGuE,IAAI,CAACG;sBAAS;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACNlO,OAAA;wBAAKsN,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,GAAEuE,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAjE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAxBDgB,KAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBT,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENlO,OAAA;gBAAKsN,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/BrN,OAAA;kBAAKsN,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,eAChGrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAmQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAIsN,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvElO,OAAA;kBAAGsN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAwD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP5J,UAAU,KAAK,UAAU,gBACxBtE,OAAA;YAAKsN,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtErN,OAAA;cAAKsN,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjErN,OAAA;gBAAKsN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrCrN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAkf;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACviB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAIsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAe;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlO,OAAA;cAAKsN,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClBrN,OAAA;gBAAKsN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzDrN,OAAA;kBAAKsN,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBrN,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAmQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpElO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,GAAA3K,qBAAA,GAAE0E,QAAQ,CAAC6K,cAAc,cAAAvP,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAAqL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAmG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAW;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjElO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAEnC,UAAU,CAAC9D,QAAQ,CAAC8K,WAAW,CAAC,IAAI;sBAAK;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAA4O;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAM;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5DlO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,GAAC,IAAE,EAACsC,UAAU,CAACvI,QAAQ,CAAC+K,cAAc,IAAI,CAAC,CAAC,CAACtC,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBrN,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAkD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9DlO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAG;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENlO,OAAA;oBAAKsN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5DrN,OAAA;sBAAKsN,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvDrN,OAAA;wBAAKwN,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJrN,OAAA;0BAAM4N,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAuB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNlO,OAAA;sBAAAqN,QAAA,gBACErN,OAAA;wBAAMsN,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpElO,OAAA;wBAAMsN,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAG;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlO,OAAA;gBAAKsN,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBrN,OAAA;kBAAKsN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCrN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAIsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,EAEL,EAAAvL,qBAAA,GAAAyE,QAAQ,CAACgL,eAAe,cAAAzP,qBAAA,uBAAxBA,qBAAA,CAA0B0N,MAAM,IAAG,CAAC,gBACnCrQ,OAAA;kBAAKsN,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,GAAAzK,sBAAA,GACnDwE,QAAQ,CAACgL,eAAe,cAAAxP,sBAAA,uBAAxBA,sBAAA,CAA0BsD,GAAG,CAAC,CAAC0L,IAAI,EAAET,GAAG,kBACvCnR,OAAA;oBAEEuN,IAAI,EAAEhO,WAAW,GAAGqS,IAAI,CAACzL,IAAK;oBAC9B0L,MAAM,EAAC,QAAQ;oBACfC,GAAG,EAAC,qBAAqB;oBACzBxE,SAAS,EAAC,4DAA4D;oBAAAD,QAAA,eAEtErN,OAAA;sBAAKsN,SAAS,EAAC,8FAA8F;sBAAAD,QAAA,gBAC3GrN,OAAA;wBAAKsN,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,eACvErN,OAAA;0BACEwN,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBH,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,gBAElCrN,OAAA;4BAAM8N,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlO,OAAA;4BAAM8N,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBAAKsN,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC9BrN,OAAA;0BAAKsN,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAChGuE,IAAI,CAACG;wBAAS;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNlO,OAAA;0BAAKsN,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,GAAEuE,IAAI,CAACI,SAAS,EAAC,KAAG;wBAAA;0BAAAjE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAxBDiD,GAAG;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBP,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENlO,OAAA;kBAAKsN,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,gBACvDrN,OAAA;oBAAKsN,SAAS,EAAC,yFAAyF;oBAAAD,QAAA,eACtGrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAGsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EAAC;kBAA2C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEP5J,UAAU,KAAK,yBAAyB,gBACvCtE,OAAA;YAAKsN,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtErN,OAAA;cAAKsN,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjErN,OAAA;gBAAKsN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrCrN,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAme;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxhB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAIsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlO,OAAA;cAAKsN,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClBrN,OAAA;gBAAKsN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzDrN,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5DrN,OAAA;oBAAKsN,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAAqN,QAAA,gBACErN,OAAA;sBAAMsN,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAoB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1ElO,OAAA;sBAAMsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAxK,qBAAA,GAAEuE,QAAQ,CAACiL,gBAAgB,cAAAxP,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAkL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5DrN,OAAA;oBAAKsN,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAA4S;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjW;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAAqN,QAAA,gBACErN,OAAA;sBAAMsN,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvElO,OAAA;sBAAMsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAvK,sBAAA,IAAAC,oBAAA,GAAEqE,QAAQ,CAACyH,SAAS,cAAA9L,oBAAA,uBAAlBA,oBAAA,CAAoB+L,cAAc,cAAAhM,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAAiL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5DrN,OAAA;oBAAKsN,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAAqN,QAAA,gBACErN,OAAA;sBAAMsN,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnElO,OAAA;sBAAMsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAArK,sBAAA,GAAEoE,QAAQ,CAACiH,gBAAgB,cAAArL,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlO,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5DrN,OAAA;oBAAKsN,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAA8T;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAAqN,QAAA,gBACErN,OAAA;sBAAMsN,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnElO,OAAA;sBAAMsN,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAApK,qBAAA,GAAEmE,QAAQ,CAACkL,aAAa,cAAArP,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNlO,OAAA;gBAAKsN,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBrN,OAAA;kBAAKsN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCrN,OAAA;oBAAKsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjDrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAIsN,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,EAEL,EAAAhL,qBAAA,GAAAkE,QAAQ,CAACmL,oBAAoB,cAAArP,qBAAA,uBAA7BA,qBAAA,CAA+BmN,MAAM,IAAG,CAAC,gBACxCrQ,OAAA;kBAAKsN,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,GAAAlK,sBAAA,GACnDiE,QAAQ,CAACmL,oBAAoB,cAAApP,sBAAA,uBAA7BA,sBAAA,CAA+B+C,GAAG,CAAC,CAAC0L,IAAI,EAAET,GAAG,kBAC5CnR,OAAA;oBAEEuN,IAAI,EAAEhO,WAAW,GAAGqS,IAAI,CAACzL,IAAK;oBAC9B0L,MAAM,EAAC,QAAQ;oBACfC,GAAG,EAAC,qBAAqB;oBACzBxE,SAAS,EAAC,4DAA4D;oBAAAD,QAAA,eAEtErN,OAAA;sBAAKsN,SAAS,EAAC,8FAA8F;sBAAAD,QAAA,gBAC3GrN,OAAA;wBAAKsN,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,eACvErN,OAAA;0BACEwN,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBH,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,gBAElCrN,OAAA;4BAAM8N,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOlO,OAAA;4BAAM8N,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNlO,OAAA;wBAAKsN,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC9BrN,OAAA;0BAAKsN,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAChGuE,IAAI,CAACG;wBAAS;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNlO,OAAA;0BAAKsN,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,GAAEuE,IAAI,CAACI,SAAS,EAAC,KAAG;wBAAA;0BAAAjE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAxBDiD,GAAG;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBP,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENlO,OAAA;kBAAKsN,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,gBACvDrN,OAAA;oBAAKsN,SAAS,EAAC,yFAAyF;oBAAAD,QAAA,eACtGrN,OAAA;sBAAKwN,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJrN,OAAA;wBAAM4N,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlO,OAAA;oBAAGsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EAAC;kBAAiD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAGP5J,UAAU,KAAK,SAAS,gBACvBtE,OAAA,CAACV,WAAW;YACVkT,WAAW,EAAE;cACXjJ,OAAO,EAAEA,OAAO;cAChB7F,IAAI,EAAE8F,kBAAkB;cACxB9B,KAAK,EAAE+B,iBAAiB;cACxBgJ,KAAK,EAAE,CAAAlJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE8G,MAAM,KAAI;YAC5B,CAAE;YACFvJ,OAAO,EAAEuC,cAAe;YACxBtC,KAAK,EAAEuC;UAAa;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,GACA,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAENlO,OAAA;UAAKsN,SAAS,EAAC,8CAA8C;UAAAD,QAAA,gBAC3DrN,OAAA;YAAKsN,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrCrN,OAAA;cAAKsN,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjDrN,OAAA;gBAAKwN,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACU,WAAW,EAAC,KAAK;gBAACT,MAAM,EAAC,cAAc;gBAACL,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eAChJrN,OAAA;kBAAM4N,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAkW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlO,OAAA;cAAIsN,SAAS,EAAC,oCAAoC;cAAAD,QAAA,EAAC;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAENlO,OAAA;YAAKsN,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBAEpDrN,OAAA;cAAAqN,QAAA,gBACErN,OAAA;gBAAOsN,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,EAAC;cAEnE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlO,OAAA;gBAAKsN,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvBrN,OAAA;kBACE6M,KAAK,EAAErI,YAAa;kBACpBkO,QAAQ,EAAGC,CAAC,IAAKlO,eAAe,CAACkO,CAAC,CAACd,MAAM,CAAChF,KAAK,CAAE;kBACjD+F,WAAW,EAAC,2BAA2B;kBACvCtF,SAAS,EAAG,kDACV5I,iBAAiB,GACb,8DAA8D,GAC9D,8DACL;gBAA+G;kBAAAqJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACXxJ,iBAAiB,iBAChB1E,OAAA;kBAAKsN,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5DrN,OAAA;oBAAKwN,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,eAC1IrN,OAAA;sBAAM4N,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA6E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClI,CAAC,EACLxJ,iBAAiB;gBAAA;kBAAAqJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlO,OAAA;cAAAqN,QAAA,gBACErN,OAAA;gBAAOsN,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,EAAC;cAEnE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRlO,OAAA;gBAAA,GACM2F,eAAe,CAAC;kBAClB2H,SAAS,EAAE;gBACb,CAAC,CAAC;gBACFA,SAAS,EAAC,4LAA4L;gBAAAD,QAAA,gBAEtMrN,OAAA;kBAAA,GAAW6F,gBAAgB,CAAC;gBAAC;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjClO,OAAA;kBAAKsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjDrN,OAAA;oBACEwN,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBU,WAAW,EAAC,KAAK;oBACjBT,MAAM,EAAC,cAAc;oBACrBL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAElCrN,OAAA;sBACE4N,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAA4G;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/G;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBAAGsN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBAC/CrN,OAAA;oBAAMsN,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,qBACrE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJlO,OAAA;kBAAGsN,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,EAAC;gBAE3C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAAA1I,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6K,MAAM,IAAG,CAAC,iBACxBrQ,OAAA;YAAKsN,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnBrN,OAAA;cAAIsN,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3ElO,OAAA;cAAKsN,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAClE7H,aAAa,CAACU,GAAG,CAAC,CAACC,IAAI,EAAEgL,GAAG,kBAC3BnR,OAAA;gBACEsN,SAAS,EAAC,8DAA8D;gBAAAD,QAAA,gBAGxErN,OAAA;kBAAKsN,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,eACzDrN,OAAA;oBACE6S,GAAG,EAAE1M,IAAI,CAACG,OAAQ;oBAClBgH,SAAS,EAAC,gCAAgC;oBAC1CwF,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;sBACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;oBAC1C,CAAE;oBACFI,GAAG,EAAC;kBAAS;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlO,OAAA;kBAAKsN,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrCrN,OAAA;oBAAKsN,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,EACzDlH,IAAI,CAAC+M;kBAAI;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNlO,OAAA;oBAAKsN,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,GACpC,CAAClH,IAAI,CAACgN,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEtD,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;kBAAA;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNlO,OAAA;kBACEwO,OAAO,EAAEA,CAAA,KAAM;oBACb/I,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAACkH,MAAM,CAAC,CAACiG,CAAC,EAAEC,aAAa,KAAKlC,GAAG,KAAKkC,aAAa,CAC9D,CAAC;kBACH,CAAE;kBACF/F,SAAS,EAAC,iJAAiJ;kBAC3JpB,KAAK,EAAC,aAAa;kBAAAmB,QAAA,eAEnBrN,OAAA;oBACEwN,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBU,WAAW,EAAC,KAAK;oBACjBT,MAAM,EAAC,cAAc;oBACrBL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAElCrN,OAAA;sBACE4N,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5CJ/H,IAAI,CAAC+M,IAAI,GAAG/B,GAAG;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6CjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDlO,OAAA;YAAKsN,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnBrN,OAAA;cACEsT,QAAQ,EAAErL,qBAAsB;cAChCuG,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI+E,KAAK,GAAG,IAAI;gBAChB5O,oBAAoB,CAAC,EAAE,CAAC;gBAExB,IAAIH,YAAY,KAAK,EAAE,IAAIgB,aAAa,CAAC6K,MAAM,KAAK,CAAC,EAAE;kBACrD1L,oBAAoB,CAAC,yCAAyC,CAAC;kBAC/D4O,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAIA,KAAK,EAAE;kBACTjQ,QAAQ,CACN5E,iBAAiB,CACf;oBACE8U,OAAO,EAAEhP,YAAY;oBACrBiP,YAAY,EAAEjO;kBAChB,CAAC,EACDjC,EACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACL5D,KAAK,CAACoH,KAAK,CACT,oDACF,CAAC;gBACH;cACF,CAAE;cACFuG,SAAS,EAAG,qEACVrF,qBAAqB,GACjB,gDAAgD,GAChD,2EACL,EAAE;cAAAoF,QAAA,EAEFpF,qBAAqB,gBACpBjI,OAAA,CAAAE,SAAA;gBAAAmN,QAAA,gBACErN,OAAA;kBAAKsN,SAAS,EAAC,gDAAgD;kBAACE,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAAAL,QAAA,gBAChIrN,OAAA;oBAAQsN,SAAS,EAAC,YAAY;oBAACoG,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACjG,MAAM,EAAC,cAAc;oBAACS,WAAW,EAAC;kBAAG;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACrGlO,OAAA;oBAAMsN,SAAS,EAAC,YAAY;oBAACG,IAAI,EAAC,cAAc;oBAACK,CAAC,EAAC;kBAAiH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzK,CAAC,aAER;cAAA,eAAE,CAAC,gBAEHlO,OAAA,CAAAE,SAAA;gBAAAmN,QAAA,gBACErN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,cAAc;kBAAAD,QAAA,eACtIrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvZ,CAAC,eAER;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlO,OAAA;YAAKsN,SAAS,EAAC,MAAM;YAAAD,QAAA,EAClB7F,kBAAkB,gBACjBxH,OAAA;cAAKsN,SAAS,EAAC,uCAAuC;cAAAD,QAAA,eACpDrN,OAAA;gBAAKsN,SAAS,EAAC;cAA4E;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,GACJzG,gBAAgB,gBAClBzH,OAAA;cAAKsN,SAAS,EAAC,8DAA8D;cAAAD,QAAA,gBAC3ErN,OAAA;gBAAKwN,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACU,WAAW,EAAC,KAAK;gBAACT,MAAM,EAAC,cAAc;gBAACL,SAAS,EAAC,cAAc;gBAAAD,QAAA,eACtIrN,OAAA;kBAAM4N,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA6E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACNlO,OAAA;gBAAAqN,QAAA,EAAO5F;cAAgB;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,GACJ3G,QAAQ,IAAIA,QAAQ,CAAC8I,MAAM,GAAG,CAAC,gBACjCrQ,OAAA;cAAKsN,SAAS,EAAC,WAAW;cAAAD,QAAA,EACvB9F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC2N,OAAO,EAAE1C,GAAG;gBAAA,IAAA2C,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,cAAA;gBAAA,oBAC1BtU,OAAA;kBAAesN,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,eAC9DrN,OAAA;oBAAKsN,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/BrN,OAAA;sBAAKsN,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAChCwG,OAAO,CAACU,WAAW,GAClB,CAAAT,oBAAA,GAAAD,OAAO,CAACU,WAAW,cAAAT,oBAAA,eAAnBA,oBAAA,CAAqBU,KAAK,gBACxBxU,OAAA;wBACEsN,SAAS,EAAC,qEAAqE;wBAC/EuF,GAAG,EAAEtT,WAAW,KAAAwU,qBAAA,GAAGF,OAAO,CAACU,WAAW,cAAAR,qBAAA,uBAAnBA,qBAAA,CAAqBS,KAAK,CAAC;wBAC9C1B,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;0BACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;wBAC1C,CAAE;wBACFI,GAAG,EAAE,EAAAe,qBAAA,GAAAH,OAAO,CAACU,WAAW,cAAAP,qBAAA,uBAAnBA,qBAAA,CAAqBzF,SAAS,KAAI;sBAAO;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,gBAEFlO,OAAA;wBAAKsN,SAAS,EAAC,2FAA2F;wBAAAD,QAAA,eACxGrN,OAAA;0BAAMsN,SAAS,EAAC,qBAAqB;0BAAAD,QAAA,GAClC,CAAA4G,qBAAA,GAAAJ,OAAO,CAACU,WAAW,cAAAN,qBAAA,eAAnBA,qBAAA,CAAqBQ,UAAU,IAAAP,qBAAA,GAC5BL,OAAO,CAACU,WAAW,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBO,UAAU,CAAC,CAAC,CAAC,GAClC,EAAE,EACL,CAAAN,qBAAA,GAAAN,OAAO,CAACU,WAAW,cAAAJ,qBAAA,eAAnBA,qBAAA,CAAqBO,SAAS,IAAAN,qBAAA,GAC3BP,OAAO,CAACU,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBM,SAAS,CAAC,CAAC,CAAC,GACjC,EAAE;wBAAA;0BAAA3G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,gBAEDlO,OAAA;wBAAKsN,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,eAC7FrN,OAAA;0BAAKwN,KAAK,EAAC,4BAA4B;0BAACC,IAAI,EAAC,MAAM;0BAACC,OAAO,EAAC,WAAW;0BAACU,WAAW,EAAC,KAAK;0BAACT,MAAM,EAAC,cAAc;0BAACL,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,eAChJrN,OAAA;4BAAM4N,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,CAAC,EAAC;0BAAyJ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9M;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAENlO,OAAA;sBAAKsN,SAAS,EAAC,QAAQ;sBAAAD,QAAA,gBACrBrN,OAAA;wBAAKsN,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,gBAC7ErN,OAAA;0BAAKsN,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,EACxC,EAAAgH,qBAAA,GAAAR,OAAO,CAACU,WAAW,cAAAF,qBAAA,uBAAnBA,qBAAA,CAAqB9F,SAAS,KAAI;wBAAQ;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eAENlO,OAAA;0BAAKsN,SAAS,EAAC,uDAAuD;0BAAAD,QAAA,gBACpErN,OAAA;4BAAKsN,SAAS,EAAC,kCAAkC;4BAAAD,QAAA,eAC/CrN,OAAA;8BAAKwN,KAAK,EAAC,4BAA4B;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAACU,WAAW,EAAC,KAAK;8BAACT,MAAM,EAAC,cAAc;8BAACL,SAAS,EAAC,4BAA4B;8BAAAD,QAAA,eACpJrN,OAAA;gCAAM4N,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,CAAC,EAAC;8BAAkD;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvG;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACNlO,OAAA;4BAAAqN,QAAA,EAAOnC,UAAU,CAAC2I,OAAO,CAACjD,UAAU;0BAAC;4BAAA7C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAE5C2F,OAAO,CAACc,UAAU,iBACjB3U,OAAA;4BACEwO,OAAO,EAAEA,CAAA,KAAM;8BACbvJ,gBAAgB,CAAC4O,OAAO,CAACtQ,EAAE,CAAC;8BAC5B4B,YAAY,CAAC,QAAQ,CAAC;8BACtBJ,kBAAkB,CAAC,IAAI,CAAC;4BAC1B,CAAE;4BACFuI,SAAS,EAAC,8EAA8E;4BAAAD,QAAA,gBAExFrN,OAAA;8BAAKwN,KAAK,EAAC,4BAA4B;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAACU,WAAW,EAAC,KAAK;8BAACT,MAAM,EAAC,cAAc;8BAACL,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,eAC1IrN,OAAA;gCAAM4N,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,CAAC,EAAC;8BAA+Z;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpd,CAAC,UAER;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAENlO,OAAA;wBAAKsN,SAAS,EAAC,yEAAyE;wBAAAD,QAAA,EACrFwG,OAAO,CAACL,OAAO,IAAI;sBAAY;wBAAAzF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,EAEL,CAAA2F,OAAO,aAAPA,OAAO,wBAAAS,cAAA,GAAPT,OAAO,CAAEe,KAAK,cAAAN,cAAA,uBAAdA,cAAA,CAAgBjE,MAAM,IAAG,CAAC,iBACzBrQ,OAAA;wBAAKsN,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,EACxDwG,OAAO,CAACe,KAAK,CAAC1O,GAAG,CAAC,CAACC,IAAI,EAAE0O,OAAO,kBAC/B7U,OAAA;0BAEE6R,MAAM,EAAC,QAAQ;0BACfC,GAAG,EAAC,qBAAqB;0BACzBvE,IAAI,EAAEhO,WAAW,GAAG4G,IAAI,CAACA,IAAK;0BAC9BmH,SAAS,EAAC,4DAA4D;0BAAAD,QAAA,eAEtErN,OAAA;4BAAKsN,SAAS,EAAC,gEAAgE;4BAAAD,QAAA,gBAC7ErN,OAAA;8BACE6S,GAAG,EAAEtT,WAAW,GAAG4G,IAAI,CAACA,IAAK;8BAC7BmH,SAAS,EAAC,4BAA4B;8BACtCwF,OAAO,EAAGC,CAAC,IAAK;gCACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,GAAG,IAAI;gCACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,GAAG,yBAAyB;8BAC1C,CAAE;8BACFI,GAAG,EAAC;4BAAY;8BAAAlF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,eACFlO,OAAA;8BAAKsN,SAAS,EAAC;4BAA4F;8BAAAS,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/G;wBAAC,GAjBD2G,OAAO;0BAAA9G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAkBX,CACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAlGEiD,GAAG;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmGR,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAENlO,OAAA;cAAKsN,SAAS,EAAC,2CAA2C;cAAAD,QAAA,gBACxDrN,OAAA;gBAAKsN,SAAS,EAAC,yFAAyF;gBAAAD,QAAA,eACtGrN,OAAA;kBAAKwN,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJrN,OAAA;oBAAM4N,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlO,OAAA;gBAAIsN,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpElO,OAAA;gBAAGsN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAAC;cAA0C;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENlO,OAAA,CAACF,iBAAiB;MAChBgV,MAAM,EAAEhQ,eAAgB;MACxBqJ,OAAO,EACLjJ,SAAS,KAAK,QAAQ,GAClB,+CAA+C,GAC/C,gBACL;MACDgH,KAAK,EAAEhH,SAAS,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAAe;MAClEiH,IAAI,EAAC,QAAQ;MACb4I,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI/P,SAAS,KAAK,QAAQ,IAAIF,aAAa,KAAK,EAAE,EAAE;UAClD1B,QAAQ,CAAC3E,iBAAiB,CAACqG,aAAa,CAAC,CAAC;UAC1CD,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACLJ,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;UAChBF,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAE;MACFiQ,QAAQ,EAAEA,CAAA,KAAM;QACdnQ,kBAAkB,CAAC,KAAK,CAAC;QACzBI,YAAY,CAAC,EAAE,CAAC;QAChBF,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAE;MACFkQ,SAAS,EAAEvN;IAAyB;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEFlO,OAAA,CAACF,iBAAiB;MAChBgV,MAAM,EAAE9Q,QAAS;MACjBkI,KAAK,eACHlM,OAAA;QAAKsN,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/CrN,OAAA;UAAKwN,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAACU,WAAW,EAAC,KAAK;UAACT,MAAM,EAAC,cAAc;UAACL,SAAS,EAAC,cAAc;UAAAD,QAAA,eACtIrN,OAAA;YAAM4N,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAA2X;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChb,CAAC,eACNlO,OAAA;UAAAqN,QAAA,EAAM;QAAuB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;MACDC,OAAO,eACLnO,OAAA;QAAKsN,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1BrN,OAAA;UAAKsN,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCrN,OAAA;YAAKwN,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACU,WAAW,EAAC,KAAK;YAACT,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eACrJrN,OAAA;cAAM4N,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA2X;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChb,CAAC,eACNlO,OAAA;YAAOsN,SAAS,EAAC,oCAAoC;YAAAD,QAAA,GAAC,uBAC/B,eAAArN,OAAA;cAAMsN,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNlO,OAAA;UAAKsN,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvBrN,OAAA;YAAKsN,SAAS,EAAC,sEAAsE;YAAAD,QAAA,eACnFrN,OAAA;cAAKwN,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACU,WAAW,EAAC,KAAK;cAACT,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/IrN,OAAA;gBAAM4N,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAyJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlO,OAAA;YACEsN,SAAS,EAAG,mBACVlJ,sBAAsB,GAClB,wDAAwD,GACxD,6DACL,iJAAiJ;YAClJyI,KAAK,EAAE3I,iBAAkB;YACzBwO,QAAQ,EAAGC,CAAC,IAAKxO,oBAAoB,CAACwO,CAAC,CAACd,MAAM,CAAChF,KAAK,CAAE;YAAAQ,QAAA,gBAEtDrN,OAAA;cAAQ6M,KAAK,EAAE,EAAG;cAAAQ,QAAA,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClD5F,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEpC,GAAG,CAAE0L,IAAI,iBACtB5R,OAAA;cAAsB6M,KAAK,EAAE+E,IAAI,CAACrO,EAAG;cAAA8J,QAAA,EAAEuE,IAAI,CAACrD;YAAS,GAAxCqD,IAAI,CAACrO,EAAE;cAAAwK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0C,CAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACTlO,OAAA;YAAKsN,SAAS,EAAC,uEAAuE;YAAAD,QAAA,eACpFrN,OAAA;cAAKwN,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACU,WAAW,EAAC,KAAK;cAACT,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/IrN,OAAA;gBAAM4N,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACL9J,sBAAsB,iBACrBpE,OAAA;UAAKsN,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClDrN,OAAA;YAAKwN,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACU,WAAW,EAAC,KAAK;YAACT,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,cAAc;YAAAD,QAAA,eACtIrN,OAAA;cAAM4N,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA6E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC,eACNlO,OAAA;YAAMsN,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAEjJ;UAAsB;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACD/B,IAAI,EAAC,MAAM;MACX4I,WAAW,EAAC,oBAAoB;MAChCC,UAAU,EAAC,QAAQ;MACnBI,kBAAkB,EAAC,2EAA2E;MAC9FC,iBAAiB,EAAC,6EAA6E;MAC/FJ,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB5Q,yBAAyB,CAAC,EAAE,CAAC;QAE7B,IAAIH,iBAAiB,KAAK,EAAE,EAAE;UAC5BG,yBAAyB,CAAC,yBAAyB,CAAC;QACtD,CAAC,MAAM;UACLN,YAAY,CAAC,IAAI,CAAC;UAClB,MAAMT,QAAQ,CACZtE,kBAAkB,CAACuE,EAAE,EAAE;YAAEgR,WAAW,EAAErQ;UAAkB,CAAC,CAC3D,CAAC;UACDH,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAE;MACFmR,QAAQ,EAAEA,CAAA,KAAM;QACd/Q,oBAAoB,CAAC,EAAE,CAAC;QACxBE,yBAAyB,CAAC,EAAE,CAAC;QAC7BJ,WAAW,CAAC,KAAK,CAAC;QAClBF,YAAY,CAAC,KAAK,CAAC;MACrB,CAAE;MACFoR,SAAS,EAAErR,SAAU;MACrBwR,WAAW,EAAC,0BAA0B;MACtCC,WAAW,eACTvV,OAAA;QAAKsN,SAAS,EAAC,iCAAiC;QAACE,KAAK,EAAC,4BAA4B;QAACC,IAAI,EAAC,MAAM;QAACC,OAAO,EAAC,WAAW;QAAAL,QAAA,gBACjHrN,OAAA;UAAQsN,SAAS,EAAC,YAAY;UAACoG,EAAE,EAAC,IAAI;UAACC,EAAE,EAAC,IAAI;UAACC,CAAC,EAAC,IAAI;UAACjG,MAAM,EAAC,cAAc;UAACS,WAAW,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACrGlO,OAAA;UAAMsN,SAAS,EAAC,YAAY;UAACG,IAAI,EAAC,cAAc;UAACK,CAAC,EAAC;QAAiH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGD9I,mBAAmB,iBAClBpF,OAAA;MAAKsN,SAAS,EAAC,4EAA4E;MAAAD,QAAA,eACzFrN,OAAA;QAAKsN,SAAS,EAAC,iFAAiF;QAAAD,QAAA,gBAE9FrN,OAAA;UAAKsN,SAAS,EAAC,oCAAoC;UAAAD,QAAA,eACjDrN,OAAA;YAAKsN,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDrN,OAAA;cAAIsN,SAAS,EAAC,sCAAsC;cAAAD,QAAA,EAAC;YAAgB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1ElO,OAAA;cACEwO,OAAO,EAAEA,CAAA,KAAMnJ,sBAAsB,CAAC,KAAK,CAAE;cAC7CiI,SAAS,EAAC,kEAAkE;cAAAD,QAAA,eAE5ErN,OAAA;gBAAKsN,SAAS,EAAC,SAAS;gBAACG,IAAI,EAAC,MAAM;gBAACE,MAAM,EAAC,cAAc;gBAACD,OAAO,EAAC,WAAW;gBAAAL,QAAA,eAC5ErN,OAAA;kBAAM4N,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACO,WAAW,EAAC,GAAG;kBAACN,CAAC,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlO,OAAA;UAAKsN,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBrN,OAAA;YAAGsN,SAAS,EAAC,6BAA6B;YAAAD,QAAA,EAAC;UAE3C;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJlO,OAAA;YAAKsN,SAAS,EAAC,WAAW;YAAAD,QAAA,EACvBT,aAAa,CAAC1G,GAAG,CAAE+F,MAAM,iBACxBjM,OAAA;cAAwBsN,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBACnDrN,OAAA;gBACEsK,IAAI,EAAC,UAAU;gBACf/G,EAAE,EAAE0I,MAAM,CAACY,KAAM;gBACjB2I,OAAO,EAAElQ,gBAAgB,CAAC4H,QAAQ,CAACjB,MAAM,CAACY,KAAK,CAAE;gBACjD6F,QAAQ,EAAEA,CAAA,KAAM1F,kBAAkB,CAACf,MAAM,CAACY,KAAK,CAAE;gBACjDS,SAAS,EAAC;cAA8F;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CAAC,eACFlO,OAAA;gBACEyV,OAAO,EAAExJ,MAAM,CAACY,KAAM;gBACtBS,SAAS,EAAG,2CAA0CrB,MAAM,CAACc,KAAM,EAAE;gBAAAM,QAAA,EAEpEpB,MAAM,CAACa;cAAK;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA,GAbAjC,MAAM,CAACY,KAAK;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcjB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlO,OAAA;UAAKsN,SAAS,EAAC,+DAA+D;UAAAD,QAAA,gBAC5ErN,OAAA;YACEwO,OAAO,EAAEA,CAAA,KAAMnJ,sBAAsB,CAAC,KAAK,CAAE;YAC7CiI,SAAS,EAAC,sHAAsH;YAAAD,QAAA,EACjI;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlO,OAAA;YACEwO,OAAO,EAAEpB,kBAAmB;YAC5BkG,QAAQ,EAAE3J,iBAAkB;YAC5B2D,SAAS,EAAC,sLAAsL;YAAAD,QAAA,EAE/L1D,iBAAiB,gBAChB3J,OAAA,CAAAE,SAAA;cAAAmN,QAAA,gBACErN,OAAA;gBAAKsN,SAAS,EAAC,4CAA4C;gBAACE,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAL,QAAA,gBAC5HrN,OAAA;kBAAQsN,SAAS,EAAC,YAAY;kBAACoG,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACjG,MAAM,EAAC,cAAc;kBAACS,WAAW,EAAC;gBAAG;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrGlO,OAAA;kBAAMsN,SAAS,EAAC,YAAY;kBAACG,IAAI,EAAC,cAAc;kBAACK,CAAC,EAAC;gBAAiH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,eAER;YAAA,eAAE,CAAC,GAEH;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAEpB;AAACzN,EAAA,CA12DQD,gBAAgB;EAAA,QACNjC,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACkBC,eAAe,EA4BrDiB,WAAW,EAsBKrB,WAAW,EAGVA,WAAW,EAINA,WAAW,EAITA,WAAW,EAOXA,WAAW,EAIZA,WAAW,EAITA,WAAW,EAOjBA,WAAW,EAQPA,WAAW,EAGjBA,WAAW,EAGLA,WAAW;AAAA;AAAAqX,EAAA,GAtG7BlV,gBAAgB;AA42DzB,eAAeA,gBAAgB;AAAC,IAAAkV,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}