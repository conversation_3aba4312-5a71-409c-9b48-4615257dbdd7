{"ast": null, "code": "import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\nimport { confirmResetPasswordReducer, coordinatorsListReducer, createCoordinatorReducer, createNewUserReducer, deleteUserReducer, detailCoordinatorReducer, getProfileUserReducer, historyListCoordinatorReducer, historyListLoggedReducer, logoutSavedUserReducer, resetPasswordReducer, updateCoordinatorReducer, updateLastLoginUserReducer, updatePasswordUserReducer, updateProfileUserReducer, userLoginReducer, usersListReducer } from \"./reducers/userReducers\";\nimport { clientListReducer, createNewClientReducer, deleteClientReducer, detailClientReducer, updateClientReducer } from \"./reducers/clientReducers\";\nimport { caseListCoordinatorReducer, caseListInsuranceReducer, caseListLoggedReducer, caseListMapReducer, caseListProviderReducer, caseListReducer, caseHistoryReducer, commentCaseListReducer, createNewCaseReducer, createNewCommentCaseReducer, deleteCaseReducer, deleteCommentCaseReducer, detailCaseReducer, duplicateCaseReducer, updateCaseAssignedReducer, updateCaseReducer, updateCaseStatusReducer } from \"./reducers/caseReducers\";\nimport { addNewProviderReducer, deleteProviderReducer, detailProviderReducer, providerListReducer, updateProviderReducer } from \"./reducers/providerReducers\";\nimport { addNewInsuranceReducer, deleteInsuranceReducer, detailInsuranceReducer, insuranceListReducer, updateInsuranceReducer } from \"./reducers/insurancereducers\";\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer\n\n  //\n});\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\") ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")) : null;\nconst initialState = {\n  userLogin: {\n    userInfo: userInfoFromStorage\n  }\n};\nconst middleware = [thunk];\nconst store = createStore(reducer, initialState, applyMiddleware(...middleware));\nexport default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "confirmResetPasswordReducer", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCoordinatorReducer", "createNewUserReducer", "deleteUserReducer", "detailCoordinatorReducer", "getProfileUserReducer", "historyListCoordinatorReducer", "historyListLoggedReducer", "logoutSavedUserReducer", "resetPasswordReducer", "updateCoordinatorReducer", "updateLastLoginUserReducer", "updatePasswordUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListCoordinatorReducer", "caseListInsuranceReducer", "caseListLoggedReducer", "caseListMapReducer", "caseListProviderReducer", "caseListReducer", "caseHistoryReducer", "commentCaseListReducer", "createNewCaseReducer", "createNewCommentCaseReducer", "deleteCaseReducer", "deleteCommentCaseReducer", "detailCaseReducer", "duplicateCaseReducer", "updateCaseAssignedReducer", "updateCaseReducer", "updateCaseStatusReducer", "addNewProviderReducer", "deleteProviderReducer", "detailProviderReducer", "providerListReducer", "updateProviderReducer", "addNewInsuranceReducer", "deleteInsuranceReducer", "detailInsuranceReducer", "insuranceListReducer", "updateInsuranceReducer", "reducer", "userLogin", "caseList", "caseListMap", "detailCase", "createNewCase", "deleteCase", "updateCase", "updateCaseStatus", "caseListCoordinator", "updateCaseAssigned", "caseListInsurance", "caseList<PERSON><PERSON><PERSON>", "caseListLogged", "duplicateCase", "caseHistory", "providerList", "detail<PERSON>rovider", "addNewProvider", "deleteProvider", "updateProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "insuranceList", "addNewInsurance", "deleteInsurance", "detailInsurance", "updateInsurance", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "updatePasswordUser", "updateLastLoginUser", "historyList<PERSON><PERSON>", "historyListCoordinator", "coordinatorsList", "createCoordinator", "detailCoordinator", "updateCoordinator", "commentCaseList", "createNewCommentCase", "deleteCommentCase", "logoutSavedUser", "resetPassword", "confirmResetPassword", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  confirmResetPasswordReducer,\n  coordinatorsListReducer,\n  createCoordinatorReducer,\n  createNewUserReducer,\n  deleteUserReducer,\n  detailCoordinatorReducer,\n  getProfileUserReducer,\n  historyListCoordinatorReducer,\n  historyListLoggedReducer,\n  logoutSavedUserReducer,\n  resetPasswordReducer,\n  updateCoordinatorReducer,\n  updateLastLoginUserReducer,\n  updatePasswordUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListCoordinatorReducer,\n  caseListInsuranceReducer,\n  caseListLoggedReducer,\n  caseListMapReducer,\n  caseListProviderReducer,\n  caseListReducer,\n  caseHistoryReducer,\n  commentCaseListReducer,\n  createNewCaseReducer,\n  createNewCommentCaseReducer,\n  deleteCaseReducer,\n  deleteCommentCaseReducer,\n  detailCaseReducer,\n  duplicateCaseReducer,\n  updateCaseAssignedReducer,\n  updateCaseReducer,\n  updateCaseStatusReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  deleteProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n  updateProviderReducer,\n} from \"./reducers/providerReducers\";\nimport {\n  addNewInsuranceReducer,\n  deleteInsuranceReducer,\n  detailInsuranceReducer,\n  insuranceListReducer,\n  updateInsuranceReducer,\n} from \"./reducers/insurancereducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  caseListMap: caseListMapReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  updateCaseStatus: updateCaseStatusReducer,\n  caseListCoordinator: caseListCoordinatorReducer,\n  updateCaseAssigned: updateCaseAssignedReducer,\n  caseListInsurance: caseListInsuranceReducer,\n  caseListProvider: caseListProviderReducer,\n  caseListLogged: caseListLoggedReducer,\n  duplicateCase: duplicateCaseReducer,\n  caseHistory: caseHistoryReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  deleteProvider: deleteProviderReducer,\n  updateProvider: updateProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n  insuranceList: insuranceListReducer,\n  addNewInsurance: addNewInsuranceReducer,\n  deleteInsurance: deleteInsuranceReducer,\n  detailInsurance: detailInsuranceReducer,\n  updateInsurance: updateInsuranceReducer,\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  updatePasswordUser: updatePasswordUserReducer,\n  updateLastLoginUser: updateLastLoginUserReducer,\n  historyListLogged: historyListLoggedReducer,\n  historyListCoordinator: historyListCoordinatorReducer,\n  //\n  coordinatorsList: coordinatorsListReducer,\n  createCoordinator: createCoordinatorReducer,\n  detailCoordinator: detailCoordinatorReducer,\n  updateCoordinator: updateCoordinatorReducer,\n  //\n  commentCaseList: commentCaseListReducer,\n  createNewCommentCase: createNewCommentCaseReducer,\n  deleteCommentCase: deleteCommentCaseReducer,\n  //\n  logoutSavedUser: logoutSavedUserReducer,\n  resetPassword: resetPasswordReducer,\n  confirmResetPassword: confirmResetPasswordReducer,\n\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  applyMiddleware(...middleware)\n);\n\nexport default store;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,OAAO;AACrE,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,SACEC,2BAA2B,EAC3BC,uBAAuB,EACvBC,wBAAwB,EACxBC,oBAAoB,EACpBC,iBAAiB,EACjBC,wBAAwB,EACxBC,qBAAqB,EACrBC,6BAA6B,EAC7BC,wBAAwB,EACxBC,sBAAsB,EACtBC,oBAAoB,EACpBC,wBAAwB,EACxBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAChC,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,QACd,2BAA2B;AAElC,SACEC,0BAA0B,EAC1BC,wBAAwB,EACxBC,qBAAqB,EACrBC,kBAAkB,EAClBC,uBAAuB,EACvBC,eAAe,EACfC,kBAAkB,EAClBC,sBAAsB,EACtBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,iBAAiB,EACjBC,wBAAwB,EACxBC,iBAAiB,EACjBC,oBAAoB,EACpBC,yBAAyB,EACzBC,iBAAiB,EACjBC,uBAAuB,QAClB,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,QAChB,6BAA6B;AACpC,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,sBAAsB,QACjB,8BAA8B;AAErC,MAAMC,OAAO,GAAGrD,eAAe,CAAC;EAC9BsD,SAAS,EAAEnC,gBAAgB;EAE3B;EACAoC,QAAQ,EAAExB,eAAe;EACzByB,WAAW,EAAE3B,kBAAkB;EAC/B4B,UAAU,EAAEnB,iBAAiB;EAC7BoB,aAAa,EAAExB,oBAAoB;EACnCyB,UAAU,EAAEvB,iBAAiB;EAC7BwB,UAAU,EAAEnB,iBAAiB;EAC7BoB,gBAAgB,EAAEnB,uBAAuB;EACzCoB,mBAAmB,EAAEpC,0BAA0B;EAC/CqC,kBAAkB,EAAEvB,yBAAyB;EAC7CwB,iBAAiB,EAAErC,wBAAwB;EAC3CsC,gBAAgB,EAAEnC,uBAAuB;EACzCoC,cAAc,EAAEtC,qBAAqB;EACrCuC,aAAa,EAAE5B,oBAAoB;EACnC6B,WAAW,EAAEpC,kBAAkB;EAC/B;EACAqC,YAAY,EAAEvB,mBAAmB;EACjCwB,cAAc,EAAEzB,qBAAqB;EACrC0B,cAAc,EAAE5B,qBAAqB;EACrC6B,cAAc,EAAE5B,qBAAqB;EACrC6B,cAAc,EAAE1B,qBAAqB;EACrC;EACA2B,UAAU,EAAErD,iBAAiB;EAC7BsD,eAAe,EAAErD,sBAAsB;EACvCsD,YAAY,EAAEpD,mBAAmB;EACjCqD,YAAY,EAAEpD,mBAAmB;EACjCqD,YAAY,EAAEvD,mBAAmB;EACjC;EACAwD,aAAa,EAAE5B,oBAAoB;EACnC6B,eAAe,EAAEhC,sBAAsB;EACvCiC,eAAe,EAAEhC,sBAAsB;EACvCiC,eAAe,EAAEhC,sBAAsB;EACvCiC,eAAe,EAAE/B,sBAAsB;EAEvC;EACAgC,SAAS,EAAEhE,gBAAgB;EAC3BiE,aAAa,EAAE9E,oBAAoB;EACnC+E,cAAc,EAAE5E,qBAAqB;EACrC6E,iBAAiB,EAAErE,wBAAwB;EAC3CsE,UAAU,EAAEhF,iBAAiB;EAC7BiF,kBAAkB,EAAExE,yBAAyB;EAC7CyE,mBAAmB,EAAE1E,0BAA0B;EAC/C2E,iBAAiB,EAAE/E,wBAAwB;EAC3CgF,sBAAsB,EAAEjF,6BAA6B;EACrD;EACAkF,gBAAgB,EAAExF,uBAAuB;EACzCyF,iBAAiB,EAAExF,wBAAwB;EAC3CyF,iBAAiB,EAAEtF,wBAAwB;EAC3CuF,iBAAiB,EAAEjF,wBAAwB;EAC3C;EACAkF,eAAe,EAAEhE,sBAAsB;EACvCiE,oBAAoB,EAAE/D,2BAA2B;EACjDgE,iBAAiB,EAAE9D,wBAAwB;EAC3C;EACA+D,eAAe,EAAEvF,sBAAsB;EACvCwF,aAAa,EAAEvF,oBAAoB;EACnCwF,oBAAoB,EAAElG;;EAEtB;AACF,CAAC,CAAC;AAEF,MAAMmG,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,GAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GACtD,IAAI;AAER,MAAMG,YAAY,GAAG;EACnBtD,SAAS,EAAE;IAAEuD,QAAQ,EAAEN;EAAoB;AAC7C,CAAC;AAED,MAAMO,UAAU,GAAG,CAAC5G,KAAK,CAAC;AAE1B,MAAM6G,KAAK,GAAGhH,WAAW,CACvBsD,OAAO,EACPuD,YAAY,EACZ3G,eAAe,CAAC,GAAG6G,UAAU,CAC/B,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}